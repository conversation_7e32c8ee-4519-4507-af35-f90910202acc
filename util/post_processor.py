import re 
from util.tag_checker import (
    sanitize_translation,
    sanitize_tags,
    sanitize_translation_strict,
    detect_cross,
)
from util.jiaobiao import SUPERSCRIPT_MAP, SUBSCRIPT_MAP
# from util.name_util import normalize_names
from logger.logger import biz_logger

# 拼出所有角标字符
_SCRIPT_CHARS = ''.join(SUPERSCRIPT_MAP.values()) + ''.join(SUBSCRIPT_MAP.values())

# 月份匹配，支持简写和全写
_MONTHS = (
    r'Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|'
    r'May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:t(?:ember)?)\?|'
    r'Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?'
)
# 匹配 “日 月 年” 格式
_DATE_RE = re.compile(
    rf'\b([0-3]?\d)\s+({_MONTHS})\s+(\d{{4}})\b',
    flags=re.IGNORECASE
)

def reorder_date_substrings(text: str) -> str:
    """
    把所有 “数字 月份 年份” → “月份 数字 年份”。
    例如 “30 Sep 2023” → “Sep 30 2023”，
          “01 April 2022” → “April 01 2022”
    """
    return _DATE_RE.sub(lambda m: f"{m.group(2)} {m.group(1)} {m.group(3)}", text)


# 删除译文中的多余空白符
def delete_extra_spaces(text):
    # 只替换连续的空格、制表符，不替换换行
    # biz_logger.info(f"删除多余空格 原文: {text}")
    result = re.sub(r'[ \t]+', ' ', text).strip()
    # biz_logger.info(f"删除多余空格 结果: {result}")
    return result

# 转义换行符
def escape_newlines(text):
    # biz_logger.info(f"转义换行符 原文: {text}") 
    result = text.replace(' \\n ', '\n').replace(' \\n', '\n').replace('\\n ', '\n').replace('\\n', '\n').strip()
    # biz_logger.info(f"转义换行符 结果: {result}")
    return result


# 删掉角标前面的空格
def delete_space_before_script(text):
    # return re.sub(r'\s+([₀-₉ₐ-ₒₓₔₕₖₗₘₙₚₛₜ₦₧₩₪₫₭₮₯₰₱₲₳₴₵₶₷₸₹₺₻₼₽₾₿])', r'\1', text)
    # 新增：把角标前面的空格去掉
    text = re.sub(r'\s+(?=[%s])' % re.escape(_SCRIPT_CHARS), "", text)
    return text

# 翻译后处理函数，包含各种后处理操作
def post_process(src_list, text_list, src_lang, tgt_lang):
    """
    对翻译结果进行后处理
    
    Args:
        text_list: 翻译结果列表
        src_lang: 源语言
        tgt_lang: 目标语言
        
    Returns:
        处理后的翻译结果列表
    """
    biz_logger.info(f"开始后处理")
    processed_list = []
    for idx, (src, text) in enumerate(zip(src_list, text_list)):
        # 转义换行符
        text = escape_newlines(text)
        # 删除多余空白符
        # text = delete_extra_spaces(text)
        # 先把 “日 月 年” 重写成 “月 日 年”
        text = reorder_date_substrings(text)
        
            
        # 删掉角标前面的空格
        text = delete_space_before_script(text)
        
        # ---- tag_checker 相关清理 ----
        # 1) 先用最宽松的：删除整体不平衡的 <g> 标签
        text = sanitize_translation(text)
        # biz_logger.debug(f"  [{idx}] 删除整体不平衡的<g>标签后: {text}")
        # 2) 再去重自闭合标签（重复 ID 保留第一条）
        text = sanitize_tags(text)
        # biz_logger.debug(f"  [{idx}] 去重自闭合标签后: {text}")

        # # 如果你要更严格的“只保留和源文对得上的 <g id=…>”：
        # text = sanitize_translation_strict(src, text)

        # 3) 可选：打印一下哪些标签不对照（方便调试）
        diffs = detect_cross(src, text)
        if diffs:
            biz_logger.warning(f"[tag diff] src={src!r}\n", "\n".join(diffs))

        # === 新增：姓名规范化 ===
        # text = normalize_names(src, text)

        processed_list.append(text)
    return processed_list