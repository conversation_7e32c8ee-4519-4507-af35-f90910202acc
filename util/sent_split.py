import re
from configurer.config_reader import get_split_conf
from util.model_loader import ModelLoader
from logger.logger import biz_logger

# ----------------- 运行时依赖 -----------------
spm_model = ModelLoader().load_spm_model()
space_code = '▁'

# 分割配置常量
SPLIT_THRESHOLD = 8      # 原文中 <g> 总数 > 1 时走 tag-aware
MAX_TAGS_PER_CHUNK = 8   # 每行至多 1 对 <g>

# <g> 标签相关正则
TAG_RE = re.compile(
    r'(<(?:x|bx|ex|mrk)\b[^>]*?/>'
    r'|<g\b[^>]*?>'
    r'|</g\b[^>]*?>)',
    flags=re.IGNORECASE
)

def count_tags(txt: str) -> int:
    """返回 txt 中完整 <g> pair 数 + 自闭合标签数。"""
    self_cnt = pair_open = pair_close = 0
    for m in TAG_RE.finditer(txt):
        tag = m.group(1).lower()
        if tag.endswith('/>'):
            self_cnt += 1
        elif tag.startswith('<g '):
            pair_open += 1
        else:
            pair_close += 1
    return self_cnt + min(pair_open, pair_close)

def find_safe_points(txt: str) -> list[int]:
    safe, depth, pos = set(), 0, 0
    for m in TAG_RE.finditer(txt):
        # 处理 m 之前的纯文本中的句末标点
        for i in range(pos, m.start()):
            if txt[i] in '.!?;；。！？':
                # 跳过数字.数字
                if (txt[i]=='.' and i>0 and txt[i-1].isdigit()
                        and i+1<len(txt) and txt[i+1].isdigit()):
                    continue
                nxt = txt[i+1] if i+1<len(txt) else ''
                if depth==0 and (nxt.isspace() or nxt=='<' or nxt==''):
                    safe.add(i+1)
        tag = m.group(1).lower()
        # 自闭合：顶层即可断
        if tag.endswith('/>') and depth==0:
            safe.add(m.end())
        # 开 <g>：顶层记录断点，深度+1
        elif tag.startswith('<g '):
            if depth==0:
                safe.add(m.start())
            depth += 1
        # 闭 </g>：深度-1，顶层记录断点
        elif tag.startswith('</g'):
            depth = max(depth-1, 0)
            if depth==0:
                safe.add(m.start())
        pos = m.end()
    # 处理结尾纯文本
    for i in range(pos, len(txt)):
        if txt[i] in '.!?;；。！？' and depth==0:
            nxt = txt[i+1] if i+1<len(txt) else ''
            if nxt.isspace() or nxt=='<' or nxt=='':
                safe.add(i+1)
    return sorted(safe)

def tag_aware_split(txt: str, max_tok: int, min_tok: int) -> list[str]:
    pts = find_safe_points(txt)
    if not pts:
        return [txt]
    chunks, start, last_safe = [], 0, 0
    for pt in pts:
        seg = txt[start:pt]
        tok = spm_model.encode(seg, out_type=str, enable_sampling=False) if spm_model else []
        pair_cnt = count_tags(seg)
        open_cnt = len(re.findall(r'<g\b[^>/]*?>', seg, flags=re.I))
        close_cnt = len(re.findall(r'</g\b[^>]*?>', seg, flags=re.I))
        unclosed = open_cnt - close_cnt
        need_cut = (pair_cnt >= MAX_TAGS_PER_CHUNK) and unclosed==0 and pt!=start
        if need_cut:
            cut = last_safe if last_safe>start else pt
            # 把紧随其后的标点一起吃掉
            if cut<len(txt) and txt[cut] in ',.!?;；。！？':
                cut += 1
            chunk = txt[start:cut].strip()
            if chunk:
                chunks.append(chunk)
            start = cut
            last_safe = cut
        else:
            if unclosed==0:
                last_safe = pt
    tail = txt[start:].strip()
    if tail:
        chunks.append(tail)
    # 回合并无 <g> 的纯文本块
    fixed = []
    for c in chunks:
        if count_tags(c)==0 and fixed:
            fixed[-1] = f"{fixed[-1]} {c}".rstrip()
        else:
            fixed.append(c)
    return fixed

def split_sentences(text: str) -> list[str]:
    """
    基于中英文常见句末标点直接分句，保留标点。
    """
    biz_logger.info(f"按标点分句，文本长度：{len(text)}")
    txt = text.strip()
    if not txt:
        return []
    split_pattern = r'[\r\n]+|(?<=[。！？])|(?<=[.!?])\s+(?=[A-Z])'
    parts = re.split(split_pattern, txt)
    return [p for p in (p.strip() for p in parts) if p]

def forced_split(text: str, max_tokens: int, min_tokens: int) -> list[str]:
    """
    若片段超长，优先在标点处按 max_tokens 切分，否则强制切分。
    """
    if not spm_model:
        biz_logger.warning("SentencePiece 未加载，返回原文")
        return [text]
    tokens = spm_model.encode(text, out_type=str, enable_sampling=False)
    chunks, cur, last_p = [], [], -1
    for i, tk in enumerate(tokens):
        cur.append(tk)
        if tk in ('.', '?', '!', ';', '。', '！', '？', '；'):
            last_p = i
        if len(cur) >= max_tokens:
            if 0 <= last_p < len(cur) and (last_p+1) >= min_tokens:
                cut = last_p + 1
            else:
                cut = len(cur)
            piece, cur = cur[:cut], cur[cut:]
            text_piece = "".join(piece).replace(space_code, " ").strip()
            chunks.append(text_piece)
            last_p = -1
    if cur:
        text_piece = "".join(cur).replace(space_code, " ").strip()
        chunks.append(text_piece)
    return [c for c in chunks if c]

def split(text: str, lang: str) -> list[tuple[str, None]]:
    """
    主入口：结合 tag-aware 和基于标点的分句逻辑
    """
    biz_logger.info(f"开始分句处理，语言：{lang}，文本长度：{len(text)} 字")
    conf = get_split_conf(lang)
    max_len = conf.get('max_length', 100)
    min_len = conf.get('min_length', 15)

    # 1) 若含 <g> 且数量超过阈值，走 tag-aware 分段
    if count_tags(text) > SPLIT_THRESHOLD:
        biz_logger.info('走 tag-aware 分段')
        return [(seg, None) for seg in tag_aware_split(text, max_len, min_len)]

    # 2) 整段 token 数判断
    tokens = spm_model.encode(text, out_type=str, enable_sampling=False) if spm_model else []
    if len(tokens) <= max_len:
        biz_logger.info(f"整体 {len(tokens)} tokens ≤ 最大 {max_len}，不做分句")
        return [(text, None)]

    # 3) 按标点分句
    sents = split_sentences(text)
    biz_logger.info(f"标点分句得到 {len(sents)} 条子句")

    # 4) 对每句超长则 forced_split
    result = []
    for idx, sent in enumerate(sents, 1):
        toks = spm_model.encode(sent, out_type=str, enable_sampling=False) if spm_model else []
        if len(toks) > max_len:
            biz_logger.info(f"第 {idx} 句 ({len(toks)} tokens) 超长 → 强制切分")
            result.extend(forced_split(sent, max_len, min_len))
        else:
            result.append(sent)

    biz_logger.info(f"最终得到 {len(result)} 条输入片段")
    return [(seg, None) for seg in result]

if __name__ == "__main__":
    # 简单自测示例
    txt = "这是一个测试。This is a test! 测试继续。"
    for seg, _ in split(txt, 'zh'):
        print(f"[{len(seg)} chars] {seg}")
