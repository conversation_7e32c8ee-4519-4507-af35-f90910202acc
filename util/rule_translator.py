# util/rule_translator.py
# -*- coding: utf-8 -*-
import re
import logging
from typing import Optional
from logger.logger import biz_logger



class RuleTranslator:
    """
    整句规则翻译器，支持 en→zh 与 zh→en 两种语向。
    translate(text, src_lang, tgt_lang)：
      - 如果整句匹配对应语向的规则，返回转换后的字符串；
      - 否则返回 None，由模型接管翻译。
    """
    def __init__(self):
        biz_logger.info("初始化规则翻译器")
        # —— 通用映射表 —— #
        self.month_map = {
            'jan': '1月', 'january': '1月',
            'feb': '2月', 'february': '2月',
            'mar': '3月', 'march': '3月',
            'apr': '4月', 'april': '4月',
            'may': '5月',
            'jun': '6月', 'june': '6月',
            'jul': '7月', 'july': '7月',
            'aug': '8月', 'august': '8月',
            'sep': '9月', 'september': '9月',
            'oct': '10月', 'october': '10月',
            'nov': '11月', 'november': '11月',
            'dec': '12月', 'december': '12月',
        }
        self.weekday_map = {
            'monday': '星期一', 'mon': '周一',
            'tuesday': '星期二', 'tue': '周二',
            'wednesday': '星期三', 'wed': '周三',
            'thursday': '星期四', 'thu': '周四',
            'friday': '星期五', 'fri': '周五',
            'saturday': '星期六', 'sat': '周六',
            'sunday': '星期日', 'sun': '周日',
        }
        self.abbrev_map = {
            'e.g.': '例如', 'i.e.': '即', 'etc.': '等等', 'vs.': '对比',
            'cf.': '参见', 'w.r.t.': '关于',
        }
        self.professional_map = {
            'dna': '脱氧核糖核酸', 'rna': '核糖核酸', 'pcr': '聚合酶链式反应',
        }
        self.org_map = {
            'un': '联合国', 'who': '世界卫生组织', 'usa': '美国',
            'eu': '欧盟', 'cfr': '美国联邦法规',
        }
        self.chemical_map = {
            'h2o': 'H₂O', 'nacl': 'NaCl',
        }
        # en→zh 单位/货币
        self.unit_map = {
            'kg': '千克', 'g': '克', 'cm': '厘米', 'mm': '毫米',
            'km': '千米', 'm': '米', 'L': '升', 'mL': '毫升',
            'ml': '毫升', 'mg': '毫克', '°C': '°C', '°F': '℉'
        }
        self.currency_map = {
            'usd': '美元', 'eur': '欧元', 'cny': '人民币',
            'jpy': '日元', 'gbp': '英镑',
        }

        # —— en→zh 整句规则 —— #
        self.patterns_en2zh = {
            'date5': re.compile(r'^(?P<day>\d{1,2}) (?P<month_name>[A-Za-z]{3,9}) (?P<year>\d{4})$'),
            'date1': re.compile(r'^(?P<year>\d{4})[-/](?P<month>0[1-9]|1[0-2])[-/](?P<day>0[1-9]|[12]\d|3[01])$'),
            'date2': re.compile(r'^(?P<day>\d{1,2})[-/](?P<month>0[1-9]|1[0-2])[-/](?P<year>\d{2,4})$'),
            'date3': re.compile(r'^(?P<month>\d{1,2})/(?P<day>\d{1,2})$'),
            'date4': re.compile(r'^(?P<day>\d{1,2})/(?P<month>\d{1,2})$'),
            'weekday': re.compile(r'^(?P<wd>Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday|Mon|Tue|Wed|Thu|Fri|Sat|Sun)$', re.IGNORECASE),
            'time': re.compile(r'^(?P<tp>\d{1,2}:\d{2}(?::\d{2})?)(?P<ap> ?[AP]M)?$', re.IGNORECASE),
            'percent': re.compile(r'^\d+(\.\d+)?%$'),
            'fraction': re.compile(r'^\d+/\d+$'),
            'ordinal': re.compile(r'^\d+(st|nd|rd|th)$', re.IGNORECASE),
            'ordinal_word': re.compile(r'^(first|second|third|fourth|fifth|sixth|seventh|eighth|ninth|tenth)$', re.IGNORECASE),
            'abbr': re.compile(r'^(e\.g\.|i\.e\.|etc\.|vs\.|cf\.|w\.r\.t\.)$', re.IGNORECASE),
            'professional': re.compile(r'^(dna|rna|pcr)$', re.IGNORECASE),
            'organization': re.compile(r'^(un|who|usa|eu|cfr)$', re.IGNORECASE),
            'chemical': re.compile(r'^(h2o|nacl)$', re.IGNORECASE),
            'unit': re.compile(r'^(?P<num>-?\d+(\.\d+)?)(?P<unit>kg|g|cm|mm|km|m|L|mL|ml|mg|°C|°F)$'),
            'currency': re.compile(r'^(\$|USD\s?|EUR\s?|CNY\s?|JPY\s?|GBP\s?)?\d+(\.\d+)?$', re.IGNORECASE),
            'roman': re.compile(r'^[IVXLCDM]+$', re.IGNORECASE),
            'url': re.compile(r'^https?://[\w\.-]+(?:/[\w\./-]*)?$'),
            'email': re.compile(r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
            'code': re.compile(r'^`[^`]+`$'),
        }

        # —— zh→en 整句规则 —— #
        # 中文日期 YYYY-MM-DD 或 YYYY/MM/DD
        self.month_cn2en = {
            '1': 'January', '2': 'February', '3': 'March', '4': 'April',
            '5': 'May', '6': 'June', '7': 'July', '8': 'August',
            '9': 'September', '10': 'October', '11': 'November', '12': 'December',
        }
        self.patterns_zh2en = {
            'date_iso': re.compile(
                r'^(?P<year>\d{4})[-/](?P<month>0?[1-9]|1[0-2])[-/](?P<day>0?[1-9]|[12]\d|3[01])$'
            ),
            'time24': re.compile(
                r'^(?P<h>[01]?\d|2[0-3]):(?P<m>[0-5]\d)(?::(?P<s>[0-5]\d))?$'
            ),
            'percent': re.compile(r'^\d+(\.\d+)?%$'),
            'fraction': re.compile(r'^\d+/\d+$'),
        }
        biz_logger.info("规则翻译器初始化完成")

    def translate(self, text: str, src_lang: str, tgt_lang: str) -> Optional[str]:
        t = text.strip()
        biz_logger.debug(f"尝试规则翻译：{t!r}，源语言：{src_lang}，目标语言：{tgt_lang}")
        # 英→中
        if src_lang.lower().startswith('en') and tgt_lang.lower().startswith('zh'):
            biz_logger.debug("应用英译中规则")
            for name, pat in self.patterns_en2zh.items():
                m = pat.fullmatch(t)
                if not m:
                    continue
                biz_logger.debug(f"匹配到英译中规则：{name}")
                handler = getattr(self, f'_handle_en2zh_{name}', None)
                result = handler(m) if handler else t
                biz_logger.debug(f"规则翻译结果：{result!r}")
                return result
        # 中→英
        if src_lang.lower().startswith('zh') and tgt_lang.lower().startswith('en'):
            biz_logger.debug("应用中译英规则")
            for name, pat in self.patterns_zh2en.items():
                m = pat.fullmatch(t)
                if not m:
                    continue
                biz_logger.debug(f"匹配到中译英规则：{name}")
                handler = getattr(self, f'_handle_zh2en_{name}', None)
                result = handler(m) if handler else t
                biz_logger.debug(f"规则翻译结果：{result!r}")
                return result
        biz_logger.debug("未匹配到规则，返回None")
        return None

    # —— en→zh 处理函数 —— #
    def _handle_en2zh_date5(self, m):
        d = int(m.group('day'))
        mon = m.group('month_name').lower()
        return f"{m.group('year')}年{self.month_map.get(mon, mon)}{d}日"

    def _handle_en2zh_date1(self, m):
        return f"{m.group('year')}年{int(m.group('month'))}月{int(m.group('day'))}日"

    def _handle_en2zh_date2(self, m):
        y = m.group('year')
        if len(y) == 2:
            y = '20' + y
        return f"{y}年{int(m.group('month'))}月{int(m.group('day'))}日"

    def _handle_en2zh_date3(self, m):
        return f"{int(m.group('month'))}月{int(m.group('day'))}日"

    def _handle_en2zh_date4(self, m):
        return f"{int(m.group('day'))}月{int(m.group('month'))}日"

    def _handle_en2zh_weekday(self, m):
        return self.weekday_map[m.group('wd').lower()]

    def _handle_en2zh_time(self, m):
        tp = m.group('tp').upper()
        ap = (m.group('ap') or '').strip().upper()
        if ap == 'AM':
            return f"上午{tp}"
        if ap == 'PM':
            return f"下午{tp}"
        return tp

    def _handle_en2zh_percent(self, m):
        return m.group(0)

    def _handle_en2zh_fraction(self, m):
        return m.group(0)

    def _handle_en2zh_ordinal(self, m):
        num = re.match(r'(\d+)', m.group(0)).group(1)
        return f"第{num}"

    def _handle_en2zh_ordinal_word(self, m):
        mp = {
            'first': '第1', 'second': '第2', 'third': '第3',
            'fourth': '第4', 'fifth': '第5', 'sixth': '第6',
            'seventh': '第7', 'eighth': '第8', 'ninth': '第9', 'tenth': '第10',
        }
        return mp[m.group(1).lower()]

    def _handle_en2zh_abbr(self, m):
        return self.abbrev_map[m.group(1).lower()]

    def _handle_en2zh_professional(self, m):
        return self.professional_map[m.group(0).lower()]

    def _handle_en2zh_organization(self, m):
        return self.org_map[m.group(0).lower()]

    def _handle_en2zh_chemical(self, m):
        return self.chemical_map[m.group(0).lower()]

    def _handle_en2zh_unit(self, m):
        num = m.group('num')
        unit = m.group('unit')
        return f"{num}{self.unit_map[unit]}"

    def _handle_en2zh_currency(self, m):
        txt = m.group(0).strip()
        num = re.sub(r'[\$\sA-Z]+', '', txt)
        up = txt.upper()
        if txt.startswith('$') or up.startswith('USD'):
            return f"{num}美元"
        if up.startswith('EUR'):
            return f"{num}欧元"
        if up.startswith('CNY'):
            return f"{num}人民币"
        if up.startswith('JPY'):
            return f"{num}日元"
        if up.startswith('GBP'):
            return f"{num}英镑"
        return txt

    def _handle_en2zh_roman(self, m):
        vals = {'I':1,'V':5,'X':10,'L':50,'C':100,'D':500,'M':1000}
        total = 0
        prev = 0
        for ch in reversed(m.group(0).upper()):
            v = vals[ch]
            total += v if v >= prev else -v
            prev = v
        return f"第{total}"

    def _handle_en2zh_url(self, m):
        return m.group(0)

    def _handle_en2zh_email(self, m):
        return m.group(0)

    def _handle_en2zh_code(self, m):
        return m.group(0)

    # —— zh→en 处理函数 —— #
    def _handle_zh2en_date_iso(self, m):
        y = m.group('year')
        mo = str(int(m.group('month')))
        d = str(int(m.group('day')))
        return f"{self.month_cn2en.get(mo, mo)} {d}, {y}"

    def _handle_zh2en_time24(self, m):
        h = int(m.group('h'))
        mm = m.group('m')
        ss = m.group('s')
        if ss:
            return f"{h:02d}:{mm}:{ss}"
        return f"{h:02d}:{mm}"

    def _handle_zh2en_percent(self, m):
        return m.group(0)

    def _handle_zh2en_fraction(self, m):
        return m.group(0)


# 最全面的zh2en测试样例
if __name__ == '__main__':
    rt = RuleTranslator()
    tests = [
        # date_iso
        ("2024-06-05", "zh", "en"),
        ("2024/6/5", "zh", "en"),
        ("2024-1-1", "zh", "en"),
        ("2024/12/31", "zh", "en"),
        ("1999-12-31", "zh", "en"),
        ("2000/01/01", "zh", "en"),
        # time24
        ("15:30", "zh", "en"),
        ("09:05", "zh", "en"),
        ("0:00", "zh", "en"),
        ("23:59", "zh", "en"),
        ("12:34:56", "zh", "en"),
        ("1:2:3", "zh", "en"),
        ("00:00:00", "zh", "en"),
        # percent
        ("50%", "zh", "en"),
        ("100%", "zh", "en"),
        ("0.5%", "zh", "en"),
        ("99.99%", "zh", "en"),
        # fraction
        ("3/4", "zh", "en"),
        ("1/2", "zh", "en"),
        ("123/456", "zh", "en"),
        ("7/8", "zh", "en"),
        # negative test (should return None)
        ("2024年6月5日", "zh", "en"),
        ("15点30分", "zh", "en"),
        ("百分之五十", "zh", "en"),
        ("三分之二", "zh", "en"),
        ("hello world", "zh", "en"),
        ("", "zh", "en"),
        ("2024-13-01", "zh", "en"),  # invalid month
        ("2024-06-32", "zh", "en"),  # invalid day
        ("24:00", "zh", "en"),       # invalid hour
        ("12:60", "zh", "en"),       # invalid minute
        ("1:2:60", "zh", "en"),      # invalid second
    ]
    for txt, sl, tl in tests:
        out = rt.translate(txt, sl, tl)
        print(f"{sl}->{tl} | {txt!r} -> {out!r}")

"""
python -m util.rule_translator
"""