import json
import os
import time
from typing import Dict

import nacos

from configurer.singleton import singleton
from logger.logger import app_logger

init_configs = {
    "mt_user_whitelist": "{}",
    "punctuation_mapping": "{}",
    "alicloud_middleware_config": "{}",
    "yy_mt_config": "{}",
    "no_translatable_elements": "{}",
    "non_translation_exact_strings": "{}",
    "mt_sentence_spliter": """
    (?:               # Group for the following options:
    (?<=[,.!?])\s+    # Either an end of sentence punctuation in English followed by whitespace,
    | (?<=[.!?]["”])  # or end of sentence punctuation in English followed by a quote and then whitespace.
    | (?<=[、，。！？])    # Or an end of sentence punctuation in Chinese (no need for whitespace check).
    )
    (?<!  Mr\.   )    # Ignore "Mr." as an ending.
    (?<!  Mrs\.  )    # Ignore "Mrs." as an ending.
    (?<!  Ms\.   )    # Ignore "Ms." as an ending.
    (?<!  Jr\.   )    # Ignore "Jr." as an ending.
    (?<!  Dr\.\s  )   # Ignore "Dr." as an ending, with a space after the period.
    (?<!  Prof\. )    # Ignore "Prof." as an ending.
    (?<!  Sr\.   )    # Ignore "Sr." as an ending.
    """,
    "sentence_endings": """
    (?:               # Group for the following options:
    (?<=[.!?])\s+   # Either an end of sentence punctuation in English followed by whitespace,
    | (?<=[.!?]["”])  # or end of sentence punctuation in English followed by a quote and then whitespace.
    | (?<=[。！？])    # Or an end of sentence punctuation in Chinese (no need for whitespace check).
    )
    (?<!  Mr\.   )    # Ignore "Mr." as an ending.
    (?<!  Mrs\.  )    # Ignore "Mrs." as an ending.
    (?<!  Ms\.   )    # Ignore "Ms." as an ending.
    (?<!  Jr\.   )    # Ignore "Jr." as an ending.
    (?<!  Dr\.\s  )   # Ignore "Dr." as an ending, with a space after the period.
    (?<!  Prof\. )    # Ignore "Prof." as an ending.
    (?<!  Sr\.   )    # Ignore "Sr." as an ending.
    (?<!  Inc\.   )    # Ignore "Inc." as an ending.
    """
}

config: Dict[str, any] = {}


def watcher(args):
    data_id = args['data_id']
    content = args['content']
    try:
        data = json.loads(content)
        config[data_id] = data
        app_logger.info(f"配置变更：{data_id} => {content}")
    except Exception as e:
        app_logger.error(f"配置解析异常发生错误:{data_id} => {content}, {e}")
        app_logger.error(f"详细信息: {e.args}")


@singleton
class Nacos:
    def __init__(self):
        self.group_id = ""
        self.client = nacos.NacosClient(
            server_addresses=os.environ.get('NACOS_SERVER', 'mse-450596b0-p.nacos-ans.mse.aliyuncs.com:8848'),
            namespace=os.environ.get('NACOS_NAMESPACE', 'yy-prod'),
            ak="LTAI5tSMtzYQ5GVPCm8njDYp",
            sk="******************************"
        )

        for data_id, default_content in init_configs.items():
            data = self.client.get_config(data_id, self.group_id)
            if data:
                try:
                    config[data_id] = json.loads(data)
                except:
                    config[data_id] = data
            else:
                try:
                    config[data_id] = json.loads(default_content)
                except:
                    config[data_id] = default_content
            self.client.add_config_watcher(data_id, self.group_id, watcher)


if __name__ == '__main__':
    """
    - fastgpt_revision_ak: <unk> 处理工作流的 apikey
    - split_rule：断句规则
    - punctuations：强制断句标点符号
    - min_length：断句的最小字符数，小于该长度的句子，不单独断句
    - max_length：超过该长度的句子，使用punctuations 标点服务强制断句
    - split_length：断句的最小长度，小于该长度的段落不断句
    - term_interv.placeholder: 术语干预占位符
    """
    mse = Nacos()
    print(f'---> mt_user_whitelist: {config.get("mt_user_whitelist")}')
    print(f'---> punctuation_mapping: {config.get("punctuation_mapping")}')
    print(f'---> alicloud_middleware_config: {config.get("alicloud_middleware_config")}')
    print(f'---> yy_mt_config: {config.get("yy_mt_config")}')
    print(f'---> no_translatable_elements: {config.get("no_translatable_elements")}')
    print(f'---> non_translation_exact_strings: {config.get("non_translation_exact_strings")}')
    print(f'---> sentence_spliter: {config.get("sentence_spliter")}')
    print(f'---> sentence_endings: {config.get("sentence_endings")}')
    # time.sleep(1000)

    """
    python -m configurer.yy_nacos
    """
