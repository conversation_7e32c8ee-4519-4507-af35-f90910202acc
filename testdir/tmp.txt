<g id="1" type="tag1" text="tag property=&quot;x-bold;color:231F20;&quot;"> <g id="1" type="tag1" text="tag property=&quot;x-bold;color:231F20;&quot;"> Abstract: </g type="tag1" text="/tag"> </g type="tag1" text="/tag"> The study of the spatial diffusion and geographical mode of COVID- 19 is of great significance for the rational allocation of health resources, the management and response of public health emergencies, and the improvement of public health system in the future. Based on multiple spatio- temporal scale, this paper studied the spatial spreading process of COVID- 19 between cities and its evolution characteristics in China, and then explored its influencing factors. The results are shown in the following: the inter-city spreading process of COVID-19 in China mainly experienced six stages, namely, stage I: diffusion in Wuhan, stage II: rapid multi- point diffusion in space, stage III: rapid increase of confirmed cases, stage IV : gradual decrease of new confirmed cases, stage V: the epidemic under control, and stage VI: cases imported from overseas. In the context of globalization and open regional system, the social and economic development of regions are closely related to each other. With the development of fast and convenient high- speed railway network, the spatial characteristic of population migration shows a cross- regional and hierarchical pattern, and forms a certain spatial cascade structure along the transport corridor. Accordingly, the spatial spread of COVID-19 mainly shows the characteristics of adjacent diffusion, relocation diffusion, hierarchical diffusion, and corridor diffusion. The study found that geographical proximity, population migration and population size, traffic network, epidemic prevention and control measures have significant influence on the spatial diffusion process of COVID-19. Among different modes of transportation, airplanes play a greater role than others in the early stage of the epidemic. In addition, the population flow during the Spring Festival had a certain impact on the spread of the epidemic. In conclusion, to some extent, the spatial spread process and pattern of COVID-19 epidemic reflects the spatial organization pattern of social and economic activities under the "space of flows" network, which is closely related to the geographical proximity, the social and economic linkages between regions, and the spatial an temporal patterns of human activities. From the perspective of geography, this paper analyzed the inter- city spread pattern of COVID- 19 epidemic and provided some implications for prevention and control measures against the epidemic in other countries, and also offered some suggestions for China to deal with public health emergency risks in the future.
<g id="1" type="tag1" text="tag property=&quot;x-bold;color:231F20;&quot;"> <g id="1" type="tag1" text="tag property=&quot;x-bold;color:231F20;&quot;"> Keywords: </g type="tag1" text="/tag"> </g type="tag1" text="/tag"> epidemic; geographical proximity; diffusion; population flow; transport
stage 7
Wang Jiaoe et al: Study on Spatial Diffusion Process and Model of Covid-19 Epidemic
1451
geography study
39 rolls
1452
geography study
39 rolls
(base) [root@VM-0-8-centos mt_server]# vi server.py 
(base) [root@VM-0-8-centos mt_server]# ll
总用量 2476
-rw-r--r-- 1 <USER> <GROUP>    4349 1月  29 17:35 client.py
-rw-r--r-- 1 <USER> <GROUP>    1898 1月  25 04:55 curl_batch.sh
-rw-r--r-- 1 <USER> <GROUP>    9896 1月  29 16:41 htmlparser.py
-rw-r--r-- 1 <USER> <GROUP>     431 1月  29 17:00 langident.py
-rw-r--r-- 1 <USER> <GROUP>    7488 1月  29 10:58 log
drwxrwxr-x 2 <USER> <GROUP>    4096 1月  23 02:45 model
-rw-r--r-- 1 <USER> <GROUP>     747 1月  29 10:14 post_demo.py
drwxr-xr-x 2 <USER> <GROUP>    4096 1月  29 17:50 __pycache__
-rw-r--r-- 1 <USER> <GROUP>    7522 1月  29 16:43 sent_split.py
-rw-r--r-- 1 <USER> <GROUP>    3160 1月  29 17:53 server.py
-rw-r--r-- 1 <USER> <GROUP>   11809 1月  29 17:57 sever.log
-rw-r--r-- 1 <USER> <GROUP> 2423393 10月 16 2020 spm.128k.model
-rw-r--r-- 1 <USER> <GROUP>      36 1月  29 11:02 start_server.sh
-rw-r--r-- 1 <USER> <GROUP>      46 1月  29 11:01 stop_server.sh
-rw-r--r-- 1 <USER> <GROUP>    8784 1月  25 03:22 thread_sync_pools.py
-rw-r--r-- 1 <USER> <GROUP>     568 1月  25 04:57 tmp.txt
-rw-r--r-- 1 <USER> <GROUP>    8242 1月  29 17:50 translate_api.py
(base) [root@VM-0-8-centos mt_server]# ll
总用量 2476
-rw-r--r-- 1 <USER> <GROUP>    4349 1月  29 17:35 client.py
-rw-r--r-- 1 <USER> <GROUP>    1898 1月  25 04:55 curl_batch.sh
-rw-r--r-- 1 <USER> <GROUP>    9896 1月  29 16:41 htmlparser.py
-rw-r--r-- 1 <USER> <GROUP>     431 1月  29 17:00 langident.py
-rw-r--r-- 1 <USER> <GROUP>    7488 1月  29 10:58 log
drwxrwxr-x 2 <USER> <GROUP>    4096 1月  23 02:45 model
-rw-r--r-- 1 <USER> <GROUP>     747 1月  29 10:14 post_demo.py
drwxr-xr-x 2 <USER> <GROUP>    4096 1月  29 17:50 __pycache__
-rw-r--r-- 1 <USER> <GROUP>    7522 1月  29 16:43 sent_split.py
-rw-r--r-- 1 <USER> <GROUP>    3160 1月  29 17:53 server.py
-rw-r--r-- 1 <USER> <GROUP>   11809 1月  29 17:57 sever.log
-rw-r--r-- 1 <USER> <GROUP> 2423393 10月 16 2020 spm.128k.model
-rw-r--r-- 1 <USER> <GROUP>      36 1月  29 11:02 start_server.sh
-rw-r--r-- 1 <USER> <GROUP>      46 1月  29 11:01 stop_server.sh
-rw-r--r-- 1 <USER> <GROUP>    8784 1月  25 03:22 thread_sync_pools.py
-rw-r--r-- 1 <USER> <GROUP>     568 1月  25 04:57 tmp.txt
-rw-r--r-- 1 <USER> <GROUP>    8242 1月  29 17:50 translate_api.py
(base) [root@VM-0-8-centos mt_server]# ll
总用量 2476
-rw-r--r-- 1 <USER> <GROUP>    4349 1月  29 17:35 client.py
-rw-r--r-- 1 <USER> <GROUP>    1898 1月  25 04:55 curl_batch.sh
-rw-r--r-- 1 <USER> <GROUP>    9896 1月  29 16:41 htmlparser.py
-rw-r--r-- 1 <USER> <GROUP>     431 1月  29 17:00 langident.py
-rw-r--r-- 1 <USER> <GROUP>    7488 1月  29 10:58 log
drwxrwxr-x 2 <USER> <GROUP>    4096 1月  23 02:45 model
-rw-r--r-- 1 <USER> <GROUP>     747 1月  29 10:14 post_demo.py
drwxr-xr-x 2 <USER> <GROUP>    4096 1月  29 17:50 __pycache__
-rw-r--r-- 1 <USER> <GROUP>    7522 1月  29 16:43 sent_split.py
-rw-r--r-- 1 <USER> <GROUP>    3160 1月  29 17:53 server.py
-rw-r--r-- 1 <USER> <GROUP>   11809 1月  29 17:57 sever.log
-rw-r--r-- 1 <USER> <GROUP> 2423393 10月 16 2020 spm.128k.model
-rw-r--r-- 1 <USER> <GROUP>      36 1月  29 11:02 start_server.sh
-rw-r--r-- 1 <USER> <GROUP>      46 1月  29 11:01 stop_server.sh
-rw-r--r-- 1 <USER> <GROUP>    8784 1月  25 03:22 thread_sync_pools.py
-rw-r--r-- 1 <USER> <GROUP>     568 1月  25 04:57 tmp.txt
-rw-r--r-- 1 <USER> <GROUP>    8242 1月  29 17:50 translate_api.py
(base) [root@VM-0-8-centos mt_server]# mkdir testdir
(base) [root@VM-0-8-centos mt_server]# cd testdir/
(base) [root@VM-0-8-centos testdir]# ll
总用量 0
(base) [root@VM-0-8-centos testdir]# wget https://github.com/thunlp/Chinese_Rumor_Dataset.git
--2024-01-29 18:12:29--  https://github.com/thunlp/Chinese_Rumor_Dataset.git
正在解析主机 github.com (github.com)... 20.205.243.166
正在连接 github.com (github.com)|20.205.243.166|:443... ^C
(base) [root@VM-0-8-centos testdir]# git cloen https://github.com/thunlp/Chinese_Rumor_Dataset.git^C
(base) [root@VM-0-8-centos testdir]# git clone https://github.com/thunlp/Chinese_Rumor_Dataset.git
正克隆到 'Chinese_Rumor_Dataset'...
^C
(base) [root@VM-0-8-centos testdir]# ll
总用量 0
(base) [root@VM-0-8-centos testdir]# ls -
ls: 无法访问-: 没有那个文件或目录
(base) [root@VM-0-8-centos testdir]# ls -l
总用量 0
(base) [root@VM-0-8-centos testdir]# git clone https://github.com/thunlp/Chinese_Rumor_Dataset.git
正克隆到 'Chinese_Rumor_Dataset'...




^C
(base) [root@VM-0-8-centos testdir]# wget https://github.com/thunlp/Chinese_Rumor_Dataset/blob/master/rumors_v170613.json
--2024-01-29 18:13:20--  https://github.com/thunlp/Chinese_Rumor_Dataset/blob/master/rumors_v170613.json
正在解析主机 github.com (github.com)... 20.205.243.166
正在连接 github.com (github.com)|20.205.243.166|:443... 



^C
(base) [root@VM-0-8-centos testdir]# ^C
(base) [root@VM-0-8-centos testdir]# ll
总用量 0
(base) [root@VM-0-8-centos testdir]# git clone https://github.com/thunlp/Chinese_Rumor_Dataset.git
正克隆到 'Chinese_Rumor_Dataset'...
该数据为从新浪微博不实信息举报平台抓取的中文谣言数据，分为两个部分。其中当前目录下的数据集仅包含谣言原微博，不包含转发/评论信息；而CED_Dataset中是包含转发/评论信息的中文谣言数据集。

数据集介绍
第一部分数据集（./rumors_v170613.json）共包含从2009年9月4日至2017年6月12日的31669条谣言。文件中，每一行为一条json格式的谣言数据，字段释义如下：

rumorCode: 该条谣言的唯一编码，可以通过该编码直接访问该谣言举报页面。
title: 该条谣言被举报的标题内容
informerName: 举报者微博名称
informerUrl: 举报者微博链接
rumormongerName: 发布谣言者的微博名称
rumormongerUr: 发布谣言者的微博链接
rumorText: 谣言内容
visitTimes: 该谣言被访问次数
result: 该谣言审查结果
publishTime: 该谣言被举报时间
引用
如果您使用该数据集，请引用以下论文：

中文：
    @article{liu2015rumors,
      title={中文社交媒体谣言统计语义分析},
      author={刘知远 and 张乐 and 涂存超 and 孙茂松},
      journal={中国科学: 信息科学},
      volume={12},
      pages={1536--1546},
      year={2015}
    }
English：
    @article{liu2015rumors,
      title={Statistical and semantic analysis of rumors in Chinese social media},
      author={Liu, Zhiyuan and Zhang, Le and Tu, Cunchao and Sun, Maosong},
      journal={Scientia Sinica Informationis},
      volume={45},
      number={12},
      pages={1536},
      year={2015}
    }
第二部分数据集（CED_Dataset）包含与微博原文相关的转发与评论信息，数据集中共包含谣言1538条和非谣言1849条。该数据集分为微博原文与其转发/评论内容。其中所有微博原文（包含谣言与非谣言）在original-microblog文件夹中，剩余两个文件夹non-rumor-repost和rumor-repost分别包含非谣言原文与谣言原文的对应的转发与评论信息。（该数据集中并不区分评论与转发）该数据文件中，每条原文，评论或评论均为json格式的数据，部分字段释义如下：

微博原文信息：
text: 微博原文的文字内容
user: 发布该条微博原文的用户信息
time: 用户发布该条微博原文的时间（时间戳格式）
转发/评论信息：
uid: 发布该转发/评论的用户ID
text: 转发/评论的文字内容（若部分用户转发时不添加评论内容，该项无内容）
data: 该转发/评论的发布时间（格式如：2014-07-24 14:37:38）
引用
如果您使用该数据集，请引用以下论文：

@article{song2018ced,
  title={CED: Credible Early Detection of Social Media Rumors},
  author={Song, Changhe and Tu, Cunchao and Yang, Cheng and Liu, Zhiyuan and Sun, Maosong},
  journal={arXiv preprint arXiv:1811.04175},
  year={2018}
}
