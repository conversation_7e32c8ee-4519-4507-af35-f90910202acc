import requests, sys

def translate(text, src_lang='en', tgt_lang='zh'):
    url="http://*************:8000/translate"
    url="http://************:8000/translate"
    data_json={
        "src_text_list": [
            text
        ],
        "src_lang": src_lang,
        "tgt_lang": tgt_lang,
    }
    resp = requests.post(url, headers={'content-type': 'application/json'},json=data_json, timeout=600)
    resp = resp.json()
    print(resp['result'])

if __name__ == "__main__":
    text="this is a test"
    for line in sys.stdin:
        line = line.strip()
        translate(line)

