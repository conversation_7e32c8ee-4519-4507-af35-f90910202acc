import re


def count_punctuation_chars(text):
    """
    计算给定文本中包含的标点符号字符数。

    Args:
        text (str): 待计算标点符号数的文本。

    Returns:
        int: 文本中的标点符号字符数。
    """
    punctuation_pattern = r'[^\w\s]'
    return len(re.findall(punctuation_pattern, text))


def count_characters_without_punctuation(text):
    """
    计算给定文本中包含的非标点符号字符数（即字数）。

    Args:
        text (str): 待计算字数的文本。

    Returns:
        int: 文本中的非标点符号字符数（字数）。
    """
    return len(text) - count_punctuation_chars(text)


def count_chinese_chars(text):
    """
    计算给定文本中包含的中文字符数。

    Args:
        text (str): 待计算中文字符数的文本。

    Returns:
        int: 文本中的中文字符数。
    """
    chinese_char_pattern = r'[\u4e00-\u9fff]'
    return len(re.findall(chinese_char_pattern, text))


def count_english_words(text):
    """
    计算给定文本中包含的英文单词数。

    Args:
        text (str): 待计算英文单词数的文本。

    Returns:
        int: 文本中的英文单词数。
    """
    word_pattern = r'\b\w+\b'
    return len(re.findall(word_pattern, text))


def calc_word_count(src_text_list, src_lang):
    if not src_text_list or not src_lang:
        return 0
    sentence_word_counts = [count_chinese_chars(text) if src_lang == 'zh' else count_english_words(text) for text in
                            src_text_list]
    total_word_count = sum(sentence_word_counts)
    return total_word_count


def main():
    sentence = "这是一个包含中英文的句子。This is an English sentence with Chinese characters."

    print("总字数（不含标点）:", count_characters_without_punctuation(sentence))
    print("中文字符数:", count_chinese_chars(sentence))
    print("英文单词数:", count_english_words(sentence))


if __name__ == "__main__":
    sentence = "这是一个包含中英文的句子。This is an English sentence with Chinese characters."
    print(count_chinese_chars(sentence))
    print(count_english_words(sentence))
    print(count_characters_without_punctuation(sentence))
    print(count_punctuation_chars(sentence))
    main()
