import re
from configurer.config_reader import get_split_conf
from util.model_loader import ModelLoader
from logger.logger import biz_logger

# 加载 SentencePiece 模型
spm_model = ModelLoader().load_spm_model()
space_code = '▁'


def split_sentences(text: str) -> list[str]:
    """
    基于中英文常见句末标点直接分句，保留标点。
    """
    biz_logger.info(f"按标点分句，文本长度：{len(text)}")
    txt = text.strip()
    if not txt:
        return []
    # 中英文句末标点：. ! ? ； 。 ！ ？ 分割
    # parts = re.split(r'(?<=[.!?。！？])\s*', txt)
    split_pattern = r'[\r\n]+|(?<=[。！？])|(?<=[.!?])\s+(?=[A-Z])'
    parts = re.split(split_pattern, txt)

    # 去除空串并返回
    return [p for p in (p.strip() for p in parts) if p]


def forced_split(text: str, max_tokens: int, min_tokens: int) -> list[str]:
    """
    若片段超长，优先在标点处按 max_tokens 切分，否则强制切分。
    """
    if not spm_model:
        biz_logger.warning("SentencePiece 未加载，返回原文")
        return [text]

    tokens = spm_model.encode(text, out_type=str, enable_sampling=False)
    chunks, cur, last_p = [], [], -1
    # 遍历所有 token
    for i, tk in enumerate(tokens):
        cur.append(tk)
        # 记录最后一个标点位置
        if tk in ('.', '?', '!', ';', '。', '！', '？', '；'):
            last_p = i
        # 达到 max_tokens 阈值，切分
        if len(cur) >= max_tokens:
            # 优先在标点处切
            if 0 <= last_p < len(cur) and (last_p + 1) >= min_tokens:
                cut = last_p + 1
            else:
                cut = len(cur)
            piece, cur = cur[:cut], cur[cut:]
            text_piece = "".join(piece).replace(space_code, " ").strip()
            chunks.append(text_piece)
            last_p = -1
    # 余下未切分的 tokens
    if cur:
        text_piece = "".join(cur).replace(space_code, " ").strip()
        chunks.append(text_piece)
    return [c for c in chunks if c]


def split(text: str, lang: str) -> list[tuple[str, None]]:
    """
    主入口：
    1. 若整体 token 数 ≤ max_length，直接返回原文；
    2. 否则按标点分句；
    3. 对每句若仍超长则 forced_split；
    4. 返回 [(segment, None), ...]
    """
    biz_logger.info(f"开始分句处理，语言：{lang}，文本长度：{len(text)}")
    conf = get_split_conf(lang)
    max_len = conf.get('max_length', 100)
    min_len = conf.get('min_length', 15)

    # 1) 整段 token 数判断
    tokens = spm_model.encode(text, out_type=str, enable_sampling=False) if spm_model else []
    if len(tokens) <= max_len:
        biz_logger.info(f"整体 {len(tokens)} tokens ≤ 最大 {max_len}，不做分句")
        return [(text, None)]

    # 2) 按标点分句
    sents = split_sentences(text)
    biz_logger.info(f"分句得到 {len(sents)} 条子句")

    # 3) 对每句进行 forced_split
    result = []
    for idx, sent in enumerate(sents, 1):
        toks = spm_model.encode(sent, out_type=str, enable_sampling=False) if spm_model else []
        if len(toks) > max_len:
            biz_logger.info(f"第 {idx} 句 ({len(toks)} tokens) 超长 → 强制切分")
            result.extend(forced_split(sent, max_len, min_len))
        else:
            result.append(sent)

    biz_logger.info(f"最终得到 {len(result)} 条输入片段")
    return [(seg, None) for seg in result]


if __name__ == "__main__":
    # 简单自测示例
    txt = "这是一个测试。This is a test! 测试继续。"
    for seg, _ in split(txt, 'zh'):
        print(f"[{len(seg)} chars] {seg}")
