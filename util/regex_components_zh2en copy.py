# util/regex_components_en2zh.py
import re
from collections import OrderedDict
from util.jiaobiao import SUPERSCRIPT_MAP, SUBSCRIPT_MAP
from configurer.config_reader import get_non_translation_exact_strings


# 本地定义的"精确非译"字符串
NON_TRANSLATION_EXACT_STRINGS = [
    # "Doc.Ref.No.DER-GDL-001-03",
    # 'SpO₂',
    # 'MFF-0300328',
    # 'cmc450623 1/81',
    # "Doc.",
    # "Ref.",
    # "No.",
    # "DER-GDL-001-03",
    # 'Everest IWRS',
    # 'Novotech',
    # 'Cₘₐₓ'
]

# 合并本地和线上配置的"精确非译"字符串
exact_match_strings = get_non_translation_exact_strings() or []  # 线上配置
all_exact_strings = list(set(exact_match_strings + NON_TRANSLATION_EXACT_STRINGS))  # 合并去重



# ——— 上下标字符 ——#
ALL_SCRIPT_CHARS = ''.join(SUPERSCRIPT_MAP.values()) + ''.join(SUBSCRIPT_MAP.values())
ALL_SCRIPTS = f"[{re.escape(ALL_SCRIPT_CHARS)}]+"
ALL_SCRIPTS_RE = re.compile(ALL_SCRIPTS)

# 允许左右不跟 [A-Za-z0-9] 也不跟上下标
_B = rf'(?<![A-Za-z0-9{ALL_SCRIPT_CHARS}])'
_B2 = rf'(?![A-Za-z0-9{ALL_SCRIPT_CHARS}])'
exact_match = _B + r'(?:' + '|'.join(map(re.escape, all_exact_strings)) + r')' + _B2
exact_match_re = re.compile(exact_match)


# —— 自闭合标签和 Markdown 图片 ——#
SELF_CLOSING_TAG_RE = re.compile(
    r'''(?x)
    (
      <[A-Za-z][^<>]*?/>          # HTML/XML 自闭合
    |
      !\[[^\]]*\]\([^\)]+\)       # Markdown 图片
    )
    ''',
    re.IGNORECASE
)

# —— g 标签数字 ——#
# 从 <g ...>123</g> 扩展到也能匹配 </g id="1"> 这种闭合
G_TAG_NUMBER_RE = re.compile(
    r'<g\b[^>]*>'           # 开标签，可能有各种属性
    r'(\d+|\s+|\d+\s+|\s+\d+)'  # 标签内容：数字或空白
    r'</g\b[^>]*>',        # 闭合标签，也允许属性
    flags=re.IGNORECASE | re.VERBOSE
)

# —— 非中/英文单字的 g 标签 ——#
# 同样把 </g> 换成能匹配带属性的 </g ...>
G_TAG_NON_CHINESE_EN_WORD_RE = re.compile(
    r'<g\b[^>]*>'                                      # 开标签
    r'(?!(?:[^<>]*[\u4e00-\u9fa5])|(?:[^<>]*[A-Za-z]{2,}))'  # 里面既无中文也无双字母
    r'[^\s<>]\s*'                                         # 单个非空白字符
    r'</g\b[^>]*>',                                     # 闭合标签，允许属性
    flags=re.IGNORECASE | re.VERBOSE
)


# —— 最终匹配字典（按顺序：先长后短/先精确后宽松） ——#
PATTERNS_ZH2EN = OrderedDict({
    'exact_match':           exact_match_re,

    # 5. 各种“标签”与属性
    # 'self_closing_tag':      SELF_CLOSING_TAG_RE,
    # 'g_tag_number':          G_TAG_NUMBER_RE,
    # 'g_tag_non_chinese_en_word': G_TAG_NON_CHINESE_EN_WORD_RE,

    # 7. 最后匹配所有上下标
    'all_scripts':           ALL_SCRIPTS_RE,
})

