#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模型加载器模块：
    提供 spaCy 模型、SentencePiece 模型以及 HanLP 模型的单例加载功能，
    避免重复加载模型，提高资源利用率。
"""

import logging
import spacy
import sentencepiece as spm
import os
# import hanlp
from configurer.singleton import singleton  # 使用单例装饰器
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)
if not logger.handlers and not logging.getLogger().handlers:
    logging.basicConfig(level=logging.INFO,
                        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    logger = logging.getLogger(__name__)

base_dir = os.path.dirname(__file__)


@singleton
class ModelLoader:
    def __init__(self):
        self._spacy_models: Dict[str, Any] = {}
        self._spm_models: Dict[str, Optional[spm.SentencePieceProcessor]] = {}
        self._hanlp_models: Dict[str, Any] = {}

    # def load_spacy_model(self, lang: str) -> Optional[Any]:
    #     """
    #     加载指定语言的 spaCy 模型，如果已经加载则直接返回。

    #     参数:
    #         lang: 语言代码，如 'zh'（中文）或 'en'（英文）

    #     返回:
    #         spaCy 模型对象；加载失败时返回 None。
    #     """
    #     if lang in self._spacy_models:
    #         return self._spacy_models[lang]

    #     try:
    #         if lang == 'zh':
    #             logger.info("加载 spaCy zh 模型")
    #             nlp = spacy.load("zh_core_web_sm")
    #             self._spacy_models[lang] = nlp
    #             return nlp
    #         elif lang == 'en':
    #             logger.info("加载 spaCy en 模型")
    #             nlp = spacy.load("en_core_web_sm")
    #             self._spacy_models[lang] = nlp
    #             return nlp
    #         else:
    #             logger.warning(f"不支持的语言: {lang}")
    #             return None
    #     except Exception as e:
    #         logger.error(f"加载 spaCy {lang} 模型失败: {e}")
    #         self._spacy_models[lang] = None
    #         return None

    def load_spm_model(self, model_path: str = os.path.join(base_dir, 'sentencepiece.bpe.model')) -> Optional[spm.SentencePieceProcessor]:
        """
        加载 SentencePiece 模型，并缓存模型实例。

        参数:
            model_path: 模型文件路径。

        返回:
            SentencePieceProcessor 实例；加载失败时返回 None。
        """
        if model_path in self._spm_models:
            return self._spm_models[model_path]
        sp_processor = spm.SentencePieceProcessor()
        try:
            sp_processor.load(model_path)
            self._spm_models[model_path] = sp_processor
            logger.info(f"成功加载 SentencePiece 模型: {model_path}")
            return sp_processor
        except Exception as e:
            logger.error(f"加载 SentencePiece 模型失败: {model_path}, 错误: {e}")
            self._spm_models[model_path] = None
            return None

    # def load_hanlp_model(self, model_name: str = "MSRA_NER_ELECTRA_SMALL_ZH") -> Any:
    #     """
    #     加载 HanLP 模型，采用 hanlp.load 加载预训练模型，并缓存。

    #     参数:
    #         model_name: HanLP 模型名称，默认为 "MSRA_NER_ELECTRA_SMALL_ZH"（可根据需要调整）

    #     返回:
    #         HanLP 模型对象；加载失败时返回 None。
    #     """

    #     # 使用pipeline方式加载HanLP模型
    #     HanLP = hanlp.pipeline() \
    #         .append(hanlp.utils.rules.split_sentence, output_key='sentences') \
    #         .append(hanlp.load('FINE_ELECTRA_SMALL_ZH'), output_key='tok') \
    #         .append(hanlp.load('MSRA_NER_ELECTRA_SMALL_ZH'), output_key='ner', input_key='tok')
    #     return HanLP


        # if model_name in self._hanlp_models:
        #     return self._hanlp_models[model_name]
        # try:
        #     logger.info(f"加载 HanLP 模型: {model_name}")
        #     model = hanlp.load(model_name)
        #     self._hanlp_models[model_name] = model
        #     return model
        # except Exception as e:
        #     logger.error(f"加载 HanLP 模型失败: {model_name}, 错误: {e}")
        #     self._hanlp_models[model_name] = None
        #     return None
    def load_spacy_model(self, param):
        pass

# 确保单例化（如果未使用@singleton装饰器，则可再次应用）
# ModelLoader = singleton(ModelLoader)
