import sys
import os
import yaml


# 添加项目的父目录到sys.path中，以便Python能找到子目录中的包
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)

CONFIG_FILE = os.path.join(os.path.dirname(__file__), 'project_config.yml')
print(f"CONFIG_FILE: {CONFIG_FILE}")

from configurer.yy_nacos import config
from exception.exception import BaseError

"""
{
    "mt-user-whitelist": {
        "yiya":{
            "appname": "yiya",
            "appsecret": "yy-1234",
            "company": "医雅语言科技有限责任公司"
        }
    },
    "punctuation_mapping": {
        "zh": {
            ',': '，',
            '?': '？',
            '!': '！',
            ':': '：',
            ';': '；',
            '(': '（',
            ')': '）'
        }  
    },
    "alicloud_middleware_config": {
        "ak": "",
        "sk": ""
    }
}
"""


def get_api_key(appname):
    if not appname:
        raise BaseError(401, "empty appname")

    if appname not in config['mt_user_whitelist']:
        raise BaseError(401, "invalid appname")

    d = config['mt_user_whitelist'][appname]
    if not d:
        raise BaseError(401, "invalid appname")
    return d['appsecret']


def get_punctuation_mapping(target_lang):
    # 判断 target_lang 是否为空
    if not target_lang:
        raise ValueError("No target language specified")

    try:
        # 尝试从配置中提取标点映射
        d = config['punctuation_mapping'][target_lang]
    except KeyError:
        # 如果 target_lang 不在预设的映射中
        raise ValueError(f"No punctuation mapping found for language: {target_lang}")
    except TypeError:
        # 如果 config['punctuation_mapping'] 不存在或不是字典
        raise TypeError("Punctuation mapping configuration is not properly set up")

    # 检查获取的映射是否为空
    if not d:
        raise ValueError(f"Punctuation mapping for {target_lang} is empty")

    return d


def get_alicloud_aksk():
    d = config['alicloud_middleware_config']
    return d['ak'], d['sk']

def get_fastgpt_ak():
    d = config['yy_mt_config']
    return d['fastgpt_revision_ak']

def get_dify_unk_revision_ak():
    d = config['yy_mt_config']
    return d['dify_unk_revision_ak']

def get_no_translatable_elements():
    return config['no_translatable_elements']

def get_non_translation_exact_strings():
    """
    支持两种写法：
      1) config['non_translation_exact_strings'] 直接是一个 list
      2) config['non_translation_exact_strings'] 是个 dict，
         然后里面有个 key 'non_translation_exact_strings'
    """
    val = config.get('non_translation_exact_strings')

    # 场景 1：直接是列表
    if isinstance(val, list):
        return val

    # 场景 2：包了一层 dict
    if isinstance(val, dict):
        inner = val.get('non_translation_exact_strings')
        if isinstance(inner, list):
            return inner

    # 都不是的话，就返回空列表，或者抛个错误都行
    return []

def get_name_list():
    """
    支持两种写法：
      1) config['name_list'] 直接是一个 list
      2) config['name_list'] 是个 dict，
         然后里面有个 key 'name_list'
    """
    val = config.get('name_list')

    # 场景 1：直接是列表
    if isinstance(val, list):
        return val

    # 场景 2：包了一层 dict
    if isinstance(val, dict):
        inner = val.get('name_list')
        if isinstance(inner, list):
            return inner

    # 都不是的话，就返回空列表，或者抛个错误都行
    return []

def get_split_conf(lang: str) -> dict:
    """
    获取分词配置
    {
        "fastgpt_revision_ak": "fastgpt-eMJIpkSR0rh8bkyoHtjlRBtxSISxPc7l3Rp9zhL1FzsctU1xTKctu0Md17VbUsWi",
        "split_rule": {
            "zh": {
                "punctuations": ["！", ";", "：", "？", ":", "。", ".", "!", "?", "；", "、", ",", "，", ")、"],
                "min_length": 15,
                "max_length": 200,
                "split_length": 200
            },
            "en": {
                "punctuations": [";", "？", ":", ".", "!", "?", "1,", "2,", "3,", "4,", "5,", "6,", "7,", "8,", "9,"],
                "min_length": 15,
                "max_length": 512,
                "split_length": 512
            },
            "default": {
                "punctuations": [";", "？", ":", ".", "!", "?", ","],
                "min_length": 15,
                "max_length": 400,
                "split_length": 400
            }
        }
    }
    """
    mt_conf = config['yy_mt_config']
    split_rule = mt_conf.get('split_rule', {})
    lang = lang.lower()
    if lang in split_rule:
        return split_rule[lang]
    else:
        return split_rule.get('default', {})


# 获取不是句尾标点，后面的首字母要改成小写
def get_no_end_punctuation():
    mt_conf = config['yy_mt_config']
    return mt_conf.get('no_end_punctuation', ',( )?([A-Z][A-Za-z]*)')

# 获取强制断句规则
def get_force_split_rule():
    return config.get('mt_sentence_spliter')

# 获取断句规则
def get_sentence_ending_rule():
    return config.get('sentence_endings')

def get_term_interv_placeholder():
    return config.get('term_interv', {'placeholder': '__TERM'}).get('placeholder', '__TERM')


def load_config():
    with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

project_config = load_config()
