#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import torch
from huggingface_hub import snapshot_download
from transformers import M2M100ForConditionalGeneration, M2M100Tokenizer


def download_model(repo_id: str, save_dir: str):
    """
    下载指定 Hugging Face 仓库快照到本地目录。
    :param repo_id: 仓库名，例如 "facebook/m2m100-12B-last-ckpt"
    :param save_dir: 本地保存路径
    """
    os.makedirs(save_dir, exist_ok=True)
    print(f"开始下载模型 '{repo_id}' 到目录：{save_dir}")
    
    local_path = snapshot_download(
        repo_id=repo_id,
        cache_dir=save_dir,
        resume_download=True   # 支持断点续传
    )
    
    print(f"模型已成功下载到：{local_path}")


def repost_model(source_dir: str, target_dir: str, device_str: str = "cuda:7"):
    """
    从本地 source_dir 加载 facebook/m2m100-12B-last-ckpt
    并将模型部署到指定 GPU (如 cuda:7)，然后保存到 target_dir。
    """
    source_dir = os.path.expanduser(source_dir)
    os.makedirs(target_dir, exist_ok=True)
    
    print(f"Loading tokenizer from {source_dir} ...")
    tokenizer = M2M100Tokenizer.from_pretrained(source_dir)
    
    print(f"Loading model weights to {device_str} (this may take a while)...")
    # 首先以半精度加载以节省显存
    model = M2M100ForConditionalGeneration.from_pretrained(
        source_dir,
        torch_dtype=torch.float16,     # 使用半精度
        low_cpu_mem_usage=True
    )
    # 将模型移动到指定 GPU
    device = torch.device(device_str)
    model.to(device)
    print(f"Model successfully loaded on {device_str}")

    print(f"Saving tokenizer to {target_dir} ...")
    tokenizer.save_pretrained(target_dir)
    print(f"Saving model weights to {target_dir} ...")
    model.save_pretrained(target_dir)
    print("Done.")


if __name__ == "__main__":
    # MODEL_ID = "facebook/m2m100-12B-last-ckpt"
    # SAVE_PATH = "/home/<USER>/data/models/m2m100/base/facebook"
    # download_model(MODEL_ID, SAVE_PATH)

    SRC = "/home/<USER>/data/models/m2m100/base/facebook/models--facebook--m2m100-12B-last-ckpt/snapshots/c94ddeb57e0d6e76b3f3b048668afc5cc7ccc44b/"
    DST = "/home/<USER>/data/models/m2m100/base/facebook/m2m100-12B-last-ckpt"
    repost_model(SRC, DST, device_str="cuda:1")
