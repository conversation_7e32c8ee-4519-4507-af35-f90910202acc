# ct2_translator.py
# -*- coding: utf-8 -*-

import ctranslate2
from transformers import AutoTokenizer
from typing import List
import logging
import json
import jieba
import sacrebleu
from sacrebleu.metrics import CHRF
from tqdm import tqdm

logger = logging.getLogger(__name__)


class CT2Translator:
    """
    基于 ctranslate2 模型的翻译封装。
    合并 tokenize -> translate -> detokenize，
    提供一个 translate_batch(...) 入口即可从文本到文本。

    注意:
      - 如果是多语言模型(M2M100)，可能需要 target_prefix=["__zh"] 等。
      - 如果是单语模型(比如专门zh->en或en->zh)则无需 lang prefix。
    """

    def __init__(self,
                 model_path: str,
                 token_path: str,
                 src_lang: str = "en",
                 tgt_lang: str = "zh",
                 device: str = "cuda",
                 device_index: list[int] | int = 0,
                 beam_size: int = 4,
                 max_length: int = 512,
                 compute_type: str = "auto"):
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self.beam_size = beam_size
        self.max_length = max_length

        # 初始化 tokenizer
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(token_path, src_lang=src_lang)
            logger.info(f"Tokenizer loaded with src_lang='{src_lang}'")
        except TypeError:
            logger.warning(f"Tokenizer from {token_path} does not accept src_lang argument. Loading without it.")
            self.tokenizer = AutoTokenizer.from_pretrained(token_path)

        # Prepare M2M100 target prefix token(s) if needed
        self.tgt_lang_token_id = self.tokenizer.lang_code_to_id[self.tgt_lang]
        self.target_prefix_tokens = [[self.tokenizer.convert_ids_to_tokens(self.tgt_lang_token_id)]]
        logger.info(f"Target prefix tokens for '{self.tgt_lang}': {self.target_prefix_tokens}")

        # 初始化 ctranslate2
        self.translator = ctranslate2.Translator(
            model_path,
            device=device,
            device_index=device_index,
            compute_type=compute_type,
            inter_threads=1 if device == "cuda" else 4,
            intra_threads=1 if device == "cuda" else 4
        )
        logger.info(f"CTranslate2 model loaded from {model_path} on {device}:{device_index} with compute_type='{compute_type}'")


    def translate_batch(self, texts: List[str]) -> List[str]:
        if not texts:
            return []

        # 1) Tokenize
        source_tokens_list = []
        try:
            self.tokenizer.src_lang = self.src_lang
            tokenized_batch = self.tokenizer.batch_encode_plus(
                texts,
                return_tensors=None,
                add_special_tokens=True
            )
            for input_ids in tokenized_batch['input_ids']:
                tokens = self.tokenizer.convert_ids_to_tokens(input_ids)
                source_tokens_list.append(tokens)
        except Exception as e:
            logger.error(f"Tokenization failed: {e}")
            source_tokens_list = []
            for txt in texts:
                input_ids = self.tokenizer.encode(txt)
                tokens = self.tokenizer.convert_ids_to_tokens(input_ids)
                source_tokens_list.append(tokens)

        if not source_tokens_list:
            logger.warning("Tokenization resulted in empty list.")
            return [""] * len(texts)

        # 2) 翻译推理
        batch_target_prefix = self.target_prefix_tokens * len(source_tokens_list)
        try:
            results = self.translator.translate_batch(
                source_tokens_list,
                beam_size=self.beam_size,
                max_decoding_length=self.max_length,
                target_prefix=batch_target_prefix,
            )
        except Exception as e:
            logger.error(f"CTranslate2 translation failed: {e}")
            return ["Error during translation"] * len(texts)

        # 3) Detokenize
        out_texts = []
        tgt_lang_token_str = self.tokenizer.convert_ids_to_tokens(self.tgt_lang_token_id)
        for res in results:
            tokens = res.hypotheses[0]
            if tokens and tokens[0] == tgt_lang_token_str:
                tokens = tokens[1:]
            elif tokens:
                logger.warning(f"Expected target lang token '{tgt_lang_token_str}' at start but found '{tokens[0]}'.")
            else:
                logger.warning("Translation result is empty.")

            try:
                decoded_text = self.tokenizer.batch_decode(
                    [self.tokenizer.convert_tokens_to_ids(tokens)],
                    skip_special_tokens=True,
                    clean_up_tokenization_spaces=True
                )[0]
                out_texts.append(decoded_text)
            except Exception as e:
                logger.error(f"Detokenization failed for tokens {tokens}: {e}")
                out_texts.append("Error during detokenization")

        return out_texts


    def translate(self, text_or_texts, batch_size: int = 10):
        """
        支持单条或多条文本翻译，自动分批。
        """
        if isinstance(text_or_texts, str):
            text_or_texts = [text_or_texts]
        if len(text_or_texts) <= batch_size:
            return self.translate_batch(text_or_texts)
        else:
            translations = []
            for i in tqdm(range(0, len(text_or_texts), batch_size), desc='翻译进度'):
                batch = text_or_texts[i:i + batch_size]
                translations.extend(self.translate_batch(batch))
            return translations


def test_bleu(
    model_path: str,
    token_path: str,
    src_lang: str,
    tgt_lang: str,
    device: str,
    compute_type: str,
    device_index: int,
    batch_size: int,
    src_file: str,
    cache_file: str,
):
    """
    1) 批量翻译 src_file 中的句子；
    2) 缓存翻译结果到 cache_file；
    3) 计算并打印：分词级 BLEU、字符级 BLEU、chrF++；
    返回三项指标的字典。
    """
    # 初始化翻译器
    translator = CT2Translator(
        model_path=model_path,
        token_path=token_path,
        src_lang=src_lang,
        tgt_lang=tgt_lang,
        device=device,
        device_index=device_index,
        max_length=512,
        beam_size=4,
        compute_type=compute_type,
    )

    # 读入 src/ref
    src_texts: List[str] = []
    refs: List[str] = []
    with open(src_file, "r", encoding="utf-8") as f:
        for line in f:
            data = json.loads(line.strip())["translation"]
            src_texts.append(data[src_lang])
            refs.append(data[tgt_lang])

    # 批量翻译
    hyps: List[str] = []
    for i in tqdm(range(0, len(src_texts), batch_size), desc="翻译进度"):
        batch = src_texts[i: i + batch_size]
        hyps.extend(translator.translate(batch))

    # 缓存到文件
    with open(cache_file, "w", encoding="utf-8") as f:
        for s, r, h in zip(src_texts, refs, hyps):
            entry = {"translation": {"src": s, "ref": r, "hyp": h}}
            f.write(json.dumps(entry, ensure_ascii=False) + "\n")

    # 中文标点归一化
    def normalize_zh(s: str) -> str:
        return (
            s.replace("，", ",")
             .replace("。", ".")
             .replace("：", ":")
             .replace("；", ";")
             .replace("？", "?")
             .replace("！", "!")
        )

    refs_norm = [normalize_zh(r) for r in refs]
    hyps_norm = [normalize_zh(h) for h in hyps]

    # 分词级
    refs_tok = [" ".join(jieba.cut(r)) for r in refs_norm]
    hyps_tok = [" ".join(jieba.cut(h)) for h in hyps_norm]

    # 字符级
    refs_char = [" ".join(list(r)) for r in refs_norm]
    hyps_char = [" ".join(list(h)) for h in hyps_norm]

    # 计算各项指标
    bleu_tok = sacrebleu.corpus_bleu(hyps_tok, [refs_tok], tokenize="none").score
    bleu_char = sacrebleu.corpus_bleu(hyps_char, [refs_char], tokenize="none").score
    chrf_score = CHRF().corpus_score(hyps, [refs]).score

    print(f"分词级 BLEU: {bleu_tok:.2f}")
    print(f"字符级 BLEU: {bleu_char:.2f}")
    print(f"chrF++     : {chrf_score:.2f}")

    return {
        "bleu_token": bleu_tok,
        "bleu_char": bleu_char,
        "chrf": chrf_score
    }


def test(
    model_path: str,
    token_path: str,
    src_lang: str,
    tgt_lang: str,
    device: str,
    device_index: int,
    compute_type: str,
):
    """
    简单的交互式翻译示例。
    """
    translator = CT2Translator(
        model_path=model_path,
        token_path=token_path,
        src_lang=src_lang,
        tgt_lang=tgt_lang,
        device=device,
        device_index=device_index,
        max_length=512,
        beam_size=4,
        compute_type=compute_type,
    )
    test_src_texts = [
        # 这里放一些测试句子
    ]
    translations = translator.translate(test_src_texts)
    for src, tgt in zip(test_src_texts, translations):
        print(f"src: {src}")
        print(f"tgt: {tgt}")
        print("-" * 50)


if __name__ == "__main__":
    import os

    checkpoint_step_list = [5000,6000,7000,8000,9000,10000,11000,12000,13000,13200]
    src_file_list = [
        "/mnt/nvme1n1/ycw_meddata/model_data/all_zhen_yxcdata.devtst.json",
        "/home/<USER>/data/cache/yxcdata.en-zh.devtst"
    ]

    # 记录每个 checkpoint 的所有测试集分数
    all_results = {}

    for checkpoint_step in checkpoint_step_list:
        model_path = f"/home/<USER>/data/models/trained/zh2en/m2m-1.2b-53/checkpoint-{checkpoint_step}-ct2"
        token_path = f"/home/<USER>/data/models/trained/zh2en/m2m-1.2b-53/checkpoint-{checkpoint_step}"
        if 'en2zh' in model_path:
            src_lang = "en"
            tgt_lang = "zh"
        else:
            src_lang = "zh"
            tgt_lang = "en"
        device = "cuda"
        compute_type = "int8"
        device_index = 7
        batch_size = 50

        results_per_ckpt = []
        for src_file in src_file_list:
            cache_file = "cache.json"
            metrics = test_bleu(
                model_path=model_path,
                token_path=token_path,
                src_lang=src_lang,
                tgt_lang=tgt_lang,
                device=device,
                compute_type=compute_type,
                device_index=device_index,
                batch_size=batch_size,
                src_file=src_file,
                cache_file=cache_file,
            )
            results_per_ckpt.append({
                "src_file": src_file,
                "metrics": metrics
            })
        all_results[checkpoint_step] = results_per_ckpt

    # 优化输出：每个 checkpoint 下所有测试集的分数
    for checkpoint_step in checkpoint_step_list:
        print(f"\n==== checkpoint-{checkpoint_step} 结果 ====")
        for result in all_results[checkpoint_step]:
            src_file = result["src_file"]
            metrics = result["metrics"]
            print(f"测试集: {src_file}")
            for k, v in metrics.items():
                print(f"  {k}: {v:.2f}")
            print("-" * 40)
        print("=" * 40)

"""
python -m translator.ct2_translator_f1score
"""