from alibabacloud_alimt20181012 import models as alimt_20181012_models
from alibabacloud_alimt20181012.client import Client as alimt20181012Client
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from configurer.config_reader import get_alicloud_aksk
import requests
from configurer.yy_nacos import Nacos
import re
from logger.logger import biz_logger

# 语种识别
class Langident:
    def __init__(self):
        biz_logger.info("初始化语种识别模块")
        # self.client = Langident.create_client()
        # 正则表达式匹配中文字符和中文标点符号的范围
        self.chs_pattern = re.compile(r'[\u4e00-\u9fff]')
        self.chs_punctuation_pattern = re.compile(r'[\u3000-\u303f\uff00-\uffef]')
        self.whole_chs_pattern = re.compile(r'^[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]+$')
        self.eng_word_pattern = re.compile(r'[a-zA-Z]+')
        # 英文字符标点，把英文标点补全
        self.eng_punctuation_pattern = re.compile(r'[a-zA-Z0-9.?!,;:\'\"()\[\]{}<>@#$%^&*\-_+=|\\/ ]')
        biz_logger.info("语种识别模块初始化完成")
    
    @staticmethod
    def create_client() -> alimt20181012Client:
        biz_logger.info("创建阿里云翻译客户端")
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        # 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378659.html。
        ak, sk = get_alicloud_aksk()
        config = open_api_models.Config(
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。,
            access_key_id=ak,
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。,
            access_key_secret=sk
        )
        # Endpoint 请参考 https://api.aliyun.com/product/alimt
        config.endpoint = f'mt.cn-hangzhou.aliyuncs.com'
        biz_logger.info("阿里云翻译客户端创建成功")
        return alimt20181012Client(config)

    def ld(self, text):
        biz_logger.info("开始调用外部语种识别服务")
        url = "http://123.157.234.146:6081/langident"
        data_json = {"data": {"text": text}}
        try:
            resp = requests.post(url, headers={'content-type': 'application/json'}, json=data_json, timeout=600)
            resp = resp.json()
            biz_logger.info(f"语种识别结果: {resp['lang']}")
            return resp['lang']
        except:
            biz_logger.error("语种识别服务调用失败，默认返回英语")
            return 'en'

    def ld2(self, text: str) -> str:
        biz_logger.info("开始调用阿里云语种识别服务")
        get_detect_language_request = alimt_20181012_models.GetDetectLanguageRequest(
            source_text=text
        )
        runtime = util_models.RuntimeOptions()
        try:
            resp = self.create_client().get_detect_language_with_options(get_detect_language_request, runtime)
            if resp and resp.status_code == 200 and resp.body.detected_language is not None:
                biz_logger.info(f"阿里云语种识别结果: {resp.body.detected_language}")
                return resp.body.detected_language
            ConsoleClient.log(UtilClient.to_jsonstring(resp))
        except Exception as error:
            # 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            # 错误 message
            biz_logger.error(f"阿里云语种识别失败: {error.message}")
            print(error.message)
            # 诊断地址
            print(error.data.get("Recommend"))
            UtilClient.assert_as_string(error.message)

        biz_logger.info("阿里云语种识别服务调用失败，默认返回英语")
        return 'en'
    
    def contains_chs(self, text: str) -> str:
        '''
        通过正则表达式判断原文是否包含中文字符
        如果包含，则返回 True，否则返回 False
        '''
        biz_logger.info("检测文本是否包含中文字符")
        match = self.chs_pattern.search(text)
        result = match is not None
        biz_logger.info(f"文本包含中文字符: {result}")
        return result

    def contains_chs_punctuation(self, text: str) -> str:
        '''
        通过正则表达式判断原文是否包含中文字符、中文标点符号
        如果包含，则返回 True，否则返回 False
        '''
        biz_logger.info("检测文本是否包含中文标点符号")
        # 使用 search 方法查找匹配项
        match = self.chs_punctuation_pattern.search(text)
        result = match is not None
        biz_logger.info(f"文本包含中文标点符号: {result}")
        return result
    
    def whole_chs(self, text: str) -> str:
        '''
        判断原文是否全部为中文字符和标点
        如果全是中文字符和标点，则返回 True，否则返回 False
        这个是match还是search？
        match 是匹配整个字符串，search 是匹配字符串中的任意位置
        '''
        biz_logger.info("检测文本是否全部为中文字符和标点")
        result = self.whole_chs_pattern.fullmatch(text) is not None
        biz_logger.info(f"文本全部为中文字符和标点: {result}")
        return result
    
    def chs_ratio(self, text: str) -> bool:
        """
        中文字符（含中文标点）占比超过 40%，就认为是"中文"句子。
        英文按单词数计算；中文和中文标点按字符数计算；忽略其他字符。
        """
        biz_logger.info("计算文本中文字符占比")
        # 如果全中文/全标点，直接返回 True
        if self.whole_chs(text):
            biz_logger.info("文本全部为中文，直接判定为中文句子")
            return True
        
        # 中文字符（含中文标点）占比超过 80%，就认为是"中文"句子。
        chs_chars = len(self.chs_pattern.findall(text))
        ratio = chs_chars / len(text)
        result = ratio > 0.40
        biz_logger.info(f"中文字符占比: {ratio:.2f}, 判定为中文句子: {result}")
        return result

        # # 1) 统计中文字符和中文标点的数量
        # chs_chars = len(self.chs_pattern.findall(text))
        # chs_puncs = len(self.chs_punctuation_pattern.findall(text))
        # total_chs = chs_chars + chs_puncs

        # # 2) 统计英文单词数
        # en_words = len(self.eng_word_pattern.findall(text))

        # # 如果既没有中文也没有英文，就说明是乱七八糟的字符，原文返回，也就是返回True
        # if total_chs == 0 and en_words == 0:
        #     return True

        # # 3) 计算占比：中文单位 / (中文单位 + 英文单词数)
        # ratio = total_chs / (total_chs + en_words)

        # return ratio > 0.3

    def eng_ratio(self, text: str) -> bool:
        """
        英文字符标点占比超过 80%，就认为是"英文"句子。
        示例：
        结果候选材料水平I和水平II的均一性F值分别为1.448 5和1.569 6，均小于F0.05，水平I和水平II在-20°C下均稳定至少30d，水平I和水平II的均一性F值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%的置信限内，表明候选材料具有良好的互换性。
        """
        biz_logger.info("计算文本英文字符占比")
        eng_chars = len(self.eng_punctuation_pattern.findall(text))
        ratio = eng_chars / len(text)
        result = ratio < 0.7
        biz_logger.info(f"英文字符占比: {ratio:.2f}, 判定结果: {result}")
        return result

if __name__ == '__main__':
    Nacos()
    ld = Langident()
    
    # 测试 contains_chs 方法
    print("===== 测试 contains_chs 方法 =====")
    test_cases_contains_chs = [
        "你好", "你好 Hello", "Hello", "。", ".",
        "中文123", "123", "中文？", "English?", "",
        "中文English混合", "标点符号,.!?", "中文标点：，。！？",
        "特殊字符@#$%^&*", "日语ひらがな", "韩语한국어",
        "包含生僻字㊣㊙㊚㊛", "包含emoji😊😂🎉", "数学符号∑∏√∂∞"
    ]
    for text in test_cases_contains_chs:
        result = ld.contains_chs(text)
        print(f"contains_chs('{text}') = {result}")
    
    # 测试 contains_chs_punctuation 方法
    print("\n===== 测试 contains_chs_punctuation 方法 =====")
    test_cases_contains_chs_punctuation = [
        "你好", "你好 Hello", "Hello", "。", ".",
        "中文123", "123", "中文？", "English?", "",
        "中文English混合", "标点符号,.!?", "中文标点：，。！？",
        "全角符号：，。！？【】（）", "半角符号:,.!?[]()", "混合符号：,。!？[]（）",
        "全角数字１２３４５", "全角字母ＡＢＣｄｅｆ", "全角空格　半角空格 "
    ]
    for text in test_cases_contains_chs_punctuation:
        result = ld.contains_chs_punctuation(text)
        print(f"contains_chs_punctuation('{text}') = {result}")
    
    # 测试 whole_chs 方法
    print("\n===== 测试 whole_chs 方法 =====")
    test_cases_whole_chs = [
        "你好", "你好Hello", "Hello", "。", ".",
        "中文123", "123", "中文？", "English?", "",
        "纯中文内容", "中文标点：，。！？", "中文和English混合",
        "全是中文字符和标点符号：这是一段纯中文文本，包含了各种中文标点。",
        "混合内容：这段文字contains some English words",
        "全角符号和汉字：【我是中文】（测试）",
        "只有中文标点符号：，。；：''""？！【】（）《》",
        "中文和全角字母：中文ＡＢＣ",
        "中文和全角数字：中文１２３"
    ]
    for text in test_cases_whole_chs:
        result = ld.whole_chs(text)
        print(f"whole_chs('{text}') = {result}")
    
    # 测试 chs_ratio 方法
    print("\n===== 测试 chs_ratio 方法 =====")
    test_cases_chs_ratio = [
        "你好 world", "Hello 世界", "这是一个 English Chinese 混合的句子",
        "This is an English sentence with 一个 Chinese word",
        "这是纯中文句子没有英文", "This is a pure English sentence",
        "中文占比 more than 30% 的句子", "Sentence less than 三十 percent Chinese",
        "...... ......... ^^&((%_(@!$%))",
        "中文占比刚好80%的句子12345678901234567890",
        "中文占比接近80%的句子123456789012345678901",
        "中文占比超过80%的句子12345678901234567",
        "技术文档：使用Python实现自然语言处理任务",
        "科学论文：量子计算在密码学中的应用研究",
        "混合代码：if(条件){执行操作}else{其他操作}",
        "数据分析报告：2023年第一季度销售额增长了15%，其中华东地区增长最为显著"
    ]
    for text in test_cases_chs_ratio:
        result = ld.chs_ratio(text)
        print(f"chs_ratio('{text}') = {result}")
    
    # 测试 eng_ratio 方法
    print("\n===== 测试 eng_ratio 方法 =====")
    test_cases_eng_ratio = [
        "This is a pure English sentence.",
        "这是纯中文句子没有英文",
        "Mixed sentence with 中文 and English",
        "80% English and 20% 中文 content",
        "20% English and 80% 中文内容占大多数的句子",
        "Technical documentation: Python implementation of NLP tasks",
        "数字和符号：123!@#$%^&*()",
        "结果候选材料水平I和水平II的均一性F值分别为1.448 5和1.569 6，均小于F0.05",
        "The result shows p-value < 0.05, indicating statistical significance",
        "混合数学公式：E=mc² 和 F=ma 是物理学基本公式",
        """mutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. 
Conclusions
The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.""",
        """结果候选材料水平I和水平II的均一性F值分别为1.448 5和1.569 6，均小于F0.05，水平I和水平II在-20°C下均稳定至少30d，水平I和水平II的均一性F值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%的置信限内，表明候选材料具有良好的互换性。
结论：
候选物均匀性好，稳定性好，互换性好，赋值准确可靠，可作为国家标准品。""",
        """You can take the EC pill up to 5 days after unprotected sex.
The sooner you take either EC pill, the better it works.
Some packs contain 1 pill, and some packs contain 2 pills. You can take the 2 pills together.""",
        """(Full name in Chinese): 厦门建发集团有限公司"""
    ]
    for text in test_cases_eng_ratio:
        result = ld.eng_ratio(text)
        print(f"eng_ratio('{text}') = {result}")
    
    # 测试 ld 方法（如果可以连接到服务）
    try:
        print("\n===== 测试 ld 方法 =====")
        test_cases_ld = [
            "Hello world", "你好世界", "Bonjour le monde", "Hola mundo",
            "こんにちは世界", "안녕하세요 세계", "Привет, мир",
            "多语言混合：Hello 你好 Bonjour こんにちは",
            "技术文档：Python是一种解释型高级编程语言",
            "Scientific paper: Quantum computing applications in cryptography"
        ]
        for text in test_cases_ld:
            try:
                result = ld.ld(text)
                print(f"ld('{text}') = {result}")
            except Exception as e:
                print(f"ld('{text}') 出错: {str(e)}")
    except:
        print("跳过 ld 测试，可能无法连接到服务")
    
    # 测试 ld2 方法（如果可以连接到阿里云服务）
    try:
        print("\n===== 测试 ld2 方法 =====")
        test_cases_ld2 = [
            "Hello world", "你好世界", "Bonjour le monde", "Hola mundo",
            "こんにちは世界", "안녕하세요 세계", "Привет, мир",
            "多语言混合：Hello 你好 Bonjour こんにちは",
            "技术文档：Python是一种解释型高级编程语言",
            "Scientific paper: Quantum computing applications in cryptography"
        ]
        for text in test_cases_ld2:
            try:
                result = ld.ld2(text)
                print(f"ld2('{text}') = {result}")
            except Exception as e:
                print(f"ld2('{text}') 出错: {str(e)}")
    except:
        print("跳过 ld2 测试，可能需要配置阿里云服务")