#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
人名罗马化功能测试脚本
测试人名罗马化功能是否正确集成到翻译流程中，确保模型只加载一次且人名正确转换
"""

import sys
import os
import time

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from util.name_romanizer import get_name_romanizer, NameRomanizer
from util.model_loader import ModelLoader


def test_model_loader():
    """测试模型加载器的单例功能"""
    print("=" * 60)
    print("测试 1: 模型加载器单例功能")
    print("=" * 60)
    
    # 创建多个 ModelLoader 实例，应该是同一个对象
    loader1 = ModelLoader()
    loader2 = ModelLoader()
    
    print(f"loader1 id: {id(loader1)}")
    print(f"loader2 id: {id(loader2)}")
    print(f"是否为同一个实例: {loader1 is loader2}")
    
    assert loader1 is loader2, "ModelLoader 应该是单例"
    print("✓ ModelLoader 单例功能正常")
    print()


def test_spacy_model_loading():
    """测试 spaCy 模型加载"""
    print("=" * 60)
    print("测试 2: spaCy 模型加载")
    print("=" * 60)
    
    loader = ModelLoader()
    
    # 第一次加载
    print("第一次加载 spaCy 模型...")
    start_time = time.time()
    model1 = loader.load_spacy_model("zh_core_web_lg")

    load_time1 = time.time() - start_time
    
    if model1 is None:
        print("⚠️  spaCy 模型加载失败，可能是模型未安装")
        print("请运行: python -m spacy download zh_core_web_lg")
        return False
    
    print(f"第一次加载耗时: {load_time1:.4f} 秒")
    print(f"模型加载时间记录: {loader.get_model_load_time('spacy_zh_core_web_lg'):.4f} 秒")
    
    # 第二次加载（应该从缓存获取）
    print("\n第二次加载 spaCy 模型...")
    start_time = time.time()
    model2 = loader.load_spacy_model("zh_core_web_lg")
    load_time2 = time.time() - start_time
    
    print(f"第二次加载耗时: {load_time2:.4f} 秒")
    print(f"是否为同一个模型实例: {model1 is model2}")
    
    assert model1 is model2, "第二次加载应该返回缓存的模型"
    assert load_time2 < load_time1, "第二次加载应该更快"
    
    print("✓ spaCy 模型加载和缓存功能正常")
    print()
    return True


def test_name_romanizer_singleton():
    """测试人名罗马化处理器的单例功能"""
    print("=" * 60)
    print("测试 3: 人名罗马化处理器单例功能")
    print("=" * 60)
    
    # 获取多个实例，应该是同一个对象
    romanizer1 = get_name_romanizer("spacy")
    romanizer2 = get_name_romanizer("spacy")
    
    print(f"romanizer1 id: {id(romanizer1)}")
    print(f"romanizer2 id: {id(romanizer2)}")
    print(f"是否为同一个实例: {romanizer1 is romanizer2}")
    
    assert romanizer1 is romanizer2, "NameRomanizer 应该是单例"
    print("✓ NameRomanizer 单例功能正常")
    print()


def test_name_romanization():
    """测试人名罗马化功能"""
    print("=" * 60)
    print("测试 4: 人名罗马化功能")
    print("=" * 60)
    
    test_cases = [
        "供试品管理人员：刘双双。",
        "供试品配制人员：张敏、王怡婷、朱凡、曾志伟。",
        "项目负责人是欧阳娜娜，协助人员是单于先生和皇甫先生。",
        "这是一段没有人名的文本。",
        "李明和王小红一起工作。"
    ]
    
    romanizer = get_name_romanizer("spacy")
    
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"原文: {text}")
        
        result = romanizer.romanize_text(text)
        
        print(f"罗马化: {result['text']}")
        print(f"人名映射: {result['mapping']}")
        print(f"处理耗时: {result['timing']}")
        
        # 验证结果
        if result['mapping']:
            # 如果有人名映射，检查是否正确替换
            for chinese_name, pinyin_name in result['mapping'].items():
                assert chinese_name not in result['text'], f"原文中的中文人名 '{chinese_name}' 应该被替换"
                assert pinyin_name in result['text'], f"罗马化后的人名 '{pinyin_name}' 应该出现在结果中"
            print(f"✓ 人名替换正确，共处理 {len(result['mapping'])} 个人名")
        else:
            print("✓ 未检测到人名，保持原文")
    
    print("\n✓ 人名罗马化功能测试通过")
    print()


def test_batch_processing():
    """测试批量处理功能"""
    print("=" * 60)
    print("测试 5: 批量处理功能")
    print("=" * 60)
    
    test_texts = [
        "供试品管理人员：刘双双。",
        "供试品配制人员：张敏、王怡婷、朱凡、曾志伟。",
        "项目负责人是欧阳娜娜。",
        "这是一段没有人名的文本。"
    ]
    
    romanizer = get_name_romanizer("spacy")
    
    print(f"批量处理 {len(test_texts)} 条文本...")
    start_time = time.time()
    romanized_texts = romanizer.romanize_texts(test_texts)
    process_time = time.time() - start_time
    
    print(f"批量处理耗时: {process_time:.4f} 秒")
    print(f"处理结果数量: {len(romanized_texts)}")
    
    assert len(romanized_texts) == len(test_texts), "输出文本数量应该与输入相同"
    
    for i, (original, romanized) in enumerate(zip(test_texts, romanized_texts)):
        print(f"\n文本 {i+1}:")
        print(f"  原文: {original}")
        print(f"  罗马化: {romanized}")
    
    print("\n✓ 批量处理功能测试通过")
    print()


def test_performance():
    """测试性能 - 确保模型只加载一次"""
    print("=" * 60)
    print("测试 6: 性能测试 - 模型加载次数")
    print("=" * 60)
    
    # 重置全局实例（仅用于测试）
    import util.name_romanizer
    util.name_romanizer._name_romanizer_instance = None
    
    test_text = "项目负责人是张三，协助人员是李四和王五。"
    
    # 第一次调用 - 应该加载模型
    print("第一次调用人名罗马化...")
    start_time = time.time()
    romanizer1 = get_name_romanizer("spacy")
    result1 = romanizer1.romanize_text(test_text)
    time1 = time.time() - start_time
    
    print(f"第一次调用耗时: {time1:.4f} 秒")
    print(f"模型加载时间: {result1['timing']['load_model_s']} 秒")
    
    # 第二次调用 - 应该使用缓存的模型
    print("\n第二次调用人名罗马化...")
    start_time = time.time()
    romanizer2 = get_name_romanizer("spacy")
    result2 = romanizer2.romanize_text(test_text)
    time2 = time.time() - start_time
    
    print(f"第二次调用耗时: {time2:.4f} 秒")
    print(f"模型加载时间: {result2['timing']['load_model_s']} 秒")
    
    # 验证
    assert romanizer1 is romanizer2, "应该返回同一个实例"
    assert result2['timing']['load_model_s'] == 0, "第二次调用不应该有模型加载时间"
    assert time2 < time1, "第二次调用应该更快"
    
    print("✓ 性能测试通过 - 模型只加载一次")
    print()


def main():
    """主测试函数"""
    print("开始人名罗马化功能测试...")
    print()
    
    try:
        # 测试 1: 模型加载器单例
        test_model_loader()
        
        # 测试 2: spaCy 模型加载
        if not test_spacy_model_loading():
            print("⚠️  跳过后续测试，因为 spaCy 模型加载失败")
            return
        
        # 测试 3: 人名罗马化处理器单例
        test_name_romanizer_singleton()
        
        # 测试 4: 人名罗马化功能
        test_name_romanization()
        
        # 测试 5: 批量处理
        test_batch_processing()
        
        # 测试 6: 性能测试
        test_performance()
        
        print("=" * 60)
        print("🎉 所有测试通过！人名罗马化功能集成成功！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
