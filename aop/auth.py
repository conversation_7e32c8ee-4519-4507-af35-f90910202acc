
import json
import os
from starlette.exceptions import HTTPException
from starlette.middleware.base import BaseHT<PERSON>Middleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response, JSONResponse

from exception.exception import BaseError

from configurer.config_reader import get_api_key
from logger.logger import app_logger
from models.result import make_fail

from logger.biz_log import update_biz_log_context

# 无需认证请求路径
no_auth_url = [
    "/health_check",
]


async def auth(appname, app_secret):
    if not appname or not app_secret:
        raise BaseError(401, "appname and app_secret can not be empty")
    api_key = get_api_key(appname)
    if app_secret != api_key:
        raise BaseError(401, "invalid app_secret")


async def auth_middleware(request: Request):
    if request.url.path in no_auth_url:
        return

    request_id = request.headers.get("x-request-id", "")
    # json_data = await request.json()

    json_data = await request.json()  # Read the body
    request.state.processed_body = json_data  # Store it in the state

    appname = json_data.get('appname', "")
    app_secret = json_data.get('app_secret', "")

    src_lang = json_data.get('src_lang', os.environ.get('SOURCE_LANG', 'zh'))
    tgt_lang = json_data.get('tgt_lang', os.environ.get('TARGET_LANG', 'en'))
    update_biz_log_context(appname=appname, app_secret=app_secret, src_lang=src_lang, tgt_lang=tgt_lang)

    try:
        await auth(appname, app_secret)
        app_logger.info(f"request_id|{request_id}|{request.url}|")
    except BaseError as be:
        app_logger.error(f"request_id|{request_id}|{request.url}|")
        raise HTTPException(status_code=be.code, detail=json.dumps(make_fail(be.code, "unauthorized").__dict__))
    except Exception as e:
        app_logger.error(f"request_id|{request_id}|{request.url}|")
        raise HTTPException(status_code=401, detail=json.dumps(make_fail(401, "unauthorized").__dict__))



class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(
            self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:

        if request.url.path in no_auth_url:
            return await call_next(request)

        request_id = request.headers.get("x-request-id", "")
        json_data = await request.json()

        if not isinstance(json_data, dict):
            return JSONResponse(
                content=make_fail(401, "unauthorized").dict(),
                status_code=401
            )

        appname = json_data.get('appname', "")
        app_secret = json_data.get('app_secret', "")
        try:
            auth(appname, app_secret)
            app_logger.info(f"request_id|{request_id}|{request.url}|")
        except Exception as e:
            app_logger.error(f"request_id|{request_id}|{request.url}|")
            return JSONResponse(
                content=make_fail(401, "unauthorized").dict(),
                status_code=401
            )

        response = await call_next(request)
        return response
