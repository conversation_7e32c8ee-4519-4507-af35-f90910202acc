#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 安装依赖：
# pip install accelerate transformers sacrebleu jieba sentencepiece

import json
import jieba
import sacrebleu
import torch
import argparse

from transformers import (
    M2M100ForConditionalGeneration,
    M2M100Tokenizer,
)

def main():
    parser = argparse.ArgumentParser(
        description="单进程多卡并行推理 M2M100-12B，并计算 BLEU/chrF++"
    )
    parser.add_argument(
        "--model_name", type=str, required=True,
        help="模型路径或 HuggingFace 仓库名",
    )
    parser.add_argument(
        "--src_file", type=str, required=True,
        help="输入 JSONL，每行 {'translation':{'en':..., 'zh':...}}",
    )
    parser.add_argument(
        "--output_file", type=str, default="cache.jsonl",
        help="输出翻译结果文件路径",
    )
    parser.add_argument(
        "--batch_size", type=int, default=8,
        help="推理时的 batch size",
    )
    args = parser.parse_args()

    print(f"[1/4] Loading model `{args.model_name}` with device_map=auto ...")
    model = M2M100ForConditionalGeneration.from_pretrained(
        args.model_name,
        device_map="auto",        # 自动切分到所有可用 GPU
        torch_dtype=torch.float16,# 半精度节省显存
        offload_folder="offload",  # 显存不足时可临时 offload 到 CPU
        offload_state_dict=True,
    )
    tokenizer = M2M100Tokenizer.from_pretrained(args.model_name)
    tokenizer.src_lang = "en"

    print(f"[2/4] Reading input from {args.src_file} ...")
    sources, references = [], []
    with open(args.src_file, "r", encoding="utf-8") as fin:
        for line in fin:
            data = json.loads(line)
            tr = data.get("translation", {})
            src = tr.get("en") or data.get("en")
            ref = tr.get("zh") or data.get("zh") or ""
            if src:
                sources.append(src)
                references.append(ref)
    print(f"  → {len(sources)} sentences loaded.")

    print(f"[3/4] Translating in batches of {args.batch_size} ...")
    hypotheses = []
    embed_device = model.model.shared.weight.device
    total = len(sources)
    for i in range(0, total, args.batch_size):
        batch = sources[i : i + args.batch_size]
        enc = tokenizer(batch, return_tensors="pt", padding=True, truncation=True)
        enc = {k: v.to(embed_device) for k, v in enc.items()}
        out_ids = model.generate(
            **enc,
            forced_bos_token_id=tokenizer.get_lang_id("zh"),
            max_length=256,
            num_beams=5,
        )
        hyps = tokenizer.batch_decode(out_ids, skip_special_tokens=True)
        hypotheses.extend(hyps)
        print(f"  → batch {i//args.batch_size+1}/{(total-1)//args.batch_size+1} done")

    print(f"[4/4] Saving results to {args.output_file} ...")
    with open(args.output_file, "w", encoding="utf-8") as fout:
        for src, ref, hyp in zip(sources, references, hypotheses):
            fout.write(json.dumps({"src": src, "ref": ref, "hyp": hyp}, ensure_ascii=False) + "\n")

    # 计算评测指标
    def tokenize_bleu(text: str) -> str:
        if any("\u4e00" <= ch <= "\u9fff" for ch in text):
            return " ".join(jieba.lcut(text))
        return " ".join(text.split())

    hyps_tok  = [tokenize_bleu(h) for h in hypotheses]
    refs_tok  = [tokenize_bleu(r) for r in references]
    score_word_bleu = sacrebleu.corpus_bleu(hyps_tok, [refs_tok], tokenize="none").score

    hyp_char = [" ".join(list(h.replace(" ", ""))) for h in hypotheses]
    ref_char = [" ".join(list(r.replace(" ", ""))) for r in references]
    score_char_bleu = sacrebleu.corpus_bleu(hyp_char, [ref_char], tokenize="none").score

    score_chrf = sacrebleu.corpus_chrf(hypotheses, [references], word_order=2).score

    print(f"\nEvaluation:")
    print(f"  分词 BLEU: {score_word_bleu:.2f}")
    print(f"  字符 BLEU: {score_char_bleu:.2f}")
    print(f"  chrF++   : {score_chrf:.2f}")

if __name__ == "__main__":
    main()
