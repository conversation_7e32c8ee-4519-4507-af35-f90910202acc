# translator/t2.py
# -*- coding: utf-8 -*-

import json
import torch
import jieba
import sacrebleu
from tqdm import tqdm
from typing import List
from transformers import (
    AutoConfig,
    M2M100ForConditionalGeneration,
    M2M100Tokenizer,
)
from sacrebleu.metrics import CHRF    # ← 新增，用来计算 chrF++

class HFNativeTranslator:
    """
    使用 HuggingFace M2M100 原生推理。
    """

    def __init__(
        self,
        model_dir: str,
        src_lang: str = "en",
        tgt_lang: str = "zh",
        device: str = "cuda",
        device_index: int = 0,
        max_length: int = 128,
        beam_size: int = 4,
    ):
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self.max_length = max_length
        self.beam_size = beam_size

        # 设备
        if torch.cuda.is_available() and device.lower() == "cuda":
            self.device = torch.device(f"cuda:{int(device_index)}")
        else:
            self.device = torch.device("cpu")

        # tokenizer & model
        config = AutoConfig.from_pretrained(model_dir)
        if config.early_stopping is None:
            config.early_stopping = True

        self.tokenizer = M2M100Tokenizer.from_pretrained(model_dir)
        self.model = M2M100ForConditionalGeneration.from_pretrained(
            model_dir, config=config, ignore_mismatched_sizes=True
        ).to(self.device)

        # beam search 设置
        self.model.generation_config.num_beams = self.beam_size
        self.model.generation_config.num_return_sequences = self.beam_size

    def translate_batch(self, texts: List[str]) -> List[List[str]]:
        if not texts:
            return []

        self.tokenizer.src_lang = self.src_lang
        enc = self.tokenizer(
            texts,
            max_length=self.max_length,
            padding=True,
            truncation=True,
            return_tensors="pt",
        )
        enc = {k: v.to(self.device) for k, v in enc.items()}

        with torch.no_grad():
            gen = self.model.generate(
                **enc,
                forced_bos_token_id=self.tokenizer.get_lang_id(self.tgt_lang),
                max_length=self.max_length,
                num_beams=self.beam_size,
                num_return_sequences=self.beam_size,
                early_stopping=True,
            )
        outs = self.tokenizer.batch_decode(gen, skip_special_tokens=True)
        # 按 batch 分组
        return [
            outs[i * self.beam_size : (i + 1) * self.beam_size]
            for i in range(len(texts))
        ]

    def translate(self, text_or_texts):
        if isinstance(text_or_texts, str):
            text_or_texts = [text_or_texts]
        return self.translate_batch(text_or_texts)


def test_bleu(
    model_dir: str,
    cache_file: str = "cache.jsonl",
    src_file: str = "devtst.jsonl",
    batch_size: int = 32,
    device_index: int = 7,
):
    """
    1) 批量翻译
    2) 分词后用 sacrebleu 计算 BLEU
    3) 用 sacrebleu.metrics.CHRF 计算 chrF++
    """
    # —— 初始化翻译器 —— #
    translator = HFNativeTranslator(
        model_dir=model_dir,
        src_lang="en",
        tgt_lang="zh",
        device="cuda",
        device_index=device_index,
        max_length=512,
        beam_size=4,   # >1 才会触发 early_stopping 警告消失
    )

    # —— 读入 src/ref —— #
    src_texts = []
    refs = []
    with open(src_file, encoding="utf-8") as rf:
        for line in rf:
            data = json.loads(line)["translation"]
            src_texts.append(data["en"])
            refs.append(data["zh"])

    # —— 批量翻译 —— #
    hyps = []
    for i in tqdm(range(0, len(src_texts), batch_size), desc="翻译进度"):
        batch = src_texts[i : i + batch_size]
        beams = translator.translate(batch)
        # beam_size=4 时取第一个
        hyps.extend([b[0] for b in beams])

    # —— 中文标点归一化函数 —— #
    def normalize_zh(s: str) -> str:
        return (
            s.replace("，", ",")
             .replace("。", ".")
             .replace("：", ":")
             .replace("；", ";")
             .replace("？", "?")
             .replace("！", "!")
        )

    # —— 分词 & 准备 sacrebleu 输入 —— #
    refs_tok   = [" ".join(jieba.cut(normalize_zh(r))) for r in refs]
    hyps_tok   = [" ".join(jieba.cut(normalize_zh(h))) for h in hyps]
    refs_char  = [" ".join(list(normalize_zh(r)))    for r in refs]
    hyps_char  = [" ".join(list(normalize_zh(h)))    for h in hyps]

    # —— 分词级 BLEU —— #
    bleu_tok   = sacrebleu.corpus_bleu(hyps_tok, [refs_tok], tokenize="none").score

    # —— 字符级 BLEU —— #
    bleu_char  = sacrebleu.corpus_bleu(hyps_char, [refs_char], tokenize="none").score

    # —— chrF++ —— #
    chrf_metric = CHRF()
    chrf_score  = chrf_metric.corpus_score(hyps, [refs]).score

    print(f"分词级 BLEU: {bleu_tok:.2f}")
    print(f"字符级 BLEU: {bleu_char:.2f}")
    print(f"chrF++     : {chrf_score:.2f}")

    # —— 保存 cache —— #
    with open(cache_file, "w", encoding="utf-8") as wf:
        for s, r, h in zip(src_texts, refs, hyps):
            wf.write(
                json.dumps({
                    "translation": {"src": s, "ref": r, "hyp": h}
                }, ensure_ascii=False) + "\n"
            )
    return bleu_tok

def test(model_dir):
    translator = HFNativeTranslator(
        model_dir=model_dir,
        src_lang="en",
        tgt_lang="zh",
        device="cuda",
        device_index=7,
        max_length=512,
        beam_size=4,   # >1 才会触发 early_stopping 警告消失
    )
    src_text = [
        "结果候选材料水平I和水平II的均一性F值分别为1.448 5和1.569 6，均小于F0.05，水平I和水平II在-20°C下均稳定至少30d，水平I和水平II的均一性F值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%的置信限内，表明候选材料具有良好的互换性。",
    ]
    res = translator.translate(src_text)
    for r, s in zip(res, src_text):
        print(f'src: {s}')
        for i, r in enumerate(r):
            print(f'beam {i}: {r}')
        print("-" * 100)


if __name__ == "__main__":
    MODEL_DIR = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-40/checkpoint-60000"
    # MODEL_DIR = "/home/<USER>/data/models/m2m100/base/facebook/m2m100_1.2B"
    print(f'MODEL_DIR: {MODEL_DIR}')
    # src_file = "/home/<USER>/data/cache/yxcdata.en-zh.devtst"
    src_file = "/mnt/nvme1n1/ycw_meddata/model_data/all_zhen_yxcdata.devtst.json"
    print(f'src_file: {src_file}')
    # MODEL_DIR = '/home/<USER>/data/models/m2m100/base/facebook/models--facebook--m2m100-12B-last-ckpt/snapshots/c94ddeb57e0d6e76b3f3b048668afc5cc7ccc44b/'
    test_bleu(MODEL_DIR, cache_file="cache.jsonl", src_file=src_file, device_index=3, batch_size=64)
    # test(MODEL_DIR)
