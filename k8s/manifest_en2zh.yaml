apiVersion: v1
kind: Service
metadata:
  name: medical-en2zh
  namespace: default
  labels:
    app: medical-en2zh
    tier: model
spec:
  ports:
  - port: 8000
    targetPort: 8000
    nodePort: 32081
  selector:
    app: medical-en2zh
    tier: model
  type: NodePort

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: medical-en2zh
  namespace: default
  labels:
    app: medical-en2zh
spec:
  selector:
    matchLabels:
      app: medical-en2zh
  replicas: 1
  template:
    metadata:
      labels:
        app: medical-en2zh
        tier: model
    spec:
      imagePullSecrets:
      - name: yiya-acr-key 

      containers:
        envFrom:
          - configMapRef:
              name: yiya-cm
        env:
        - name: MODEL_LANG
          value: en2zh

        - name: medical-en2zh
          image: "yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/yiya/mt-server:202405121813"
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 4
              memory: 8
              nvidia.com/gpu: 1
            request:
              cpu: 2
              memory: 6
              nvidia.com/gpu: 1
          ports:
            - containerPort: 8000
          readinessProbe:
            tcpSocket:
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 10
            successThreshold: 1
          livenessProbe:
            tcpSocket:
              port: 8000
            initialDelaySeconds: 120
            periodSeconds: 3


