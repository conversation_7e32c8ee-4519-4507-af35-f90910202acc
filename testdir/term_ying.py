import re
import os
from typing import List, Tu<PERSON>, Dict, Any
import ctranslate2
from transformers import AutoTokenizer

def replace_terms(text: str, term_map: List[Dict[str, str]]) -> Tuple[str, Dict[str, str]]:
    """
    将文本中出现的每个术语替换为占位符 <__TERMXXX>，返回替换后的文本及占位符→目标术语的映射字典。
    """
    placeholder_map = {}
    replaced_text = text
    counter = 1
    # 对于每个术语，不区分大小写全局替换
    for term in term_map:
        src_term = term["src_term"]
        placeholder = f"<__TERM{counter:03d}>"
        # 替换所有出现的 src_term
        replaced_text = re.sub(re.escape(src_term), placeholder, replaced_text)
        placeholder_map[placeholder] = term["tgt_term"]
        counter += 1
    return replaced_text, placeholder_map

def restore_terms(text: str, placeholder_map: Dict[str, str]) -> str:
    """
    将翻译后文本中的占位符替换回对应目标术语。
    """
    restored_text = text
    for placeholder, tgt_term in placeholder_map.items():
        restored_text = restored_text.replace(placeholder, tgt_term)
    return restored_text

class Ctranslate2TranslatorHard:
    """
    硬干预方案：先将文本中术语替换成占位符，翻译后再恢复占位符。
    同时采用如下超参：
      - repetition_penalty=1.1：适度惩罚重复
      - length_penalty=1.2：鼓励生成较长输出以覆盖所有占位符
      - coverage_penalty=0.1：惩罚漏译
      - no_repeat_ngram_size=3：防止生成连续重复的片段
    """
    def __init__(self, model_path: str, token_path: str,
                 src_lang: str, tgt_lang: str,
                 beam_size: int, max_decoding_length: int,
                 compute_type: str = "auto", device: str = "cuda", device_index: int = 0):
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self._infer_engine = ctranslate2.Translator(
            model_path, compute_type=compute_type, device=device, device_index=device_index
        )
        self._tokenizer = AutoTokenizer.from_pretrained(token_path)
        self._target_lang_tokens = [self._tokenizer.lang_code_to_token[tgt_lang]]
        self._beam_size = beam_size
        self._max_decoding_length = max_decoding_length

    def tokenize(self, sequences: List[str]) -> List[List[str]]:
        return [self._tokenizer.convert_ids_to_tokens(self._tokenizer.encode(seq))
                for seq in sequences]

    def detokenize(self, tokens_list: List[Any]) -> List[str]:
        # 假设每个结果取第一个 Beam，去掉起始符 BOS
        return [self._tokenizer.decode(
                    self._tokenizer.convert_tokens_to_ids(result.hypotheses[0][1:])
                ) for result in tokens_list]

    def translate(self, tokens_list: List[List[str]]):
        tgt_tokens_list = self._infer_engine.translate_batch(
            tokens_list,
            target_prefix=[self._target_lang_tokens] * len(tokens_list),
            beam_size=self._beam_size,
            max_decoding_length=self._max_decoding_length,
            repetition_penalty=1.1,
            length_penalty=1.2,
            coverage_penalty=0.1,
            no_repeat_ngram_size=3
        )
        return tgt_tokens_list

if __name__ == "__main__":
    # 模型及分词器路径（请根据实际环境调整）
    model_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_quant_2025021722"
    token_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_origin_2025021722"
    src_lang = "zh"
    tgt_lang = "en"

    # 定义术语映射列表
    term_map = [
        {"src_term": "MACCE", "tgt_term": "MACCE"},
        {"src_term": "不良事件", "tgt_term": "adverse events"},
        {"src_term": "严重不良事件", "tgt_term": "serious adverse events"},
        {"src_term": "试验组", "tgt_term": "test group"},
        {"src_term": "对照组", "tgt_term": "control group"},
        {"src_term": "试验产品DissolveTM冠脉药物球囊扩张导管", "tgt_term": "investigational product DissolveTM coronary drug coated balloon"},
        {"src_term": "冠状动脉支架内再狭窄", "tgt_term": "coronary in-stent restenosis"}
    ]
    
    # 源文本样例（共5条）
    texts = [
        "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
        "本申请要求于2023年2月17日递交的中国专利申请第202310153996.9号的优先权，在此全文引用上述中国专利申请公开的内容以作为本申请的一部分。",
        "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；",
        "试验组共记录103例（80.5%）不良事件，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。",
        "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；试验组共记录103例（80.5%）不良事件，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。"
    ]
    
    # 对每条文本进行术语替换（硬干预）
    replaced_texts = []
    placeholder_maps = []
    for text in texts:
        replaced, ph_map = replace_terms(text, term_map)
        replaced_texts.append(replaced)
        placeholder_maps.append(ph_map)
    
    translator = Ctranslate2TranslatorHard(
        model_path=model_path,
        token_path=token_path,
        src_lang=src_lang,
        tgt_lang=tgt_lang,
        beam_size=5,
        max_decoding_length=512,
        device="cuda"
    )
    
    tokens = translator.tokenize(replaced_texts)
    translated_tokens = translator.translate(tokens)
    results = translator.detokenize(translated_tokens)
    
    final_results = []
    for res, ph_map in zip(results, placeholder_maps):
        final_results.append(restore_terms(res, ph_map))
    
    for i, (src, res) in enumerate(zip(texts, final_results), start=1):
        print(f"【硬干预方案】\n源文本 {i}: {src}")
        print(f"翻译结果 {i}: {res}")
        print("-" * 50)
