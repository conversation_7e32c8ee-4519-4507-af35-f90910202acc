#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
人名罗马化处理器模块：
    基于 name_api.py 的 NameTranslator 类，创建一个集成到翻译流程中的人名罗马化处理器。
    使用 model_loader 加载的模型，避免重复加载。
"""

import time
import re
from typing import List, Dict, Optional
from abc import ABC, abstractmethod

# 假设这些模块存在且功能正确
from util.model_loader import ModelLoader
from util.name_api import PypinyinConverter, _ALL_SURNAMES, _SURNAME_PINYIN
from logger.logger import biz_logger


class NERInterface(ABC):
    """NER 接口，用于统一不同 NER 实现"""

    @abstractmethod
    def extract_person(self, text: str) -> List[str]:
        """提取文本中的人名"""
        pass


class SpacyNERAdapter(NERInterface):
    """spaCy NER 适配器"""

    def __init__(self, model_loader: ModelLoader):
        self.model_loader = model_loader
        self._nlp = None
        self.load_time: float = 0.0  # 新增：用于存储模型加载时间

    @property
    def nlp(self):
        """延迟加载 spaCy 模型，并记录加载时间"""
        if self._nlp is None:
            biz_logger.info("正在延迟加载 spaCy 模型 'zh_core_web_lg'...")
            start_time = time.perf_counter()
            self._nlp = self.model_loader.load_spacy_model("zh_core_web_lg")
            self.load_time = time.perf_counter() - start_time
            biz_logger.info(f"spaCy 模型加载完成，耗时: {self.load_time:.4f} 秒")

            if self._nlp is None:
                raise RuntimeError("无法加载 spaCy 模型")
        return self._nlp

    def extract_person(self, text: str) -> List[str]:
        """使用 spaCy 提取人名"""
        seen, out = set(), []
        for ent in self.nlp(text).ents:
            if ent.label_ == "PERSON" and ent.text not in seen:
                seen.add(ent.text)
                out.append(ent.text)
        return out


class HanlpNERAdapter(NERInterface):
    """HanLP NER 适配器"""

    def __init__(self, model_loader: ModelLoader):
        self.model_loader = model_loader
        self._pipeline = None
        self.load_time: float = 0.0  # 新增：用于存储模型加载时间

    @property
    def pipeline(self):
        """延迟加载 HanLP 管道，并记录加载时间"""
        if self._pipeline is None:
            biz_logger.info("正在延迟加载 HanLP 模型 'default_ner_pipeline'...")
            start_time = time.perf_counter()
            self._pipeline = self.model_loader.load_hanlp_model("default_ner_pipeline")
            self.load_time = time.perf_counter() - start_time
            biz_logger.info(f"HanLP 模型加载完成，耗时: {self.load_time:.4f} 秒")

            if self._pipeline is None:
                raise RuntimeError("无法加载 HanLP 管道")
        return self._pipeline

    def extract_person(self, text: str) -> List[str]:
        """使用 HanLP 提取人名"""
        doc = self.pipeline(text)
        seen, out = set(), []
        if 'ner' in doc:
            for sent_ner in doc['ner']:
                for word, label, _, _ in sent_ner:
                    # 检查 'NR' 和 'PERSON' 两种可能的人名标签
                    if label in ('PERSON', 'NR') and word not in seen:
                        seen.add(word)
                        out.append(word)
        return out


class NameRomanizer:
    """
    人名罗马化处理器

    集成到翻译流程中，在预处理阶段将中文人名转换为拼音形式。
    使用 ModelLoader 的单例模型，避免重复加载。
    """

    def __init__(self, ner_type: str = "spacy", model_loader: Optional[ModelLoader] = None):
        """
        初始化人名罗马化处理器

        参数:
            ner_type: NER 类型，支持 "spacy" 或 "hanlp"
            model_loader: 模型加载器实例，如果为 None 则创建新实例
        """
        self.model_loader = model_loader or ModelLoader()
        self.converter = PypinyinConverter()

        # 根据类型创建 NER 适配器
        if ner_type.lower() == "spacy":
            self.ner = SpacyNERAdapter(self.model_loader)
        elif ner_type.lower() == "hanlp":
            self.ner = HanlpNERAdapter(self.model_loader)
        else:
            raise ValueError(f"不支持的 NER 类型: {ner_type}")

        self.ner_type = ner_type.lower()
        self._load_time_reported = False

        biz_logger.info(f"人名罗马化处理器初始化完成，使用 {ner_type} NER")

    def get_model_load_time(self) -> float:
        """从 NER 适配器获取模型加载时间"""
        # 修正：从适配器实例获取记录的加载时间
        return getattr(self.ner, 'load_time', 0.0)

    def romanize_text(self, text: str) -> Dict:
        """
        对单个文本进行人名罗马化

        参数:
            text: 输入文本

        返回:
            包含处理结果和统计信息的字典
        """
        tic = time.perf_counter()

        load_time_to_report = 0.0

        try:
            # 提取人名。这一步会触发模型的延迟加载（如果尚未加载）
            persons = self.ner.extract_person(text)
            t1 = time.perf_counter()

            # 在模型加载后，获取加载时间（只在第一次调用时报告）
            if not self._load_time_reported:
                load_time_to_report = self.get_model_load_time()
                self._load_time_reported = True

            if not persons:
                total_time = time.perf_counter() - tic
                return {
                    "text": text,
                    "mapping": {},
                    "timing": {
                        "load_model_s": round(load_time_to_report, 4),
                        "ner_s": round(total_time, 4),
                        "pinyin_s": 0,
                        "replace_s": 0,
                        "total_s": round(total_time, 4),
                    }
                }

            # 转换人名为拼音
            mapping = {p: self.converter.convert(p) for p in persons}
            t2 = time.perf_counter()

            # 替换文本中的人名
            sorted_persons = sorted(persons, key=len, reverse=True)
            pattern = re.compile("|".join(map(re.escape, sorted_persons)))

            def get_pinyin(match: re.Match) -> str:
                return mapping[match.group(0)]

            romanized_text = pattern.sub(get_pinyin, text)
            # 处理中文顿号分隔的人名列表
            romanized_text = re.sub(r'、([A-Z][a-z]+(?: [A-Z][a-z]+)?)', r', \1', romanized_text)
            t3 = time.perf_counter()

            # t1-tic 是 NER 提取的总时间，它在首次运行时包含了模型加载时间
            ner_total_duration = t1 - tic

            return {
                "text": romanized_text,
                "mapping": mapping,
                "timing": {
                    "load_model_s": round(load_time_to_report, 4),
                    "ner_s": round(ner_total_duration, 4),
                    "pinyin_s": round(t2 - t1, 4),
                    "replace_s": round(t3 - t2, 4),
                    "total_s": round(t3 - tic, 4),
                }
            }

        except Exception as e:
            biz_logger.error(f"人名罗马化处理失败: {e}", exc_info=True)
            # 尝试在异常情况下也报告加载时间
            if not self._load_time_reported:
                load_time_to_report = self.get_model_load_time()
                self._load_time_reported = True
            return {
                "text": text,  # 出错时返回原文
                "mapping": {},
                "timing": {
                    "load_model_s": round(load_time_to_report, 4),
                    "ner_s": 0,
                    "pinyin_s": 0,
                    "replace_s": 0,
                    "total_s": round(time.perf_counter() - tic, 4),
                }
            }

    def romanize_texts(self, texts: List[str]) -> List[str]:
        """
        批量处理文本列表，进行人名罗马化

        参数:
            texts: 文本列表

        返回:
            罗马化后的文本列表
        """
        if not texts:
            return []

        biz_logger.info(f"开始批量人名罗马化处理，共 {len(texts)} 条文本")

        romanized_texts = []
        total_mappings = {}
        # 初始化 timing 字典，确保 load_model_s 只被记录一次
        total_timing = {
            "load_model_s": 0, "ner_s": 0, "pinyin_s": 0, "replace_s": 0, "total_s": 0
        }

        first_call = True
        for text in texts:
            result = self.romanize_text(text)
            romanized_texts.append(result["text"])

            total_mappings.update(result["mapping"])

            # 累加时间
            total_timing["ner_s"] += result["timing"]["ner_s"]
            total_timing["pinyin_s"] += result["timing"]["pinyin_s"]
            total_timing["replace_s"] += result["timing"]["replace_s"]
            total_timing["total_s"] += result["timing"]["total_s"]

            # 模型加载时间只在第一次调用时记录
            if first_call and result["timing"]["load_model_s"] > 0:
                total_timing["load_model_s"] = result["timing"]["load_model_s"]
                first_call = False

        if total_mappings:
            biz_logger.info(f"人名罗马化完成，共处理 {len(total_mappings)} 个人名")
            biz_logger.info(f"人名映射: {total_mappings}")
        else:
            biz_logger.info("未检测到需要罗马化的人名")

        # 将耗时四舍五入以便查看
        rounded_timing = {k: round(v, 4) for k, v in total_timing.items()}
        biz_logger.info(f"批量人名罗马化耗时统计: {rounded_timing}")

        return romanized_texts


# 全局单例实例
_name_romanizer_instance: Optional[NameRomanizer] = None


def get_name_romanizer(ner_type: str = "spacy") -> NameRomanizer:
    """
    获取人名罗马化处理器的全局单例实例

    参数:
        ner_type: NER 类型，支持 "spacy" 或 "hanlp"

    返回:
        NameRomanizer 实例
    """
    global _name_romanizer_instance
    if _name_romanizer_instance is None or _name_romanizer_instance.ner_type != ner_type.lower():
        biz_logger.info(f"创建新的 NameRomanizer 单例 (类型: {ner_type})")
        _name_romanizer_instance = NameRomanizer(ner_type=ner_type)
    return _name_romanizer_instance


if __name__ == "__main__":
    # 模拟所需的外部模块，以便脚本可以独立运行测试
    # 在您的实际环境中，请删除这个模拟部分
    try:
        from unittest.mock import MagicMock

        # --- 模拟模块 Start ---
        ModelLoader = MagicMock()
        ModelLoader.return_value.load_spacy_model.return_value = MagicMock()
        # 简单模拟spaCy返回结构
        doc_mock = MagicMock()
        ent1 = MagicMock()
        ent1.label_ = "PERSON"
        ent1.text = "刘双双"
        ent2 = MagicMock()
        ent2.label_ = "PERSON"
        ent2.text = "张敏"
        ent3 = MagicMock()
        ent3.label_ = "PERSON"
        ent3.text = "王怡婷"
        ent4 = MagicMock()
        ent4.label_ = "PERSON"
        ent4.text = "朱凡"
        ent5 = MagicMock()
        ent5.label_ = "PERSON"
        ent5.text = "曾志伟"
        ent6 = MagicMock()
        ent6.label_ = "PERSON"
        ent6.text = "欧阳娜娜"
        ent7 = MagicMock()
        ent7.label_ = "GPE"  # Not a person
        ent7.text = "先生"
        doc_mock.ents = [ent1, ent2, ent3, ent4, ent5, ent6, ent7]
        ModelLoader.return_value.load_spacy_model.return_value.return_value = doc_mock

        PypinyinConverter = MagicMock()
        # 简单模拟拼音转换
        pinyin_map = {
            "刘双双": "Liu Shuangshuang", "张敏": "Zhang Min", "王怡婷": "Wang Yiting",
            "朱凡": "Zhu Fan", "曾志伟": "Zeng Zhiwei", "欧阳娜娜": "Ouyang Nana",
        }
        PypinyinConverter.return_value.convert.side_effect = lambda name: pinyin_map.get(name, name)

        biz_logger = MagicMock()
        biz_logger.info = print
        biz_logger.error = print
        # --- 模拟模块 End ---

        # 测试代码
        demo_texts = [
            "供试品管理人员：刘双双。",
            "供试品配制人员：张敏、王怡婷、朱凡、曾志伟。",
            "项目负责人是欧阳娜娜，协助人员是单于先生和皇甫先生。"
        ]

        print("--- 测试人名罗马化处理器 (批量处理) ---")
        romanizer = get_name_romanizer("spacy")
        romanized_list = romanizer.romanize_texts(demo_texts)
        print("\n--- 批量处理结果 ---")
        for original, romanized in zip(demo_texts, romanized_list):
            print(f"原文: {original}")
            print(f"转换: {romanized}")

        print("\n\n--- 测试人名罗马化处理器 (单条处理) ---")
        # 重置实例以观察单条处理的计时
        _name_romanizer_instance = None
        romanizer_single = get_name_romanizer("spacy")
        for text in demo_texts:
            print(f"\n原文: {text}")
            result = romanizer_single.romanize_text(text)
            print(f"罗马化: {result['text']}")
            print(f"映射: {result['mapping']}")
            print(f"耗时: {result['timing']}")

    except ImportError:
        print("请安装 `unittest.mock` (通常是内置的) 或调整模拟代码以进行测试。")