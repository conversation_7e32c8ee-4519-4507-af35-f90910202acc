# hf_native_translator.py
# -*- coding: utf-8 -*-

import torch
from transformers import M2M100ForConditionalGeneration, M2M100Tokenizer
from typing import List
from transformers import AutoConfig, M2M100ForConditionalGeneration

class HFNativeTranslator:
    """
    使用 HuggingFace Transformers 原生推理 (M2M100)。
    合并 tokenize + model.generate + decode。
    """

    def __init__(self,
                 model_dir: str,
                 src_lang: str = "en",
                 tgt_lang: str = "zh",
                 device: str = "cuda",
                 device_index: int = 0,
                 max_length: int = 128,
                 beam_size: int = 4):
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self.max_length = max_length
        self.beam_size = beam_size

        # 构建 device
        if torch.cuda.is_available() and device.lower() == "cuda":
            self.device = torch.device(f"cuda:{int(device_index)}")
        else:
            self.device = torch.device("cpu")

        # 初始化 tokenizer & model
        config = AutoConfig.from_pretrained(model_dir)
        if config.early_stopping is None:
            config.early_stopping = True
        self.tokenizer = M2M100Tokenizer.from_pretrained(model_dir)
        self.model = M2M100ForConditionalGeneration.from_pretrained(
            model_dir, config=config, ignore_mismatched_sizes=True
        ).to(self.device)

        # 更新生成配置，确保 beam search 参数生效
        self.model.generation_config.num_beams = self.beam_size
        self.model.generation_config.num_return_sequences = self.beam_size

    def translate_batch(self, texts: List[str]) -> List[List[str]]:
        """
        一次性翻译一批文本，返回每个输入对应的候选翻译列表（beam_size 个候选）
        """
        if not texts:
            return []
        # 设置 tokenizer 的源语言
        self.tokenizer.src_lang = self.src_lang

        # 编码
        encoded = self.tokenizer(
            texts,
            max_length=self.max_length,
            padding=True,
            truncation=True,
            return_tensors="pt"
        )
        encoded = {k: v.to(self.device) for k, v in encoded.items()}

        # 生成
        with torch.no_grad():
            generated = self.model.generate(
                **encoded,
                forced_bos_token_id=self.tokenizer.get_lang_id(self.tgt_lang),
                max_length=self.max_length,
                num_beams=self.beam_size,            # 使用 beam search
                num_return_sequences=self.beam_size,  # 返回 beam_size 个候选翻译
                early_stopping=True,                  # 显式指定 early_stopping
                # no_repeat_ngram_size=3                # 防止重复 n-gram（根据需要调整）
            )

        # 解码：将生成的 token 序列批量转换为字符串，
        # 因为生成结果是按 beam 数量排布的，所以需要按每个输入分组
        outputs = self.tokenizer.batch_decode(generated, skip_special_tokens=True)
        grouped = [outputs[i * self.beam_size:(i + 1) * self.beam_size] for i in range(len(texts))]
        return grouped

    def translate(self, text_or_texts):
        if isinstance(text_or_texts, str):
            text_or_texts = [text_or_texts]
        return self.translate_batch(text_or_texts)
    
def compute_bleu_scores(translations, references):
    # 计算 BLEU 分数
    bleu = sacrebleu.corpus_bleu(translations, [references])
    return bleu.score

def test_bleu(
    model_dir: str,
    cache_file: str = "/home/<USER>/data/src-formatted_tgt-tgt.json",
    src_file: str = "/home/<USER>/data/cache/yxcdata.en-zh.devtst",
    batch_size: int = 30,
):
    """
    1) 一次性批量调用 HFNativeTranslator.translate，生成翻译并保存到缓存；
    3) 计算并打印 sacreBLEU 分数。
    """
    # 创建翻译器实例
    translator = HFNativeTranslator(
        model_dir=model_dir,
        src_lang="en",
        tgt_lang="zh",
        device="cuda",
        device_index=7,
        max_length=512,
        beam_size=4,  # 只取最佳结果
    )



    # 加载 sacrebleu
    # bleu_scorer = evaluate.load("sacrebleu")
    # sacrebleu.corpus_bleu(src_file, cache_file)


    src_texts = []
    formatted_refs = []  # sacrebleu 要求的 [[ref1], [ref2], ...]
    tgt_texts = []

    # 2. 从原始 devtst 文件读取 src+ref
    with open(src_file, "r", encoding="utf-8") as f:
        for line in (f.readlines()[:100]):
            data = json.loads(line.strip())["translation"]
            src_texts.append(data["en"])
            # sacrebleu 需要引用为列表的列表
            formatted_refs.append([data["zh"]])

    # 批量翻译
    for i in tqdm(range(0, len(src_texts), batch_size), '翻译进度'):
        batch = src_texts[i : i + batch_size]
        beams = translator.translate(batch)  # 返回 List[List[str]]
        # beam_size=1 时，每个子列表只有一个元素
        tgt_texts.extend([b[0] for b in beams])

    # 3. 缓存到文件
    with open(cache_file, "w", encoding="utf-8") as f:
        for src, ref, tgt in zip(src_texts, formatted_refs, tgt_texts):
            line = {"translation": {"src": src, "formatted_tgt": ref[0], "tgt": tgt}}
            print(line)
            json.dump(line, f, ensure_ascii=False)
            f.write("\n")

    # 4. 计算并打印 BLEU
    # result = bleu_scorer.compute(predictions=tgt_texts, references=formatted_refs)
    # 修正：sacrebleu需要的格式是 translations 为列表，references 为列表的列表
    refs_transposed = list(zip(*formatted_refs))  # 转置引用列表
    # bleu_score = sacrebleu.corpus_bleu(tgt_texts, refs_transposed).score
    # bleu = BLEU().corpus_score(translations, [test_ref]).score

    # bleu_score = BLEU().corpus_score(hypotheses=tgt_texts, references=refs_transposed).score
    metric = load_metric("sacrebleu")
    bleu_score = metric.compute(predictions=tgt_texts, references=refs_transposed)['score']


    print(f"BLEU 分数: {bleu_score:.2f}")
    return bleu_score
    

    ######### 测试 ##########
#     test_src_texts = [
#         "本申请要求于2023年2月17日递交的中国专利申请第202310153996.9号的优先权，在此全文引用上述中国专利申请公开的内容以作为本申请的一部分。",
#         "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；",
#         "<__TERM001>共记录103例（80.5%）<__TERM002>，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。",
#         "[__NTE001]因为空间分辨率较低、技术要求高及紧急情况下不适宜应用等缺点，在急性[_NTE_02]诊断中不作为一线诊断方法。",        "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
#         """AE 免疫性肠炎
# AE 尿蛋白阳性
# AE 尿蛋白阳性 1级
# AE 尿蛋自阳性 1级结束
# AE 尿蛋白阳性1级结束
# AE 凝血酶原时间延长
# AE 贫血
# AE 贫血加重""",
#         """供试品管理人员：刘双双
# 供试品配制人员：张敏、王怡婷、朱凡、周颖琪、张恬、张瑜
# 供试品分析：胡晓、王超超、陈泽政、张伟伟
# 张敏
# 王怡婷
# 朱凡""",
#         """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病“全身性重症肌无力”，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
#         "人工智能技术正在改变我们的生活方式。",
#         "风险管理文档",
#         "风险管理文档。",
#         "处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。",
#         "停止",
#         "除药明和泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商",
#         """金额合计(大写)壹佰陆拾壹元贰角陆分
# 金额合计(大写)壹佰贰拾捌元伍角伍分
# 金额合计(大写)贰佰零贰元捌角壹分
# 金额合计(大写)柒拾壹元叁角伍分
# 金额合计(大写)壹拾贰元整
# 金额合计(大写)肆仟柒佰伍拾陆元肆角柒分
# 金额合计(大写)柒拾陆元壹角肆分
# 金额合计(大写)壹佰捌拾元伍角捌分
# 金额合计(大写)壹佰肆拾玖元陆角叁分
# 金额合计(大写)壹佰捌拾元壹角
# 金额合计(大写)壹佰伍拾伍元柒角壹分
# 金额合计(大写)壹佰玖拾肆元伍角
# 金额合计(大写)叁佰肆拾肆元伍角
# 金额合计(大写)壹佰壹拾元伍角肆分
# 金额合计(大写)柒拾柒元零壹分
# 金额合计(大写)捌拾肆元伍角柒分
# 金额合计(大写)陆拾壹元壹角柒分
# 金额合计(大写)叁拾贰元叁角玖分
# 金额合计(大写)壹拾元整
# 金额合计(大写)壹佰柒拾捌元捌角捌分
# 金额合计(大写)壹佰柒拾柒元整
# 金额合计(大写)玖拾伍元伍角""",
#         "就诊类型：门诊，结算方式：支付宝支付：61. 17",
#         "否",
#         """附录K
# (规范性)
# 6.7中未涵盖的绝缘要求""",
#         "苏州禾川化学技术服务有限公司",
#         """【禾川官网】""",
#         """【禾川官微】""",
#         """<For the seal, refer to the source document>
# 【禾川官网】
# 【禾川官微】
# 苏州禾川化学技术服务有限公司
# 苏州禾川化学技术服务有限公司(以下简称本公司)为提供符合下述条款的测试和报告，而接受有关样品和货品，本公司基于下述条款提供服务，下述条款为本公司与""",
#         """该患因“咳嗽，咳痰三十余年，加重十余天”于2019年11月05日入院，血钾（K+）：3.1mmol/L，血钠（Na+）：141mmol/L，氧分压PO2）：71mmHg，氧化碳分压（PCO2）：38mmHg，二氧化碳总量（TCO2）：28.70mmo1/L.临床诊断为慢性阻塞性肺疾病急性加重期，于2019年11月07日给予吸入用复方异内托溴铵溶液2.5ml雾化吸入，一日两次，于2019年11月07日晚出现手抖，怀疑药物不良反屈引起，给予停药处置，于2019年11月8日好转，未再接触怀疑药品。""",
#         """患者因“1.肺原位癌2.胸膜钙化”于2023.4.4来我院住院治疗。""",
#         """患儿面部红疹于3日10：00自行消退，无其它不适。""",
#         """化学品及企业标识(chemical product and company identification)""",
#         """品牌
# 舒路通®(SurfLubri®)""",
#         """患儿于11月15日使用吸入用异丙托溴按溶液2m1+0.9%NS·2m1进行雾化吸入治疗后出现颜面、躯干可见红色皮疹，偶有咳嗽，无发热，无抽搐，无吼喘、呼吸困难，停止雾化后逐步恢复，查体：T36.5℃，P·110次/分，R·28次/分，神清、神可，颜面及躯干可见少许红色皮疹，唇周无绀，双肺呼吸音粗，未闻及干、湿啰音，心音有力，律齐，心前区未闻及明显杂音，腹软，肠鸣音3-4次/分，肢暖，处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。""",
#         """无锡泰格医药科技有限公司
# (除药明泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商)""",
#         """比利时""",
#         """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病“全身性重症肌无力”，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
#         """本品的制剂生产厂是位于德国Vetter Pharma-Fertigung GmbH& Co. KG公司，原液生产厂是位于中国的无锡药明生物技术股份有限公司，III期临床试验阶段和上市注册阶段生产厂一致，并供应全球。""",
#         """为了持续改进,提升产品性能,拟评估外周高压球囊扩张导管的充卸压时间的相关数据,拟制定充卸压时间的标准。""",
#         """一项多中心、开放标签、随机对照III期临床研究：比较FS-1502和T-DM1在HER2阳性不可手术切除的局部晚期或转移性乳腺癌患者中的疗效和安全性""",
#         """共2个治疗组，治疗组1：患者将接受FS-1502联合斯鲁利单抗+化疗(5-FU/卡培他滨)治疗；治疗组2：患者将接受FS-1502联合斯鲁利单抗治疗；""",
#         """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。""",
#         """FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
#         """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。
# FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
#         """**你好*""",
#         """阿奇霉素...红霉素.….克拉霉素...地红霉素""",
#         """要求患者在半卧或仰卧休息至少10分钟后进行采集，连续采集3次，每次间隔至少1分钟，记录数据（QT间期、QTcF间期、PR间期、QRS间期、RR间期)。""",
#         "白细胞计数0.44*10^9/L，中性粒细胞数0.16*10^9/L，考虑发生骨髓抑制不良反应，一般生命征平稳，予升白、输血小板处理。",
#         "Abstract：Objective   To establish the national standard materials of testosterone in frozen human serum，    to evaluate the accuracy，and to promote the standardization of testosterone assay. Methods Serum samples without lipemia，hemolysis and jaundice were collected. After multiple filtration sterilization，serum pools were packed into ampoules in 2-level candidate materials and were stored in -70 ℃. The homogeneity for candidate material was evaluated by single factor analysis of variance，and the stability was evaluated by linear regression analysis. The values of candidate material were assigned by reference method，and the uncertainty was calculated. The commutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. Conclusions The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.",
#         "利用0.035in导丝及造影导管置入输送导管头端置于右侧颈总动脉中段，退出泥鳅导丝及造影导管，造影示：右侧大脑中动脉M1段闭塞；利用0.014in微导丝及微导管置入远端通路导管于C2段远端，置入0.014in微导丝和微导管在路图指引下顺利通过右侧大脑中动脉闭塞部位，远端置于M2段中部，退出微导丝，行微导管造影显示：M2段中远端血流缓慢，沿微导管置入5.0*33mm取栓支架在大脑中动脉M1-2段顺利释放，复查造影显示：右侧大脑中动脉再通，未见明显造影剂渗出和动脉栓塞，利用锚定技术将远端通路导管置于大脑中动脉M1段，持续进行抽吸，3分钟后回收支架，并逐渐退出远端通路导管，取出血栓，沿输送导管复查血管造影示右侧大脑中动脉M1段闭塞；重复上述抽拉结合取栓1次后复查造影示：右侧大脑中动脉M1段再通，M2段局部可见血栓影；利用微导丝微导管将远端通路导管置于颈内动脉C3段，复查造影示：大脑中动脉未见明显血栓影，右侧M4段小分支及大脑前动脉A3段小分支闭塞，考虑血栓溶解；",
#         "MRPA因为空间分辨率较低、技术要求高及紧急情况下不适宜应用等缺点，在急性PTE诊断中不作为一线诊断方法。",
#         """医疗器械经营质量管理规范
# 第一章总则
# 第一条为了加强医疗器械经营质量管理,规范医疗器械
# 经营活动,保证医疗器械安全、有效,根据《医疗器械监督管
# 理条例》《医疗器械经营监督管理办法》等法规规章的规定,
# 制定本规范。
# 第二条 本规范是医疗器械经营质量管理的基本要求。从
# 事医疗器械经营活动,应当在医疗器械采购、验收、贮存、销
# 售、运输、售后服务等全过程采取有效的质量管理措施,确保
# 医疗器械产品在经营过程中的质量安全与可追溯。
# 第三条 医疗器械经营企业应当严格执行本规范。""",
#         '自首次给药至应答的天数',
#         '患者可能无法接受最有效的抗菌药物。',
#         '由于产生抗红细胞、白细胞和血小板的抗体而引起的自身免疫性血液病，通常涉及免疫调节的全身性疾病，具有多系统疾病的临床表现[Miller 1983, Coppo 2004, Haas 2009]。',
#         '国家卫生健康委卫生发展研究中心',
#         'Monarch平台支气管镜检查2.2.3软件设计验证计划'
#     ]
#     test_batch_size = 5
#     for i in range(0, len(test_src_texts), test_batch_size):
#         test_batch_src_texts = test_src_texts[i:i+test_batch_size]
#         test_tgt_texts = translator.translate(test_batch_src_texts)
#         for test_src, test_beams in zip(test_batch_src_texts, test_tgt_texts):
#             print("原文:", test_src)
#             print("-" * 100)
#             for i, test_tgt in enumerate(test_beams, start=1):
#                 print(f"Beam {i}: {test_tgt}")
#                 print('-' * 100)
#             print("#" * 100)
#     ######### 测试 ##########
    # print("sacreBLEU:", result)
    # return result

def test(model_dir):
    print(model_dir)
    translator = HFNativeTranslator(
        model_dir=model_dir,
        src_lang="zh",
        tgt_lang="en",
        device="cuda",
        device_index=7,
        max_length=512,
        beam_size=4,  # 只取最佳结果
    )
    src_texts = [
        "本申请要求于2023年2月17日递交的中国专利申请第202310153996.9号的优先权，在此全文引用上述中国专利申请公开的内容以作为本申请的一部分。",
        "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；",
        "<__TERM001>共记录103例（80.5%）<__TERM002>，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。",
        "[__NTE001]因为空间分辨率较低、技术要求高及紧急情况下不适宜应用等缺点，在急性[_NTE_02]诊断中不作为一线诊断方法。",
        "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
        """AE 免疫性肠炎
AE 尿蛋白阳性
AE 尿蛋白阳性 1级
AE 尿蛋自阳性 1级结束
AE 尿蛋白阳性1级结束
AE 凝血酶原时间延长
AE 贫血
AE 贫血加重""",
        """供试品管理人员：刘双双
供试品配制人员：张敏、王怡婷、朱凡、周颖琪、张恬、张瑜
供试品分析：胡晓、王超超、陈泽政、张伟伟
张敏
王怡婷
朱凡""",
        """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病“全身性重症肌无力”，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
        "人工智能技术正在改变我们的生活方式。",
        "风险管理文档",
        "风险管理文档。",
        "处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。",
        "停止",
        "除药明和泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商",
        """金额合计(大写)壹佰陆拾壹元贰角陆分
金额合计(大写)壹佰贰拾捌元伍角伍分
金额合计(大写)贰佰零贰元捌角壹分
金额合计(大写)柒拾壹元叁角伍分
金额合计(大写)壹拾贰元整
金额合计(大写)肆仟柒佰伍拾陆元肆角柒分
金额合计(大写)柒拾陆元壹角肆分
金额合计(大写)壹佰捌拾元伍角捌分
金额合计(大写)壹佰肆拾玖元陆角叁分
金额合计(大写)壹佰捌拾元壹角
金额合计(大写)壹佰伍拾伍元柒角壹分
金额合计(大写)壹佰玖拾肆元伍角
金额合计(大写)叁佰肆拾肆元伍角
金额合计(大写)壹佰壹拾元伍角肆分
金额合计(大写)柒拾柒元零壹分
金额合计(大写)捌拾肆元伍角柒分
金额合计(大写)陆拾壹元壹角柒分
金额合计(大写)叁拾贰元叁角玖分
金额合计(大写)壹拾元整
金额合计(大写)壹佰柒拾捌元捌角捌分
金额合计(大写)壹佰柒拾柒元整
金额合计(大写)玖拾伍元伍角""",
        "就诊类型：门诊，结算方式：支付宝支付：61. 17",
        "否",
        """附录K
(规范性)
6.7中未涵盖的绝缘要求""",
        "苏州禾川化学技术服务有限公司",
        """【禾川官网】""",
        """【禾川官微】""",
        """<For the seal, refer to the source document>
【禾川官网】
【禾川官微】
苏州禾川化学技术服务有限公司
苏州禾川化学技术服务有限公司(以下简称本公司)为提供符合下述条款的测试和报告，而接受有关样品和货品，本公司基于下述条款提供服务，下述条款为本公司与""",
        """该患因“咳嗽，咳痰三十余年，加重十余天”于2019年11月05日入院，血钾（K+）：3.1mmol/L，血钠（Na+）：141mmol/L，氧分压PO2）：71mmHg，氧化碳分压（PCO2）：38mmHg，二氧化碳总量（TCO2）：28.70mmo1/L.临床诊断为慢性阻塞性肺疾病急性加重期，于2019年11月07日给予吸入用复方异内托溴铵溶液2.5ml雾化吸入，一日两次，于2019年11月07日晚出现手抖，怀疑药物不良反屈引起，给予停药处置，于2019年11月8日好转，未再接触怀疑药品。""",
        """患者因“1.肺原位癌2.胸膜钙化”于2023.4.4来我院住院治疗。""",
        """患儿面部红疹于3日10：00自行消退，无其它不适。""",
        """化学品及企业标识(chemical product and company identification)""",
        """品牌
舒路通®(SurfLubri®)""",
        """患儿于11月15日使用吸入用异丙托溴按溶液2m1+0.9%NS·2m1进行雾化吸入治疗后出现颜面、躯干可见红色皮疹，偶有咳嗽，无发热，无抽搐，无吼喘、呼吸困难，停止雾化后逐步恢复，查体：T36.5℃，P·110次/分，R·28次/分，神清、神可，颜面及躯干可见少许红色皮疹，唇周无绀，双肺呼吸音粗，未闻及干、湿啰音，心音有力，律齐，心前区未闻及明显杂音，腹软，肠鸣音3-4次/分，肢暖，处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。""",
        """无锡泰格医药科技有限公司
(除药明泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商)""",
        """比利时""",
        """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病“全身性重症肌无力”，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
        """本品的制剂生产厂是位于德国Vetter Pharma-Fertigung GmbH& Co. KG公司，原液生产厂是位于中国的无锡药明生物技术股份有限公司，III期临床试验阶段和上市注册阶段生产厂一致，并供应全球。""",
        """为了持续改进,提升产品性能,拟评估外周高压球囊扩张导管的充卸压时间的相关数据,拟制定充卸压时间的标准。""",
        """一项多中心、开放标签、随机对照III期临床研究：比较FS-1502和T-DM1在HER2阳性不可手术切除的局部晚期或转移性乳腺癌患者中的疗效和安全性""",
        """共2个治疗组，治疗组1：患者将接受FS-1502联合斯鲁利单抗+化疗(5-FU/卡培他滨)治疗；治疗组2：患者将接受FS-1502联合斯鲁利单抗治疗；""",
        """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。""",
        """FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
        """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。
FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
        """**你好*""",
        """阿奇霉素...红霉素.….克拉霉素...地红霉素""",
        """要求患者在半卧或仰卧休息至少10分钟后进行采集，连续采集3次，每次间隔至少1分钟，记录数据（QT间期、QTcF间期、PR间期、QRS间期、RR间期)。""",
        "白细胞计数0.44*10^9/L，中性粒细胞数0.16*10^9/L，考虑发生骨髓抑制不良反应，一般生命征平稳，予升白、输血小板处理。",
        "Abstract：Objective   To establish the national standard materials of testosterone in frozen human serum，    to evaluate the accuracy，and to promote the standardization of testosterone assay. Methods Serum samples without lipemia，hemolysis and jaundice were collected. After multiple filtration sterilization，serum pools were packed into ampoules in 2-level candidate materials and were stored in -70 ℃. The homogeneity for candidate material was evaluated by single factor analysis of variance，and the stability was evaluated by linear regression analysis. The values of candidate material were assigned by reference method，and the uncertainty was calculated. The commutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. Conclusions The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.",
        "利用0.035in导丝及造影导管置入输送导管头端置于右侧颈总动脉中段，退出泥鳅导丝及造影导管，造影示：右侧大脑中动脉M1段闭塞；利用0.014in微导丝及微导管置入远端通路导管于C2段远端，置入0.014in微导丝和微导管在路图指引下顺利通过右侧大脑中动脉闭塞部位，远端置于M2段中部，退出微导丝，行微导管造影显示：M2段中远端血流缓慢，沿微导管置入5.0*33mm取栓支架在大脑中动脉M1-2段顺利释放，复查造影显示：右侧大脑中动脉再通，未见明显造影剂渗出和动脉栓塞，利用锚定技术将远端通路导管置于大脑中动脉M1段，持续进行抽吸，3分钟后回收支架，并逐渐退出远端通路导管，取出血栓，沿输送导管复查血管造影示右侧大脑中动脉M1段闭塞；重复上述抽拉结合取栓1次后复查造影示：右侧大脑中动脉M1段再通，M2段局部可见血栓影；利用微导丝微导管将远端通路导管置于颈内动脉C3段，复查造影示：大脑中动脉未见明显血栓影，右侧M4段小分支及大脑前动脉A3段小分支闭塞，考虑血栓溶解；",
        "MRPA因为空间分辨率较低、技术要求高及紧急情况下不适宜应用等缺点，在急性PTE诊断中不作为一线诊断方法。",
        """医疗器械经营质量管理规范
第一章总则
第一条为了加强医疗器械经营质量管理,规范医疗器械
经营活动,保证医疗器械安全、有效,根据《医疗器械监督管
理条例》《医疗器械经营监督管理办法》等法规规章的规定,
制定本规范。
第二条 本规范是医疗器械经营质量管理的基本要求。从
事医疗器械经营活动,应当在医疗器械采购、验收、贮存、销
售、运输、售后服务等全过程采取有效的质量管理措施,确保
医疗器械产品在经营过程中的质量安全与可追溯。
第三条 医疗器械经营企业应当严格执行本规范。""",
        '自首次给药至应答的天数',
        '患者可能无法接受最有效的抗菌药物。',
        '由于产生抗红细胞、白细胞和血小板的抗体而引起的自身免疫性血液病，通常涉及免疫调节的全身性疾病，具有多系统疾病的临床表现[Miller 1983, Coppo 2004, Haas 2009]。',
        '国家卫生健康委卫生发展研究中心',
        'Monarch平台支气管镜检查2.2.3软件设计验证计划'
    ]
    batch_size = 5
    for i in range(0, len(src_texts), batch_size):
        batch_src_texts = src_texts[i:i+batch_size]
        tgt_texts = translator.translate(batch_src_texts)
        for src, beams in zip(batch_src_texts, tgt_texts):
            print("原文:", src)
            print("-" * 100)
            for i, tgt in enumerate(beams, start=1):
                print(f"Beam {i}: {tgt}")
                print('-' * 100)
            print("#" * 100)
        


if __name__ == "__main__":
    import os
    import evaluate
    import json
    from tqdm import tqdm
    import sacrebleu
    from sacrebleu.metrics import BLEU
    from datasets import load_dataset, load_metric, concatenate_datasets
    # metric = load_metric("sacrebleu")
    # metrresult = metric.compute(predictions=dechyp[currStart:currStart+rownum], references=decref[currStart:currStart+rownum])



    # model_dir = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-43/checkpoint-42000"
    model_dir = '/home/<USER>/data/models/trained/en2zh/m2m-1.2b-40/checkpoint-38000'
    # test(model_dir)
    test_bleu(model_dir)


    """
    python -m translator.t1
    """