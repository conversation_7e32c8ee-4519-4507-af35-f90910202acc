# From ubuntu:22.04
FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/ubuntu:22.04

ENV APP_NAME mt_server
ENV APP_HOME /root

COPY deb-sources.list /etc/apt/sources.list

RUN apt update
RUN apt install -y curl
RUN apt install -y wget
RUN apt install -y iputils-ping
RUN apt install -y telnet
RUN apt install -y unzip
RUN apt install -y vim
RUN apt install -y zip
RUN apt install -y build-essential libssl-dev zlib1g-dev
RUN apt install -y libncurses5-dev libsqlite3-dev
RUN apt install -y libgdbm-dev libdb5.3-dev
RUN apt install -y libreadline-dev libffi-dev libbz2-dev liblzma-dev
RUN apt install -y lsof

# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件

RUN mkdir -p $APP_HOME/$APP_NAME/bin && \
    mkdir -p $APP_HOME/$APP_NAME/conf && \
    mkdir -p $APP_HOME/$APP_NAME/target && \
    mkdir -p $APP_HOME/logs/app && \
    mkdir -p $APP_HOME/logs/supervisord


# 安装Python
ARG PYTHON_VER=3.10.0
ARG PYTHON_MAIN_VER=3.10
RUN wget -c "https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/drivers/Python-${PYTHON_VER}.tgz" -O /tmp/Python-$PYTHON_VER.tgz && \
	cd /tmp && tar xvf Python-$PYTHON_VER.tgz && cd Python-$PYTHON_VER && \
	./configure --prefix=/usr/local/python${PYTHON_MAIN_VER} --enable-optimizations &&  make -j$(nproc) && make altinstall
RUN echo 'export PATH=/usr/local/python${PYTHON_MAIN_VER}/bin:$PATH' >> ~/.bashrc && \
    ln -s /usr/local/python${PYTHON_MAIN_VER}/bin/python${PYTHON_MAIN_VER} /bin/python && \
    ln -s /usr/local/python${PYTHON_MAIN_VER}/bin/pip${PYTHON_MAIN_VER} /bin/pip && \
	rm -rf /tmp/Python-$PYTHON_VER /tmp/Python-$PYTHON_VER.tgz


# 转PDF中文乱码问题 https://blog.csdn.net/A___LEi/article/details/118113211
RUN apt-get update && \
    apt-get install -y language-pack-zh-hans xfonts-utils fontconfig && \
    locale-gen zh_CN.UTF-8 && \
    locale-gen && \
    wget https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/drivers/fonts.zip && unzip fonts.zip && cp -r fonts/* /usr/share/fonts/ && \
    rm -f fonts.zip && \
    cd /usr/share/fonts/ && mkfontscale && mkfontdir && fc-cache && fc-list :lang=zh


ENV LANG=zh_CN.UTF-8
ENV LANGUAGE=zh_CN:zh

# 将应用启动脚本、健康检查脚本、nginx配置文件复制到镜像中
COPY environment/common/app/conf/requirements.txt /home/<USER>/$APP_NAME/requirements.txt

# 安装依赖
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --upgrade pip && pip install -r /home/<USER>/$APP_NAME/requirements.txt
#RUN pip install --pre torch --index-url https://download.pytorch.org/whl/nightly/cu124

RUN pip install circus sacrebleu==2.5.1

ENV PATH "$PATH:$APP_HOME/.local/bin:/usr/local/python${PYTHON_MAIN_VER}/bin"

COPY environment/common/app/ $APP_HOME/$APP_NAME/
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini

RUN echo "$APP_HOME/$APP_NAME/bin/appctl.sh start" >> $APP_HOME/start.sh && \
    echo "$APP_HOME/$APP_NAME/bin/preload.sh" >> $APP_HOME/health.sh

# 设置文件操作权限
RUN chmod -R a+x ${APP_HOME}/$APP_NAME/bin/ && \
chmod +x ${APP_HOME}/*.sh

WORKDIR $APP_HOME/$APP_NAME

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]

