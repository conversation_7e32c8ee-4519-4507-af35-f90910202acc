from typing import List

from pydantic import BaseModel, Field


class Terminology(BaseModel):
    src_term: str = Field(..., title="source terminology", description="源语言术语")
    tgt_term: str = Field(..., title="target terminology", description="目标语言术语")
    is_case_sensitive: bool = Field(False, title="case sensitive", description="大小写是否敏感")
    # 是否不翻译
    is_not_translate: bool = Field(False, title="not translate", description="是否不翻译")
    # 术语替换占位符
    placeholder: str = Field(None, title="placeholder", description="术语替换占位符")


class TranslateRequest(BaseModel):
    src_text: str = Field(None, title="source text", description="source text to translate")
    src_text_list: List[str] = Field(..., title="source text list", description="source text list to translate")
    src_lang: str = Field(..., title="source language", description="source language to translate")
    tgt_lang: str = Field(..., title="target language", description="target language to translate")
    # deprecated
    term_list: List[List[str]] = Field(None, title="term_list", description="terms to translate")
    terms: List[Terminology] = Field(None, title="terms", description="terms to translate")
    appname: str = Field(..., title="app name", description="app name")
    app_secret: str = Field(..., title="app secret", description="app secret")
