import time

class TimeProfiler:
    def __init__(self, title='', desc='', profiling=True):
        """
        初始化计时器包装器
        :param title: 主体描述（将会传递给内部的 TimeTicker）
        :param desc: 本次计时的描述
        :param profiling: 是否开启计时
        """
        self.ticker = TimeTicker(title)
        self.desc = desc
        self.profiling = profiling

    def __enter__(self):
        if self.profiling:
            self.ticker.tick_s()
        return self

    def __exit__(self, exc_type, exc_value, tb):
        if self.profiling:
            self.ticker.tick_e(self.desc)


class TimeTicker:
    def __init__(self, title: str, unit='s', interval_mode=False, final_print=False):
        """
        初始化时间计数器
        :param title: 计时器的主体描述
        :param unit: 时间单位，可以是 's' 或 'ms'
        :param interval_mode: 是否采用间隔模式（目前仅用于 tick()）
        :param final_print: 如果为 True，则记录所有打点信息，后续调用 print_all() 输出
        """
        self._start_time = time.time()
        self._records: list[tuple[str, float]] = []
        self._interval_mode = interval_mode
        self._final_print = final_print
        self._unit = unit
        self._title = title

    def update(self):
        """更新开始计时时间"""
        self._start_time = time.time()

    def tick(self) -> float:
        """
        打点计时，返回上次打点以来的时间差
        """
        curr_time = time.time()
        elapsed = curr_time - self._start_time
        self._start_time = curr_time
        return elapsed if self._unit == 's' else elapsed * 1000

    def tick_s(self):
        """
        记录打点开始的时间
        """
        self._start_time = time.time()

    def tick_e(self, desc: str):
        """
        记录结束打点并输出计时信息
        :param desc: 对当前计时的描述
        """
        if not hasattr(self, '_start_time') or self._start_time is None:
            print("计时器错误：请先调用 tick_s()。")
            raise RuntimeError('计时器错误：请先调用 tick_s()。')

        elapsed = time.time() - self._start_time
        if self._unit != 's':
            elapsed *= 1000
        message = f"计时器:{self._title}:{desc}, 耗时: {elapsed:.2f} {self._unit}"
        if self._final_print:
            # 将结果记录下来，后续统一打印
            self._records.append((desc, elapsed))
        else:
            print(message)
        self._start_time = None

    def print_all(self):
        """
        输出所有记录的打点信息
        """
        for desc, elapsed in self._records:
            print(f"{self._title}:{desc}, 耗时: {elapsed:.2f} {self._unit}")


def profile(title, desc, profiling=True):
    """
    辅助函数：返回一个 TimeProfiler 对象，便于 with 语句使用
    """
    return TimeProfiler(title, desc, profiling)
