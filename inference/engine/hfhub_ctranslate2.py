import os
from typing import List, Any
import transformers
from hf_hub_ctranslate2 import MultiLingualTranslatorCT2fromHfHub
from logger.logger import biz_logger

class Ctranslate2:
    '''
    Adapter that exposes the original Ctranslate2-like interface
    but uses hf_hub_ctranslate2.MultiLingualTranslatorCT2fromHfHub under the hood.
    '''
    def __init__(
        self,
        model_path: str,
        token_path: str,
        src_lang: str = "zh",
        tgt_lang: str = "en",
        beam_num: int = 4,
        max_input_length: int = 512,
        max_decoding_length: int = 512,
        compute_type: str = "int8_float16",
        device: str = "cuda",
        device_index: int = 0,
        profiling: bool = True,
        num_hypotheses: int = 1,
    ):
        biz_logger.debug('-' * 100)
        biz_logger.debug(f"model_path: {model_path}")
        biz_logger.debug(f"token_path: {token_path}")
        biz_logger.debug(f"src_lang: {src_lang}")
        biz_logger.debug(f"tgt_lang: {tgt_lang}")
        biz_logger.debug(f"beam_num: {beam_num}")
        biz_logger.debug(f"max_input_length: {max_input_length}")
        biz_logger.debug(f"max_decoding_length: {max_decoding_length}")
        biz_logger.debug(f"compute_type: {compute_type}")
        biz_logger.debug(f"device: {device}")
        biz_logger.debug(f"device_index: {device_index}")
        biz_logger.debug(f"profiling: {profiling}")
        biz_logger.debug(f"num_hypotheses: {num_hypotheses}")
        biz_logger.debug('-' * 100)
        # preserve signature compatibility
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self.beam_num = beam_num
        self.max_input_length = max_input_length
        self.max_decoding_length = max_decoding_length
        self.num_hypotheses = num_hypotheses
        self.profiling = profiling

        # load tokenizer
        self.tokenizer = transformers.AutoTokenizer.from_pretrained(token_path, use_fast=True)

        # initialize hf_hub ctranslate2 model
        self._model = MultiLingualTranslatorCT2fromHfHub(
            model_name_or_path=model_path,
            tokenizer=self.tokenizer,
            device=device,
            device_index=device_index,
            compute_type=compute_type,
        )

    def tokenize(self, sequences: List[str]) -> List[List[str]]:
        '''
        Keep compatibility: splits each raw text into tokenizer tokens.
        '''
        return [self.tokenizer.tokenize(seq) for seq in sequences]

    def translate(self, token_lists: List[Any]) -> List[str]:
        '''
        Accepts either raw strings or token lists; returns list of generated strings.
        '''
        # Detect if input is tokenized (list of lists)
        if token_lists and isinstance(token_lists[0], list):
            # join tokens back to text
            texts = ["".join(tokens) for tokens in token_lists]
        else:
            texts = token_lists  # assume list of raw strings

        # call hf_hub generate API
        outputs = self._model.generate(
            texts,
            src_lang=[self.src_lang] * len(texts),
            tgt_lang=[self.tgt_lang] * len(texts),
            beam_size=self.beam_num,
            # num_hypotheses=self.num_hypotheses,
            max_input_length=self.max_input_length,
            max_decoding_length=self.max_decoding_length,
        )
        return outputs

    def detokenize(self, outputs: List[str]) -> List[List[str]]:
        '''
        Wrap each single output into a 1-beam list for compatibility.
        '''
        return [[out] for out in outputs]

    def translate_batch(self, sequences: List[str]) -> List[str]:
        '''
        Convenience: raw batch translate, skip explicit tokenize/detokenize.
        '''
        return self._model.generate(
            sequences,
            src_lang=[self.src_lang] * len(sequences),
            tgt_lang=[self.tgt_lang] * len(sequences),
            beam_size=self.beam_num,
            # num_hypotheses=self.num_hypotheses,
            max_input_length=self.max_input_length,
            max_decoding_length=self.max_decoding_length,
        )

    def __call__(self, text_or_texts):
        '''
        Allow Ctranslate2(...)"()" like original (single or batch).
        '''
        if isinstance(text_or_texts, str):
            return self.translate([text_or_texts])[0]
        return self.translate(text_or_texts)


# Example usage:
# translator = Ctranslate2(
#     model_path="/path/to/ctr2_model",
#     token_path="/path/to/tokenizer",
#     src_lang="zh",
#     tgt_lang="en",
#     beam_num=5,
#     max_decoding_length=512,
# )
# tokens = translator.tokenize(["你好世界"])
# hyps = translator.translate(tokens)
# results = translator.detokenize(hyps)
# print(results)
