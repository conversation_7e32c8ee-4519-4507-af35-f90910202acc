#!/bin/bash

# 激活 Conda 环境
# source ~/anaconda3/etc/profile.d/conda.sh
# conda activate mt_server_py310
source ~/.bashrc
pyenv activate my_server

# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_quant_2025021722
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_origin_2025021722
# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-45/checkpoint-230-ct2
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-45/checkpoint-230
# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-47/checkpoint-5000-ct2
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-47/checkpoint-5000
# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-50/checkpoint-64000-ct2
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-50/checkpoint-64000
# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-53/checkpoint-13000-ct2
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-53/checkpoint-13000
# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-54/checkpoint-5000-ct2
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-54/checkpoint-5000
# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-56/checkpoint-54000-ct2
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-56/checkpoint-54000
# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-57/checkpoint-172000-ct2
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-57/checkpoint-172000
# export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-59/checkpoint-74000-ct2
# export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-59/checkpoint-74000
export MODEL_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-60/checkpoint-66000-ct2
export TOKEN_PATH=/home/<USER>/data/models/trained/zh2en/m2m-1.2b-60/checkpoint-66000
export SOURCE_LANG=zh
export TARGET_LANG=en
export DEVICE_INDEX=7
# export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export MAX_LENGTH=512

gunicorn app:app --workers 1 --worker-class uvicorn.workers.UvicornH11Worker --worker-connections 1000 --bind 0.0.0.0:7860