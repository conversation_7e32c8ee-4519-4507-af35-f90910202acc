apiVersion: v1
kind: Service
metadata:
  name: medical-zh2en
  namespace: default
  labels:
    app: medical-zh2en
    tier: model
spec:
  ports:
  - port: 8000
    targetPort: 8000
    nodePort: 32080
  selector:
    app: medical-zh2en
    tier: model
  type: NodePort

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: medical-zh2en
  namespace: default
  labels:
    app: medical-zh2en
spec:
  selector:
    matchLabels:
      app: medical-zh2en
  replicas: 1
  template:
    metadata:
      labels:
        app: medical-zh2en
        tier: model
    spec:
      imagePullSecrets:
      - name: yiya-acr-key 

      containers:
        - name: medical-zh2en
          image: "yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/yiya/mt-server:202405121813"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: yiya-cm
          env:
            - name: MODEL_LANG
              value: zh2en
          resources:
            limits:
              cpu: 4
              memory: 8
              nvidia.com/gpu: 1
            request:
              cpu: 2
              memory: 6
              nvidia.com/gpu: 1
          ports:
            - containerPort: 8000
          readinessProbe:
            tcpSocket:
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 10
            successThreshold: 1
          livenessProbe:
            tcpSocket:
              port: 8000
            initialDelaySeconds: 120
            periodSeconds: 3


