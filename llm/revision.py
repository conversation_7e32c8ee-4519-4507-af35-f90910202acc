import requests
import json
from configurer.config_reader import get_fastgpt_ak,get_dify_unk_revision_ak
import logging
from logger.logger import biz_logger


logger = logging.getLogger(name=__name__)


def replace_unk_tokens(src_text, tgt_text) -> str:
    '''
    替换译文中的unk词
    :param src_text:
    :param tgt_text:
    :return:
    '''
    if not tgt_text or '<unk>' not in tgt_text:
        return tgt_text
    result = call_workflow('dify', src_text, tgt_text)
    biz_logger.warning('+' * 30)
    biz_logger.warning(f"\n原文：{src_text}\n译文：{tgt_text}\n润色：{result}")
    biz_logger.warning('+' * 30)
    return result


def call_workflow(workflow_type: str, src_text: str, tgt_text: str) -> str:
    """
    调用不同类型的工作流进行 unk 替换或润色
    :param workflow_type: 支持 'fastgpt'、'dify' 等
    :param src_text: 原文
    :param tgt_text: 初始译文
    :return: 处理后的文本
    """

    if workflow_type == 'dify':
        revision_ak = get_dify_unk_revision_ak()
        url = "https://f.yiya-ai.com/v1/workflows/run"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {revision_ak}'
        }
        payload = {
            "user": "abcd",
            "inputs": {
                "src_text": src_text,
                "tgt_text": tgt_text
            },
            "response_mode": "blocking"
        }
    elif workflow_type == 'fastgpt':
        revision_ak = get_fastgpt_ak()
        url = "http://rag.yiya-ai.com/api/v1/chat/completions"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {revision_ak}'
        }
        payload = {
            "chatId": "abcd",
            "stream": False,
            "detail": False,
            "messages": [
                {
                    "role": "user",
                    "content": f"原文：{src_text}\n译文：{tgt_text}"
                }
            ]
        }
    else:
        raise ValueError(f"Unsupported workflow type: {workflow_type}")
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        response.raise_for_status()
        response_data = response.json()

        if workflow_type == 'fastgpt':
            return response_data['choices'][0]['message']['content']
        elif workflow_type == 'dify':
            return response_data['data']['outputs']['tgt_text']

    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred: {http_err}")
    except requests.exceptions.ConnectionError as conn_err:
        logger.error(f"Connection error occurred: {conn_err}")
    except requests.exceptions.Timeout as timeout_err:
        logger.error(f"Timeout error occurred: {timeout_err}")
    except requests.exceptions.RequestException as req_err:
        logger.error(f"An error occurred: {req_err}")
    except KeyError as key_err:
        logger.error(f"Key error in response: {key_err}")

    logger.error(f"[replace_unk_tokens error] src_text:{src_text}, tgt_text:{tgt_text}")

    return tgt_text

if __name__ == '__main__':
    src_text = '9例肺转移患者肺部转移灶明显缩小,胸水量明显减少,胸闷、咳嗽明显好转'
    tgt_text = 'In 9 patients with pulmonary metastasis, the pulmonary metastases shrunk significantly, the volume of pleural effusion decreased significantly, and the chest distress and cough were significantly improved'
    print(call_workflow('dify',src_text, tgt_text))
