#!/bin/bash

# 检查是否至少提供了两个关键词
if [ "$#" -lt 2 ]; then
    echo "请至少提供两个关键词。例如：./kill.sh uvicorn 7862"
    exit 1
fi

# 构造查找命令，依次筛选包含所有关键词的行
CMD="ps -ef"
for KEYWORD in "$@"; do
    CMD+=" | grep \"$KEYWORD\""
done
CMD+=" | grep -v grep"

echo "正在查找同时包含关键词 '$*' 的进程..."
# 使用 eval 执行构造好的命令，并获取进程ID
PIDS=$(eval $CMD | awk '{print $2}')

if [ -z "$PIDS" ]; then
    echo "未找到与关键词 '$*' 匹配的进程。"
else
    echo "找到以下进程："
    eval $CMD
    # 杀死所有匹配的进程
    for PID in $PIDS; do
        echo "杀死进程 ID: $PID"
        kill -9 $PID
    done
    echo "所有匹配 '$*' 的进程已成功杀死。"
fi
