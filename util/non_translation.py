import re
from configurer.config_reader import get_no_translatable_elements, get_non_translation_exact_strings
from util.langident import Langident
from configurer.singleton import singleton
from logger.logger import biz_logger


@singleton
class NonTranslation:
    def __init__(self):
        biz_logger.info("初始化NonTranslation类")
        self.patterns = {
            'date1': r'^(?P<year>\d{4})[-/](?P<month>0[1-9]|1[012])[-/](?P<day>0[1-9]|[12][0-9]|3[01])$',  # YYYY-MM-DD 或 YYYY/MM/DD
            'date2': r'^(?P<day2>0[1-9]|[12][0-9]|3[01])[-/](?P<month2>0[1-9]|1[012])[-/](?P<year2>\d{4}|\d{2})$',  # DD-MM-YYYY / DD/MM/YYYY / DD-MM-YY / DD/MM/YY
            'date3': r'^(?P<month3>0[1-9]|1[012])/(?P<day3>0[1-9]|[12][0-9]|3[01])$',  # MM/DD (无年份)
            'date4': r'^(?P<day4>0[1-9]|[12][0-9]|3[01])/(?P<month4>0[1-9]|1[012])$',  # DD/MM (无年份)
            'times': r'\b\d{1,2}:\d{2}(:\d{2})?( ?[AP]M)?\b',             # 时间格式
            'emails': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z]{2,}\b', # 邮箱地址
            'urls': r'https?://[\w.-]+(?:/[\w.-]*)?',                     # URL链接
            'currency_symbols': r'[$€£¥]',                                # 常见货币符号
            'numbers': r'^\d+(\.\d+)?$',                                  # 数字，包括小数
            'numbers2': r'\(?\d+\)?',                                    # 带括号的数字，如1) 2)
            'hashtags': r'#\w+',                                         # Twitter风格的标签
            'mentions': r'@\w+',                                         # Twitter风格的提及
            'code_snippets': r'`[^`]*`',                                 # 单行代码片段
            # 'all_caps': r'^[A-Z\s]+$',                                # 大写
            # "uper": "^['A-Z0-9._ ]{2,}$"
        }
        # 固定字符串非译列表，用于快速修复线上bug
        self.exact_match_strings = [
            "Doc.Ref. No.DER-GDL-001-03"
        ]
        # 从配置中获取固定字符串非译列表
        self.update_exact_match_strings()
        biz_logger.info("NonTranslation类初始化完成")
    
    def update_exact_match_strings(self):
        """从配置中更新固定字符串非译列表"""
        biz_logger.info("开始更新固定字符串非译列表")
        config_strings = get_non_translation_exact_strings()
        if config_strings:
            self.exact_match_strings.extend(config_strings)
            # 去重
            self.exact_match_strings = list(set(self.exact_match_strings))
            biz_logger.info(f"已更新固定字符串非译列表: {self.exact_match_strings}")
        else:
            biz_logger.info("未从配置中获取到固定字符串非译列表")

    def is_non_translatable(self, text, src_lang, tgt_lang, langident: Langident):
        biz_logger.info(f"检查文本是否为非可译元素: {text}")
        # 检查是否在固定字符串非译列表中
        if text in self.exact_match_strings:
            biz_logger.info(f"文本在固定字符串非译列表中，返回原文: {text}")
            return True
        elif self.is_non_translation(text, src_lang, tgt_lang, langident):
            biz_logger.info("文本被识别为非翻译内容")
            return True
        elif self.is_non_elements(text):
            biz_logger.info("文本被识别为非可译元素")
            return True
        biz_logger.info("文本为可译内容")
        return False

    def is_non_elements(self, text):
        """
        返回 True 表示非译元素，False 表示可译元素。
        """
        biz_logger.info("开始检查文本是否匹配非可译元素模式")
        patterns2 = get_no_translatable_elements()
        
        if patterns2:
            patterns = patterns2
            biz_logger.info("使用配置中的非可译元素模式")
        else:
            patterns = self.patterns
            biz_logger.info("使用默认的非可译元素模式")

        for pattern_name, pattern in patterns.items():
            # 完全匹配则视为非译元素
            # IGNORECASE 忽略大小写
            # UNICODE 使用 Unicode 字符类
            if re.fullmatch(pattern, text, re.IGNORECASE | re.UNICODE):
                biz_logger.info(f"文本匹配非可译元素模式 '{pattern_name}'")
                return True

        biz_logger.info("文本不匹配任何非可译元素模式")
        return False

    def is_non_translation(self, text, src_lang, tgt_lang, langident: Langident):
        """
        返回 True 表示非译元素，False 表示可译元素。
        """
        biz_logger.info(f"检查文本是否为非翻译内容，语向: {src_lang} -> {tgt_lang}")
        if src_lang == 'zh' and tgt_lang == 'en' and (not langident.contains_chs(text)):
            biz_logger.info(f"语向：{src_lang} -> {tgt_lang}，原文中不包含中文，返回原文: {text}")
            return True
        biz_logger.info("文本不符合非翻译条件")
        return False
    
if __name__ == '__main__':
    from configurer.singleton import Nacos
    Nacos()
    ld = Langident()
    print(ld.eng_ratio("结果候选材料水平I和水平II的均一性F值分别为1.448 5和1.569 6，均小于F0.05，水平I和水平II在-20°C下均稳定至少30d，水平I和水平II的均一性F值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%的置信限内，表明候选材料具有良好的互换性。"))