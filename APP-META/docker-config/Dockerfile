FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/mt-server-base:202501151140

RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --upgrade pip && \
    pip install wordfreq hf_hub_ctranslate2==2.13.1 pypinyin==0.54.0 numpy==1.26.4 torch==2.2.2 typing_extensions==4.11.0 nltk
    # pip install wordfreq hf_hub_ctranslate2==2.13.1 spacy==3.7.5 pypinyin==0.54.0 numpy==1.26.4 torch==2.2.2 typing_extensions==4.11.0 nltk &&
    # python -m spacy download zh_core_web_sm --root-user-action ignore

# 设置代理
ENV http_proxy="http://proxy.tigermed.local:53128"
ENV https_proxy="http://proxy.tigermed.local:53128"
ENV ftp_proxy="http://proxy.tigermed.local:53128"
ENV no_proxy="localhost,127.0.0.1"

# 下载 punkt 模型
RUN python -m nltk.downloader punkt

# 下载完成后关闭代理
ENV http_proxy=""
ENV https_proxy=""
ENV ftp_proxy=""
ENV no_proxy=""

# 指定 NLTK_DATA 环境变量，程序就能自动找到
# ENV NLTK_DATA /usr/local/share/nltk_data

ENV APP_NAME mt_server
ENV APP_HOME /root

WORKDIR $APP_HOME/$APP_NAME

COPY $APP_NAME.tgz $APP_HOME/$APP_NAME/target/

CMD ["/bin/bash", "-c", "/root/start.sh"]
