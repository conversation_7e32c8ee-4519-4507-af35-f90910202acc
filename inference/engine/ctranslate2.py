import os
from typing import List, Any

import ctranslate2
from transformers import AutoTokenizer

from inference.engine import profiler


class Ctranslate2:
    '''
    使用ctranslate2的翻译推理引擎
    '''

    def __init__(self, model_path, token_path, src_lang, tgt_lang, beam_num, max_decoding_length, compute_type="auto", device='cuda', device_index=0, profiling=True, num_hypotheses=1):
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self._infer_engine = ctranslate2.Translator(model_path, compute_type=compute_type, device=device, device_index=device_index)
        self._tokenizer = AutoTokenizer.from_pretrained(token_path)
        self._target_lang_tokens = [self._tokenizer.lang_code_to_token[tgt_lang]]  # 目标语言的token
        self._profiling = profiling if os.getenv('TRANS_PROFILING', None) is not None else False  # 是否开启性能分析
        self._beam_num = beam_num  # 束搜索宽度
        self._max_decoding_length = max_decoding_length  # 最大解码长度
        self._num_hypotheses = num_hypotheses  # 生成数量

    def tokenize(self, sequences: List[str]) -> List[List[Any]]:
        '''
        将sequence进行tokenize编码为token数据
        '''
        with profiler.TimeProfiler('Ctranslate2', f'{self.src_lang}-{self.tgt_lang} 模型分词', profiling=self._profiling):
            src_tokens_list = [self._tokenizer.convert_ids_to_tokens(self._tokenizer.encode(seq)) for seq in sequences]
            return src_tokens_list

    # def detokenize(self, tokens_list: List[List[Any]]) -> List[str]:      
    #     '''
    #     将token数据解码为sequence数据
    #     '''        
    #     with profiler.TimeProfiler('Ctranslate2', f'{self.src_lang}-{self.tgt_lang} 模型解码', profiling=self._profiling):
    #         tgt_sequences = [self._tokenizer.decode(self._tokenizer.convert_tokens_to_ids(tgt_tokens.hypotheses[0][1:])) for tgt_tokens in tokens_list]
    #         return tgt_sequences
    #         # return ([len(src_tokens) for src_tokens in src_tokens_list], [len(tgt_tokens.hypotheses[0][1:])for tgt_tokens in tgt_tokens_list], tgt_sequences)

    def detokenize(self, tokens_list: List[Any]) -> List[List[str]]:
        '''
        将 token 数据解码为多个 beam 的 sequence 数据，返回一个二维列表，
        每个内部列表包含对应源句子的所有 beam 翻译。
        '''
        with profiler.TimeProfiler('Ctranslate2', f'{self.src_lang}-{self.tgt_lang} 模型解码', profiling=self._profiling):
            all_sequences = []
            for result in tokens_list:
                beams = []
                # 直接遍历所有候选译文
                for i, hyp in enumerate(result.hypotheses):
                    # 如果需要剔除起始标记（例如语言标记），使用 hyp[1:]，否则可直接使用 hyp
                    token_ids = self._tokenizer.convert_tokens_to_ids(hyp)[1:]
                    seq = self._tokenizer.decode(token_ids)
                    # 如果返回了分数且数量匹配，则附加分数信息
                    if result.scores and len(result.scores) > i:
                        beams.append(f"{seq} (score: {result.scores[i]})")
                    else:
                        beams.append(seq)
                all_sequences.append(beams)
            return all_sequences


    def translate(self, tokens_list: List[List[Any]]):
        '''
        翻译token列表
        '''
        with profiler.TimeProfiler('Ctranslate2', f'{self.src_lang}-{self.tgt_lang} 模型翻译, token列表大小:{len(tokens_list)}', profiling=self._profiling):
            tgt_tokens_list = self._infer_engine.translate_batch(
                tokens_list,
                target_prefix=[self._target_lang_tokens] * len(tokens_list),
                # beam_size=self._beam_num,
                max_decoding_length=self._max_decoding_length,
                # # 设置解码策略
                # patience=1,
                # # 设置生成数量
                # num_hypotheses=self._num_hypotheses,
                # # 设置长度惩罚，鼓励译文较长，避免过早结束遗漏占位符
                # length_penalty=1.2,
                # # 覆盖惩罚使模型尽量覆盖所有输入（包括占位符）
                # coverage_penalty=0.1,
                # # 重复惩罚，避免输出重复但不要设太高，防止抑制后续占位符生成
                # repetition_penalty=1.1,
                # # 禁止重复3-gram
                # no_repeat_ngram_size=3,
                # # 设置最小解码长度，确保输出足够长，比如包含所有占位符
                # min_decoding_length=1,
                # # 使用vmap优化
                # use_vmap=False,
                # # 返回得分
                # return_scores=False
            )
            return tgt_tokens_list


if __name__ == "__main__":
    import torch
    # 示例用法
    model_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_quant_2025021722"
    token_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_origin_2025021722"
    src_lang = "zh"
    tgt_lang = "en"
    
    # 初始化翻译引擎
    translator = Ctranslate2(
        model_path=model_path,
        token_path=token_path,
        src_lang=src_lang,
        tgt_lang=tgt_lang,
        beam_num=5,
        max_decoding_length=512,
        device="cuda" if torch.cuda.is_available() else "cpu",
        device_index=7 if torch.cuda.is_available() else None,
        num_hypotheses=5
    )
    
    # 示例文本
    texts = [
        # "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
        # "人工智能技术正在改变我们的生活方式。",
        # "这是一个简单的测试句子。",
        # "风险管理文档",
        # "就诊类型：门诊，结算方式：支付宝支付：61. 17",
        # "否",
#         """附录K
# (规范性)
# 6.7中未涵盖的绝缘要求""",
        # "苏州禾川化学技术服务有限公司",
        # """【禾川官网】""",
        # """【禾川官微】""",
#         """<For the seal, refer to the source document>
# 【禾川官网】
# 【禾川官微】
# 苏州禾川化学技术服务有限公司
# 苏州禾川化学技术服务有限公司(以下简称本公司)为提供符合下述条款的测试和报告，而接受有关样品和货品，本公司基于下述条款提供服务，下述条款为本公司与""",
        # """该患因“咳嗽，咳痰三十余年，加重十余天”于2019年11月05日入院，血钾（K+）：3.1mmol/L，血钠（Na+）：141mmol/L，氧分压PO2）：71mmHg，氧化碳分压（PCO2）：38mmHg，二氧化碳总量（TCO2）：28.70mmo1/L.临床诊断为慢性阻塞性肺疾病急性加重期，于2019年11月07日给予吸入用复方异内托溴铵溶液2.5ml雾化吸入，一日两次，于2019年11月07日晚出现手抖，怀疑药物不良反屈引起，给予停药处置，于2019年11月8日好转，未再接触怀疑药品。""",
        # """患者因“1.肺原位癌2.胸膜钙化”于2023.4.4来我院住院治疗。""",
        # """患儿面部红疹于3日10：00自行消退，无其它不适。""",
#         """化学品及企业标识(chemical product and company identification)""",
#         """品牌
# 舒路通®(SurfLubri®)""",
        # """患儿于11月15日使用吸入用异丙托溴按溶液2m1+0.9%NS·2m1进行雾化吸入治疗后出现颜面、躯干可见红色皮疹，偶有咳嗽，无发热，无抽搐，无吼喘、呼吸困难，停止雾化后逐步恢复，查体：T36.5℃，P·110次/分，R·28次/分，神清、神可，颜面及躯干可见少许红色皮疹，唇周无绀，双肺呼吸音粗，未闻及干、湿啰音，心音有力，律齐，心前区未闻及明显杂音，腹软，肠鸣音3-4次/分，肢暖，处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。""",
#         """无锡泰格医药科技有限公司
# (除药明泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商)""",
        # """比利时""",
        # """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病“全身性重症肌无力”，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
        # """本品的制剂生产厂是位于德国Vetter Pharma-Fertigung GmbH& Co. KG公司，原液生产厂是位于中国的无锡药明生物技术股份有限公司，III期临床试验阶段和上市注册阶段生产厂一致，并供应全球。""",
        # """为了持续改进,提升产品性能,拟评估外周高压球囊扩张导管的充卸压时间的相关数据,拟制定充卸压时间的标准。""",
        # """一项多中心、开放标签、随机对照III期临床研究：比较FS-1502和T-DM1在HER2阳性不可手术切除的局部晚期或转移性乳腺癌患者中的疗效和安全性""",
        # """共2个治疗组，治疗组1：患者将接受FS-1502联合斯鲁利单抗+化疗(5-FU/卡培他滨)治疗；治疗组2：患者将接受FS-1502联合斯鲁利单抗治疗；""",
#         """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。""",
#         """FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
#         """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。
# FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
        # """**你好*""",
        # """阿奇霉素...红霉素.….克拉霉素...地红霉素""",
        """要求患者在半卧或仰卧休息至少10分钟后进行采集，连续采集3次，每次间隔至少1分钟，记录数据（QT间期、QTcF间期、PR间期、QRS间期、RR间期)。"""



    ]
    
    # 执行翻译
    tokens = translator.tokenize(texts)
    translated_tokens = translator.translate(tokens)
    results = translator.detokenize(translated_tokens)

    # 遍历每个源文本对应的翻译结果
    for i, (src, beams) in enumerate(zip(texts, results)):
        print(f"源文本 {i+1}: {src}")
        for j, beam in enumerate(beams):
            print(f"Beam {j+1}: {beam}")
        print("-" * 50)



    """
    python -m inference.engine.ctranslate2
    """