import argparse
import logging
import queue
import traceback
from enum import Enum
from typing import List, Tuple, Union

# from inference.engine.ctranslate2 import Ctranslate2
from inference.engine.hfhub_ctranslate2 import Ctranslate2
from inference.scheduler import Scheduler
from configurer.config_reader import project_config

EXEC_BATCHS_PER_S = project_config.get('exec_batchs_per_s', 15)  # 每秒处理请求数


class ErrCode(Enum):
    INPUT_INVALID = 1001  # 输入非法
    ENGINE_OVERLOAD = 1002  # 队列已满，限流错误
    ENGINE_EXEC_ERROR = 1003  # 引擎执行错误
    RESPONSE_DATA_ERROR = 1004  # 执行返回数据格式异常


class ErrorInfo:

    def __init__(self, code: int, message: str) -> None:
        self.code = code
        self.message = message


class Translator:
    def __init__(self,
                 model_path,
                 token_path,
                 src_lang,
                 tgt_lang,
                 beam_num=4,
                 max_input_length=512,
                 max_decoding_length=512,
                 max_batch_size=256,
                 batch_wait_timeout_s=0.1,
                 timeout=120,
                 device='cuda',
                 device_index=0,
                 compute_type='int8_float16',
                 num_hypotheses=1) -> None:

        self._engine = Ctranslate2(
            model_path=model_path,
            token_path=token_path,
            src_lang=src_lang,
            tgt_lang=tgt_lang,
            beam_num=beam_num,
            compute_type=compute_type,
            max_input_length=max_input_length,
            max_decoding_length=max_decoding_length, 
            device=device,
            device_index=device_index,
            num_hypotheses=num_hypotheses
        )
        # max_queue_size:根据timeout 结合每秒处理的请求数(当前只能预估)得到
        self._scheduler = Scheduler(
            timeout,
            max_queue_size=timeout * EXEC_BATCHS_PER_S,
            max_batch_size=max_batch_size,
            batch_wait_timeout_s=batch_wait_timeout_s,
            trans_func_chain=(self._engine.tokenize, self._engine.translate, self._engine.detokenize),
            batching_func_idx=1
        )

    @property
    def src_lang(self):
        return self._engine.src_lang

    @property
    def tgt_lang(self):
        return self._engine.tgt_lang

    def translate(self, text_list: Union[List[str], str]) -> Union[Tuple[List[str], ErrorInfo], None, ErrorInfo]:
        if len(text_list) == 0:
            logging.warning("the input text list is empty")
            return (None, ErrorInfo(ErrCode.INPUT_INVALID.value, "the input text list is empty"))
        if not isinstance(text_list, (List, str)):
            logging.warning("the intput data type is invalid")
            return (None, ErrorInfo(ErrCode.INPUT_INVALID.value, "the intput data type is invalid"))
        try:
            # logging.info(f"=====text_list: {text_list}")
            res = self._scheduler.exec_sync(text_list, timeout=self._scheduler.timeout)
            # logging.info(f"=====res: {res}")
            if res is None or len(res) == 0:
                return (res, ErrorInfo(ErrCode.RESPONSE_DATA_ERROR.value, "the tranlated data is empty"))
            return (res, ErrorInfo(0, ""))
        except queue.Full as e:
            logging.warning("the translator data cache queue is full,need reduce the request concurrency")
            return (None, ErrorInfo(ErrCode.ENGINE_OVERLOAD.value,
                                    "the translator data cache queue is full,need reduce the request concurrency"))
        except queue.Empty as e:
            logging.error("get empty result from the translator")
            traceback.print_exc()
            return (None, ErrorInfo(ErrCode.ENGINE_OVERLOAD.value,
                                    "the translator data cache queue is full,need reduce the request concurrency"))
        except ValueError as e:
            logging.error(f"occur ValueError when execute the tranlsation pipline,error:{str(e)}")
            traceback.print_exc()
            return (None, ErrorInfo(ErrCode.RESPONSE_DATA_ERROR.value, f"{str(e)}"))
        except Exception as e:
            logging.error(f"execute tranlation occur exception,input text list:{text_list},error:{str(e)}")
            traceback.print_exc()
            return (None, ErrorInfo(ErrCode.ENGINE_EXEC_ERROR.value, f"{str(e)}"))


from inference.engine.profiler import TimeProfiler, TimeTicker


def do_translate(trans, index, text_list, locker=None, all_results=None):
    ticker = TimeTicker('trans.translate')
    results = trans.translate(text_list=text_list)
    rt = ticker.tick()
    if results[0] is None:
        logging.error(f"translate failed,err_code:{results[1].code},err_msg:{results[1].message}")
    else:
        if isinstance(text_list, str):
            logging.info(
                f"index:{index},src_lang:en,tgt_lang:zh,src_text:{text_list},tgt_text:{results[0][0]},rt:{rt}s")
            if locker is not None:
                with locker:
                    all_results.append(results[0][0])
        else:
            for i, text in enumerate(text_list):
                logging.info(f"index:{index},src_lang:en,tgt_lang:zh,src_text:{text},tgt_text:{results[0][i]},rt:{rt}s")
                # logging.info(f"src_lang:en,tgt_lang:zh,src_text:{text},tgt_text:{results[0][i]}")
                if locker is not None:
                    with locker:
                        all_results.append(*results[0])


def translate_file(trans, args):
    all_results = []
    import threading
    locker = threading.Lock()

    if not os.path.isfile(args.f):
        logging.error(f"can not found file:{args.f}")
        os._exit(-1)
    text_list = []
    import xml.etree.ElementTree as et
    xml_root = et.parse(args.f).getroot()
    namespaces = {'xml': 'http://www.w3.org/XML/1998/namespace'}
    for tu_element in xml_root.findall('.//tu', namespaces):
        english_seg = tu_element.find('.//tuv[@xml:lang="en-US"]//seg', namespaces)
        chinese_seg = tu_element.find('.//tuv[@xml:lang="zh-CN"]//seg', namespaces)

        if english_seg is not None and chinese_seg is not None:
            en_text = english_seg.text.strip()
            zh_text = chinese_seg.text.strip()
            if args.src == 'en':
                if len(en_text) > 15 and len(en_text) < 70:
                    text_list.append(en_text)
            else:
                if len(en_text) > 15 and len(en_text) < 70:
                    text_list.append(zh_text)
    import random
    '''
    text_list=['Article 13 After the establishment of a company, a certificate of capital contribution shall be issued to the shareholders who have paid their capital contribution.']
    text_list *= 1024
    '''
    text_list *= 4
    random.shuffle(text_list)
    tokens_list = trans._engine.tokenize(text_list)
    token_size = sum([len(t) for t in tokens_list])

    from concurrent.futures import ThreadPoolExecutor
    threadPool = ThreadPoolExecutor(max_workers=256, thread_name_prefix="trans_")
    with TimeProfiler(title=f"Test with file", desc=f"do_tranlate items:{len(text_list)}"):
        for i, text in enumerate(text_list):
            future = threadPool.submit(do_translate, trans, i, text, locker=locker, all_results=all_results)

        threadPool.shutdown(wait=True)
    tgt_token_list = trans._engine.tokenize(sequences=all_results)
    tgt_token_size = sum([len(t) for t in tgt_token_list])
    logging.info(
        f"test file:{args.f},total ressult iterms:{len(all_results)}, input token size:{token_size}, output token size:{tgt_token_size}")


def main(args):
    warm_text_list = [
        'About what is coronavirus',
        None,
        'During the period of liquidation, the partnership enterprise shall survive and shall not carry out business activities that have nothing to do with liquidation.',
        'Article 17 A partner who does not participate in the execution of partnership affairs shall have the right to supervise the execution of partnership affairs by the partner, the managing partner shall regularly report to the other partners the execution of the affairs and the operation and financial status of the partnership, the proceeds of the execution of the partnership firm shall belong to the partnership, and the expenses and losses incurred are borne by the partnership.'
    ]

    trans = Translator(model_path=args.m, token_path=args.t, src_lang=args.src, tgt_lang=args.tgt, device=args.dev)

    # warmup
    do_translate(trans, 0, warm_text_list)
    # test multiple batch case
    import threading
    t0 = threading.Thread(target=do_translate, args=(trans, 11, ['who are you?']))
    t1 = threading.Thread(target=do_translate, args=(trans, 22, ['what is your name?', 'what can i help you?']))
    t2 = threading.Thread(target=do_translate,
                          args=(trans, 33, ['how do you do?', 'how much is this book?', 'my name is david']))
    t0.start()
    t1.start()
    t2.start()
    t0.join()
    t1.join()
    t2.join()

    if args.i is not None:
        do_translate(trans, 0, args.i)
    if args.f is not None:
        translate_file(trans, args)


if __name__ == "__main__":
    import os

    logging.basicConfig(level=logging.DEBUG,
                        format='%(filename)s:%(lineno)d - %(funcName)s  %(levelname)s %(asctime)s --- %(message)s',
                        datefmt='%m/%d/%Y %H:%M:%S')
    parser = argparse.ArgumentParser()
    parser.add_argument("--m", type=str, default="model", help="model dir path")
    parser.add_argument("--t", type=str, default="tokenizer", help="tokenizer model path")
    parser.add_argument("--i", type=str, default=None, help="input sequence")
    parser.add_argument("--f", type=str, default=None, help="input sequence file path")
    parser.add_argument("--src", type=str, default='en', help="source language")
    parser.add_argument("--tgt", type=str, default='zh', help="target language")
    parser.add_argument("--dev", type=str, default='cuda', help="device type for inference")
    args = parser.parse_args()
    main(args)
    os._exit(0)
