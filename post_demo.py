import logging
import time

import json
import os
import requests
import random
from argparse import ArgumentParser
from concurrent.futures import ThreadPoolExecutor
import xml.etree.ElementTree as et

from inference.engine.profiler import TimeProfiler
from util.character_util import count_chinese_chars, count_english_words


# 测试样例
def test_quality(src_lang="en", tgt_lang="zh", use_terms=False):
    url = f"{ENDPOINT}/translate"
    zh_text_list = [
        """AE 免疫性肠炎
AE 尿蛋白阳性
AE 尿蛋白阳性 1级
AE 尿蛋自阳性 1级结束
AE 尿蛋白阳性1级结束
AE 凝血酶原时间延长
AE 贫血
AE 贫血加重""",
        """供试品管理人员：刘双双
供试品配制人员：张敏、王怡婷、朱凡、周颖琪、张恬、张瑜
供试品分析：胡晓、王超超、陈泽政、张伟伟
张敏
王怡婷
朱凡""",
        """金额合计(大写)壹佰陆拾壹元贰角陆分
金额合计(大写)壹佰贰拾捌元伍角伍分
金额合计(大写)贰佰零贰元捌角壹分
金额合计(大写)柒拾壹元叁角伍分
金额合计(大写)壹拾贰元整
金额合计(大写)肆仟柒佰伍拾陆元肆角柒分
金额合计(大写)柒拾陆元壹角肆分
金额合计(大写)壹佰捌拾元伍角捌分
金额合计(大写)壹佰肆拾玖元陆角叁分
金额合计(大写)壹佰捌拾元壹角
金额合计(大写)壹佰伍拾伍元柒角壹分
金额合计(大写)壹佰玖拾肆元伍角
金额合计(大写)叁佰肆拾肆元伍角
金额合计(大写)壹佰壹拾元伍角肆分
金额合计(大写)柒拾柒元零壹分
金额合计(大写)捌拾肆元伍角柒分
金额合计(大写)陆拾壹元壹角柒分
金额合计(大写)叁拾贰元叁角玖分
金额合计(大写)壹拾元整
金额合计(大写)壹佰柒拾捌元捌角捌分
金额合计(大写)壹佰柒拾柒元整
金额合计(大写)玖拾伍元伍角""",
        """品牌
舒路通®(SurfLubri®)""",
        """<For the seal, refer to the source document>
【禾川官网】
【禾川官微】
苏州禾川化学技术服务有限公司
苏州禾川化学技术服务有限公司(以下简称本公司)为提供符合下述条款的测试和报告，而接受有关样品和货品，本公司基于下述条款提供服务，下述条款为本公司与""",
        """无锡泰格医药科技有限公司
(除药明泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商)""",
        """附录K
(规范性)
6.7中未涵盖的绝缘要求""",
        """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。
FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
        """相关方法和结果的描述见模块2，第2.7.1节临床生物药剂学总结。
        Isatuximab的分布容积较低（8.75 L），表明其主要分布在循环系统中。
Isatuximab的分布容积较低（8.75 L），表明其主要分布在循环系统中。
值得注意的是，所有缓解者的RO至少为70%（2.7.2【第3.3.1.1节】，【第3.4.1节】）。
Isatuximab与泊马度胺联合用药的剂量/方案选择考虑了许多因素（2.7.2【第3.4节】）。

.不好处理。这种情况该怎么判断要不要从.改成。""",
        """2024年3月13日
2020年3月31日
2016年12月12日
2009年11月16日
2005年10月20日

给药
ACE1831研究性药物以含有DMSO的冷冻保存配方提供，用于静脉给药。""",
        """结果候选材料水平I和水平II的均一性F值分别为1.448 5和1.569 6，均小于F0.05，水平I和水平II在-20°C下均稳定至少30d，水平I和水平II的均一性F值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%的置信限内，表明候选材料具有良好的互换性。
结论：
候选物均匀性好，稳定性好，互换性好，赋值准确可靠，可作为国家标准品。""",
        """您可以在无保护性行为后最多5天内服用紧急避孕药。
越早服用任一种紧急避孕药，效果越好。
有些包装含有1片药，有些包装含有2片药。您可以同时服用这2片药。""",
        """2024年3月13日
2020年3月31日
2016年12月12日
2009年11月16日
2005年10月20日
给药
ACE1831研究性药物以含有DMSO的冷冻保存配方提供，用于静脉给药。""",
        

        """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病"全身性重症肌无力"，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
        """一项CM355治疗复发或难治性B细胞非霍奇金淋巴瘤（B-NHL）的I/II期研究已获得国家药品监督管理局（NMPA）批准，批件号为：2021LP01500。""",
        "试验组共记录103例（80.5%）不良事件，对照组共记录99例（75.0%）不良事件（p=0.2889）",
        "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；",
        "试验组共记录103例（80.5%）不良事件，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。",
        "不良事件",
        "严重不良事件",
        "同时加强翻身拍背等气道管理，并做好静脉血栓防治和感控，及时汇报并沟通患者病情，积极抢救处理。",
        "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
        "人工智能技术正在改变我们的生活方式。",
        "风险管理文档",
        "风险管理文档。",
        "处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。",
        "停止",
        "除药明和泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商",
        "就诊类型：门诊，结算方式：支付宝支付：61. 17",
        "否",
        "苏州禾川化学技术服务有限公司",
        """【禾川官网】""",
        """【禾川官微】""",
        """患者因"1.肺原位癌2.胸膜钙化"于2023.4.4来我院住院治疗。""",
        """患儿面部红疹于3日10：00自行消退，无其它不适。""",
        """化学品及企业标识(chemical product and company identification)""",
        """比利时""",
        """本品的制剂生产厂是位于德国Vetter Pharma-Fertigung GmbH& Co. KG公司，原液生产厂是位于中国的无锡药明生物技术股份有限公司，III期临床试验阶段和上市注册阶段生产厂一致，并供应全球。""",
        """为了持续改进,提升产品性能,拟评估外周高压球囊扩张导管的充卸压时间的相关数据,拟制定充卸压时间的标准。""",
        """一项多中心、开放标签、随机对照III期临床研究：比较FS-1502和T-DM1在HER2阳性不可手术切除的局部晚期或转移性乳腺癌患者中的疗效和安全性""",
        """共2个治疗组，治疗组1：患者将接受FS-1502联合斯鲁利单抗+化疗(5-FU/卡培他滨)治疗；治疗组2：患者将接受FS-1502联合斯鲁利单抗治疗；""",
        """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。""",
        """FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
        """**你好*""",
        """阿奇霉素...红霉素.….克拉霉素...地红霉素""",
        """要求患者在半卧或仰卧休息至少10分钟后进行采集，连续采集3次，每次间隔至少1分钟，记录数据（QT间期、QTcF间期、PR间期、QRS间期、RR间期)。""",
        """白细胞计数0.44*10^9/L，中性粒细胞数0.16*10^9/L，考虑发生骨髓抑制不良反应，一般生命征平稳，予升白、输血小板处理。""",
        """MRPA因为空间分辨率较低、技术要求高及紧急情况下不适宜应用等缺点，在急性PTE诊断中不作为一线诊断方法。""",
        """(Full name in Chinese): 厦门建发集团有限公司""",
        """（中文全称）：厦门建发集团有限公司""",
        """在第-14天（即开始服用relugolix的当天）、第1天（即开始与apalutamide联合给药的当天）和第28天测量血清睾酮。""",
        """Concomitant Medication""",
        """合并用药""",
        """受试者信息""",
        """相关病史""",
        """体格检查""",
        """完整临床化学检查""",
        """方案版本 - 编号""",
        """大熊制药有限公司""",
        """DWP16001组有26例（21.7%）患者报告了69起不良反应事件，而安慰剂组有20例（16.7%）患者报告了41起不良反应事件。""",
        """研究中受试者的撤出""",
        """试剂""",
        """名称：氯化铯""",
        """制造商：Canto化学""",
        """储存：15 ~ 25°C""",
        """生物流体处理""",
        """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病"全身性重症肌无力"，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
        """在II期临床试验期间，免疫性肠炎患者在接受新型单克隆抗体治疗后显示出显著改善，85%的病例在8周内报告症状减轻和生物标志物正常化。""",
        """研究记录了治疗组中23例尿蛋白阳性病例，其中7例患者进展为尿蛋白阳性1级，根据安全监测委员会制定的草案指南需要调整剂量。""",
        """实验室检查显示，接受研究药物的患者中有15%出现凝血酶原时间延长，而8%出现贫血，严重程度通常为轻度至中度，仅有两例贫血加重需要输血。""",
        """免疫相关不良事件管理草案指南建议，一旦确认为3级或更高级别的免疫性肠炎，立即停止治疗，随后进行大剂量皮质类固醇治疗，并密切监测尿蛋白阳性状态。""",
        """摘要：目的 建立冻干人血清中睾酮国家标准物质，评价其准确度，促进睾酮检测的标准化。方法 收集无脂血、溶血和黄疸的血清样本。经多次过滤灭菌后，将血清池分装到安瓿瓶中，制成2级候选材料，并储存在-70℃。通过单因素方差分析评价候选材料的均匀性，通过线性回归分析评价稳定性。采用参考方法对候选材料进行赋值，并计算不确定度。评价了互换性。结果 候选材料水平Ⅰ和水平Ⅱ的均匀性F值分别为1.448 5和1.569 6，均小于F0.05。水平Ⅰ和水平Ⅱ在-20℃下至少可稳定30天。水平Ⅰ和水平Ⅱ的赋值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%置信区间内，表明互换性良好。结论 候选材料的均匀性、稳定性和互换性良好，其值被准确可靠地赋予。它们可以作为国家标准使用。""",
        """草案指南""",
        """我在16:30到17:30之间进食""",
        """我在2005年10月20日16:30到17:30之间进食""",
        """中毒性巨结肠[中毒性巨结肠(10027115)*]，严重性：导致/延长住院，结果：恢复中/缓解中""",
        """CRP [C反应蛋白 (10006824)*], 2025-04-05, 126""",
        """CRP [C反应蛋白 (10006824)*], 269""",
        """样品文库为arr[0]、A[i]。""",
        """请使用函数compute_ratio(input_value)计算ROI。""",
        """访问arr[0]和A[i]索引以从数组中检索元素。""",
        """化合物Mg(OH)2在溶液中表现出7.4的pH值（中性）。""",
        """在最终分析中，风险降低报告为HR=0.65[95% CI:0.52-0.81]，p<0.0001。""",
        """结果显示与基线相比，应答率提高了35%（具有统计学意义）。""",
        """执行re.match('(ab|cd)', text)以匹配字符串中的模式。""",
        """所有参与者的剂量设定为5 mg/kg（口服给药）。""",
        """在处理前确保使用(int)value在C中进行值转换。""",
        """详细分析请参见图2（治疗组）。""",
        """约翰·史密斯于2023年1月15日来访""",
        """玛丽亚·罗德里格斯医生于2024年7月8日执行了该程序""",
        """患者：威廉·约翰逊，出生日期：1985年3月22日""",
        """周二4月5日14:30与张伟安排会议""",
        """最后一次与田中由纪女士的随访于2022年12月10日进行""",
        """样本由卡洛斯·门德斯医生于2023年9月30日上午08:45采集""",
        """李明于2024年5月18日至2024年6月2日期间请假""",
        """李明 took leave from May 18, 2024 to June 2, 2024""",
        """王芳医生将于2023年10月15日星期一上午9:00进行手术""",
        """陈伟（ID: 98765432）于2022年4月1日入职""",
        """该患因"咳嗽，咳痰三十余年，加重十余天"于2019年11月05日入院，血钾（K+）：3.1mmol/L，血钠（Na+）：141mmol/L，氧分压PO2）：71mmHg，氧化碳分压（PCO2）：38mmHg，二氧化碳总量（TCO2）：28.70mmo1/L.临床诊断为慢性阻塞性肺疾病急性加重期，于2019年11月07日给予吸入用复方异内托溴铵溶液2.5ml雾化吸入，一日两次，于2019年11月07日晚出现手抖，怀疑药物不良反屈引起，给予停药处置，于2019年11月8日好转，未再接触怀疑药品。""",
        """患儿于11月15日使用吸入用异丙托溴按溶液2m1+0.9%NS·2m1进行雾化吸入治疗后出现颜面、躯干可见红色皮疹，偶有咳嗽，无发热，无抽搐，无吼喘、呼吸困难，停止雾化后逐步恢复，查体：T36.5℃，P·110次/分，R·28次/分，神清、神可，颜面及躯干可见少许红色皮疹，唇周无绀，双肺呼吸音粗，未闻及干、湿啰音，心音有力，律齐，心前区未闻及明显杂音，腹软，肠鸣音3-4次/分，肢暖，处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。""",
        '2024-06-05',
        'ABCD',
        'CAPA CPU WPO',
        "是",
        "LIRAGLUTIDE",
        "2021-07-24 (-969)",
        "2023-07-06 (-257)",
        "0.6-1.2（毫克）",
        "其他（QN）",
        "皮下注射",
        "14（其他：IU）",
        "QD",
        "皮下注射",
        "在本次试验中，使用了API、HTTP/2和TCP/IP协议进行数据传输。",
        "请将日志文件通过FTP://DATA.SERVER.COM/UPLOAD上传。",
        "患者出现了AE（ADVERSE EVENT）和SAE（SERIOUS ADVERSE EVENT）。",
        "本研究严格遵循GCP和ICH-E6指导原则。",
        "设备型号为XYZ-1234，序列号为SN/5678-90/AB。",
        "QC阶段，CPU使用率超过90%，GPU使用率超过80%。",
        "详细文档请访问https://WWW.EXAMPLE.COM/PATH或联系*******************。",
        "在QA/QC测试中，所有结果均符合PASS标准。",
        "请前往 http://foo.com 获取更多信息。",
        "可以通过 https://foo.com/bar?arg=值 下载资源。",
        "报告请使用 ftp://数据.服务器.cn/文件.zip 下载。",
        "请查看（https://foo.com/doc）了解详情。",
        "您可以登录 https://user:<EMAIL>:443/dashboard。",
        '资源链接：<img src="https://cdn.example.com/image.png" />',
        "设备管理界面位于 http://********:8000/。",
        "请访问 https://foo.com/path，获取最新数据。",
        "参见链接：https://foo.com/path(详情)。",
        "备用地址 http://backup.example.com 和 https://mirror.example.org 都可使用。",
        "Ki-67:25%",
        "HGB : 98 g/L (↓)",
        "比例 : 50%",
        "错误码 404: 未找到",
        "Key:值",
        '无',
        '周六的朝阳刚爬上梧桐树梢,林小玉就提着水壶走进了社区花园。',
        '周六的朝阳刚爬上梧桐树梢 林小玉就提着水壶走进了社区花园。',
        '冠状动脉支架内再狭窄',
        "免疫性肠炎",
        '对照组',
        'SIT_SYS_逻辑视图_CAN_限束器及遥控器控制',
        'SIT_SYS_UIV_TUI_基本控件',
        'SIT_SYS_UIV_TUI_悬吊相关UI',
        'SIT_SYS_UIV_胸片架_控制模块',
        'SIT_SYS_用户界面_无线遥控器_基本操作',
        'SIT_SYS_UIV_AWS_平板状态显示',
        'SIT_SYS_UIV_AWS_检查界面_变更',
        'SIT_SYS_UIV_TUI_随动控制',
        "液(500ml,QD,静脉输液,2025年5月27日)用于呕吐,钾口服溶液(10g,QD,PO,氯化2025年5月27日至2025年5月28日)用于呕吐,异内嗪注射液(50mg,QD,输液,2025年5月27日)用于呕吐,氯化钾注射液(3g,静脉QD,静脉输液,2025年5月27日至2025年5月28日)用于呕吐,注射捕奥美拉唑钠(40mg,每日两次(BID),静脉输液,2025年5月27日)用于呕吐,浓氯化钠注射液(20ml,QD,静脉输液,2025年5月27日)用于呕吐。",
        "χ2检验,检验标准为α=0.05,P<0.05为差异有统计学意义。",
        "结果",
        "临床疗效",
        "观察组:CR 0例,PR 17例,SD 5例,PD2例;对照组CR 0例,PR 6例,SD 5例,",
        "PD 6例;2组比较均差异有统计学意义(P<0.05)(表1)。观察组8例乳腺肿瘤指标明 显下 降,其中3例CEA、CAl53恢复至正常水平;9例肺转移患者肺部转移灶明显缩小,胸水量明显减少,胸闷、咳嗽明显好转;6例肝转移患者病灶缩小>50%,肝区胀痛缓解。随访12个月观察组死亡4例,对照组死亡7例,1年总体生存率分别为83.33%(20/24)和58.82%(10/17);其中观察组有2例(均为肺转移)带瘤生存至今分别为31个月和26个月。表1",
        "观察组和对照组临床疗效对比(%, n)",
        "观察组",
        "对照组",
        "χ2值",
        "P值",
        "9例肺转移患者肺部转移灶明显缩小,胸水量明显减少,胸闷、咳嗽明显好转;",
        "9例肺转移患者肺部转移灶明显缩小,胸水量明显减少,胸闷和咳嗽明显好转;",
        "9例肺转移患者肺部转移灶明显缩小 胸水量明显减少 胸闷和咳嗽明显好转",
        "李贲",
        "吴诸丽",
        "林小玉",
        "李明",
        "王皞然",
        "张琬琰",
        "李彧珩",
        "陈霁月",
        "刘泫雅",
        "杨翾飞",
        "黄旖萱",
        "周峤岳",
        "吴玥玟",
        "徐澍霖",
        "孙蒨茹",
        "胡琲瑭",
        "林巘峥",
        "何瑭珂",
        "郭昶煦",
        "马翀霄",
        "罗嬿婉",
        "梁昪晖",
        "宋旸晞",
        "郑婳祎",
        "谢翯翯",
        "韩澂澈",
        "唐璆琳",
        "冯霭霭",
        "董珩瑜",
        "张睿",
        "王珞",
        "李湛",
        "刘昶",
        "陈昀",
        "杨飒",
        "周翎",
        "吴攸",
        "郑砚",
        "赵棠",
        "孙皎",
        "胡澈",
        "林岫",
        "何霁",
        "郭翎",
        "马骁",
        "罗纨",
        "梁昭",
        "宋熹",
        "谢昀",
        "韩澈",
        "唐瑭",
        "冯砚",
        "董皎",
        "苏翀",
        "潘翎",
        "卢攸",
        "汪飒",
        "蔡棠",
        "贾湛",
        "开学第一天，班主任王芳介绍了新同学张伟、李娜、陈浩和周婷，班长刘洋负责带他们熟悉校园。",
        "在项目讨论会上，经理赵刚让王强、李敏、吴斌和孙丽分别汇报各自负责的模块进展。",
        "周末家宴时，大伯张建国、姑姑王秀英、表哥李志强和表妹刘佳都来参加了，爷爷赵明德特别开心。",
        "挂号处排着长队，张阿姨带着孙子王小明来看病，前面是李建军、周小红和陈阿姨在等候。",
        "社区组织的植树活动中，志愿者队长杨光明带着队员孙芳、吴伟、郑丽和林强一起种了20棵树。",
        "高中同学聚会上，班长王磊见到了多年不见的老同学张静、李勇、刘芳和赵斌，大家聊得很开心。",
        "超市收银台前，周阿姨遇到了邻居王叔叔、李婶、张大哥和他女儿张小玉，大家都在排队结账。",
        "旅行团的导游刘明带着游客王丽、张强、李娜和陈伟参观了故宫，大家都拍了很多照片。",
        "物业办公室内，主任赵建国正在处理王阿姨、李叔叔、张伯伯和周大姐反映的小区停车位问题。",
        "婚礼上，新郎王勇和新娘李静收到了伴郎张伟、伴娘刘芳和证婚人赵老师的美好祝福。",
        "三、本院 AI 办公室自行开发之软件医材或智能医疗应用产品。",
        "申请临床试用需填写“人工智能模型临床应用部署申请表”，由 AI 办公室收案，及相关单位进行 试用流程预审。",
        "预审通过后由申请单位将申请书及相关附件，签请单位主管，视需要会办协同申请 单位，由人工智能临床部署审查小组核准后，始得开始试用。",
        "申请单位应为院内各级单位使得符合 申请人条件，且主要负责人须任职本校、本院、本校受委托经营之医疗事业及其他单位之专任员工。",
        "经试用流程评估，通过后得签请院长核准正式使用。",
        "本院 AI 办公室自行开发之软件医材或智能医疗应用产品，则不受限于申请试用流程，但仍须填 写“人工智能模型临床应用部署申请表”，并符合本院信息科制定 ISMS-3-GE-008 应用系统管理作业 说明书之软件上线要求，并持续监测与管理。",
        "第 4 条 名词定义",
        "医疗器材软件 ( 以下简称医材软件 ) 定义：依据国际医疗器材法规管理论坛 (International Medical Device Regulators Forum,  以下简称 IMDRF) 以及卫生福利部食品药物管理署医用软件分 类分级参考指引， 医材软件无需为医疗器材硬件之一部分，是可执行一个以上的医疗用途，适用于 诊断、治疗或是辅助应用之软件。",
        "应用部署实例",
        "高雄医科大学附属中和纪念医院",
        "医材软件可在一般运算平台上试用，亦可与其他医疗器材结合或互通试用。",
        "若仅作为驱动医疗 器材硬件之驱动程序，则不属于医材软件之范围。",
        "临床应用部署定义：本办法所定义之临床应用部署系指包含软件医材或智能医疗应用产品于临 床应用的部署、资源整合、效益评估及持续改进，且需与医疗信息系统（如 HIS、PACS 等）接合以 实现实际临床应用。",
        "第 5 条 评估审查流程",
        "一、初步审查：",
        "申请资料提交后，由 AI 办公室进行初步审查，确认申请之医材软件或智能医疗应用产品符合以 下条件：",
        "1. 符合本办法适用范围及申请资格。",

        "胃肠功能恢复情况观察患者术后胃肠功能恢复情况，记录患者术后首次肛门排气时间，并追踪患者住院时间。",
        "营养指标术前1 d和术后1周，测定患者血清前白蛋白水平，采集患者静脉血3 ml，经全自动生化分析仪完成测定，对比营养指标水平变化。",
        "结果",
        "胃肠功能恢复情况促动力组术后首次肛门排气时间、住院时间均短于肠内组（P<0.05）。",
        "见表1。",
        """高血压""",
        """注射液""",
        "本案例及分析由华大科技伦理委员会康辉提供。",
        "附件一：",
        "人工智能模型临床部署应用管理办法",
        "高雄医科大学附属中和纪念医院",
        "第 1 条 目的",
        "高雄医科大学附属中和纪念医院（以下简称本院） 为应对 AI 医疗应用产品及医疗器材软件 (Software as Medical Device，以下简称医材软件 ) 的特殊性，确保信息安全性、患者资料不外泄 及不影响本院信息系统运作之前提下，提供申请临床应用时有所依循，并于 AI 产品临床应用部署后 持续监测与管理，特订本办法。",
        "第 2 条 业管单位",
        "本办法之业管单位为智能医疗促进办公室（以下简称 AI 办公室），推动办公室负责本办法之规划、 执行与管理，并统筹人工智能模型于本院及高医医疗体系之临床应用部署相关事宜，包括资源整合、  效能监控、法规遵循及技术审查，确保人工智能应用符合相关规范并实现智能医疗之策略目标。",

        '''<g id="1" type="tag1" text="tag">rtg-tools vcfeval 功能启用后分解和斜滑倍数参数，用于比较过滤后的 Freebayes 和 MuTect2 VCF，并保留交叉调用。变异用 vcf2maf （v 1.6.21）（DOI: 10.5281/zenodo.1185418）注释，这取决于 Ensembl 变异效应预测器 （v.104.3）</g type="tag1" text="/tag"><g id="2" type="tag2" text="tag property=&quot;x-sup;color:0068A5;&quot;">63</g type="tag2" text="/tag">，而 OncoKB （v.3.3.1）<g id="3" type="tag3" text="tag property=&quot;x-sup;color:0068A5;&quot;">57</g type="tag3" text="/tag"><g id="4" type="tag4" text="tag">。所有交叉和非交叉比较的变异被导入 R，每个变异使用自定义脚本进行标记、筛选和计数。如果 Freebayes 和 MuTect2 均能识别，且符合以下条件，则认为 SNV/indels 为真阳性：（1）变异在 OncoKB 中整理，且必须标记为致癌或疑似致癌；或 （2）如果变异未在 OncoKB 中整理，则必须将其标记为致癌或疑似致癌，但也不得在 OncoKB 中进行剪接位点或剪接区域分类。针对已知种系或非致病性变异状态的特定基因应用附加过滤标准：</g type="tag4" text="/tag"><g id="5" type="tag5" text="tag property=&quot;x-italic;&quot;">PMS2 </g id="5>remove p.Argn20）；<g id="6" type="tag6" text="tag property=&quot;x-italic;&quot;">MET </g type="tag6" text="/tag">（remove.GluAsp168）；<g id="7" type="tag7" text="tag property=&quot;x-italic;&quot;">KMTC2 </g type="tag7" text="/tag">（remove.Tyr816）；Terlym<g id="8" type="tag8" text="tag property=&quot;x-italic;&quot;">ATM</g type="tag8" text="/tag">（删除p.His1380Tyr）；<g id="9" type="tag9" text="tag property=&quot;x-italic;&quot;">FANCA</g type="tag9" text="/tag">（删除p.Ser1088Phe和p.Ser858Arg）；<g id="10" type="tag10" text="tag property=&quot;x-italic;&quot;">NCOR1</g type="tag10" text="/tag">（删除p.Arg190Ter）；<g id="11" type="tag11" text="tag property=&quot;x-italic;&quot;">AURKA</g type="tag11" text="/tag">（删除p.Phe31Ile）；<g id="12" type="tag12" text="tag property=&quot;x-italic;&quot;">FOXA1</g type="tag12" text="/tag"><g id="13" type="tag13" text="tag">（忽略上文OncoKB过滤并删除所有同义突变，p.Ala83Thr、p.Ser448Asn和p.Leu148Val）。如果最终过滤变异影响</g type="tag13" text="/tag"><g id="14" type="tag14" text="tag property=&quot;x-italic;&quot;">HSD3B1</g type="tag14" text="/tag">或除以下基因之外的任何其他基因，则将其归类为可能的种系<g id="15" type="tag15" text="tag property=&quot;x-italic;&quot;">AR</g type="tag15" text="/tag">、<g id="16" type="tag16" text="tag property=&quot;x-italic;&quot;">TP53</g type="tag16" text="/tag">、<g id="17" type="tag17" text="tag property=&quot;x-italic;&quot;">PTEN</g type="tag17" text="/tag">、<g id="18" type="tag18" text="tag property=&quot;x-italic;&quot;">ERF</g type="tag18" text="/tag">、<g id="19" type="tag19" text="tag property=&quot;x-italic;&quot;">PIK3CA</g type="tag19" text="/tag">或<g id="20" type="tag20" text="tag property=&quot;x-italic;&quot;">CDK12</g type="tag20" text="/tag"><g id="21" type="tag21" text="tag">等位基因频率在0.45-0.55或&gt; 0.95之间。否则，过滤的变异被归类为可能的体细胞变异。</g type="tag21" text="/tag">''',
        """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-italic;color:1D1D1B;&quot;\">— 自动化班次准备</g type=\"tag1\" text=\"/tag\">.""",
        """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-italic;color:1D1D1B;&quot;\">— 增强团队领导力</g type=\"tag1\" text=\"/tag\">.""",
        """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-color:1D1D1B;&quot;\">用例二：通过智能偏差管理简化生产维护 管理偏差和纠正预防措施(CAPAs)的端到端流程需要制造现场4%至6%的资源，且充满挑战。</g type=\"tag1\" text=\"/tag\"><g id=\"2\" type=\"tag2\" text=\"tag property=&quot;x-color:1D1D1B;&quot;\">4  </g type=\"tag2\" text=\"/tag\">常见痛点包括检测延迟、手动任务、首次正确解决方案率低、效率低、文档不一致以及被动反应流程。""",
        """意图：风险矩阵报告记录了<x id="2" type="tag2" text="tag property=&quot;x&quot;/"/><g id="3" type="tag3" text="tag property=&quot;x-bold;color:auto;&quot;">PUREVUE <x id="4" type="tag4" text="tag property=&quot;x&quot;/"/></g type="tag3" text="/tag">的RoHS认证、合规测试结果和相关风险评估""",
        """样品文库使用<g id=\"23\">NEBNext Ultra II</g>试剂盒制备，分配条形码<bx id=\"24\"/> A01–H12<ex id=\"25\"/>，在<g id=\"26\">Illumina MiSeq</g>上进行2×300 bp测序；文库制备ID<bx id=\"27\"/>已记录<ex id=\"28\"/>并上传至<mrk id=\"29\"/> LIMS v4.2。""",
        '在实验中，测得电容 C<g id="1" type="tag1" text="tag property=&quot;x-sub;&quot;">1</g type="tag1" text="/tag"> 的电压 V<g id="2" type="tag2" text="tag property=&quot;x-sup;&quot;">2</g type="tag2" text="/tag">，详见文献 <g id="LNK" type="tagLink" text="tag property=&quot;x-link;color:007FAC;&quot;">[1]<g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tagLink" text="/tag">；另附表<bx id="10" type="tagBox" text="tag property=&quot;x-link;color:FF00FF;&quot;"/>A 和脚注<ex id="11" type="tagEx" text="/tag"/>²；同时在<mrk id="12" mtype="emph"/>深度测试中出现嵌套<g id="3" type="tag3" text="tag property=&quot;x-sup;&quot;"><g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tag3" text="/tag">，并且保留普通标签；此外还有分隔符<x id="X1" type="tagX" text="tag property=&quot;x&quot;"/>和实体&lt; &amp; &quot; &gt;。',
        '在实验中，</g type="tag1" text="/tag">测得电容 C<g id="1" type="tag1" text="tag property=&quot;x-sub;&quot;">1</g type="tag1" text="/tag"> 的电压 V<g id="2" type="tag2" text="tag property=&quot;x-sup;&quot;">2</g type="tag2" text="/tag">，详见文献 <g id="LNK" type="tagLink" text="tag property=&quot;x-link;color:007FAC;&quot;">[1]<g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tagLink" text="/tag">；另附表<bx id="BX1" type="tagBox" text="tag property=&quot;x-link;color:FF00FF;&quot;"/>A 和脚注<ex id="EX1" type="tagEx" text="/tag"/>²；同时在<mrk id="M1" mtype="emph"/>深度测试中出现嵌套<g id="3" type="tag3" text="tag property=&quot;x-sup;&quot;"><g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tag3" text="/tag">，并且</g type="tag1" text="/tag">保留<span>普通标签</span>；此外还有分隔符<x id="X1" type="tagX" text="tag property=&quot;x&quot;"/>和实体&lt; &amp; &quot; &gt;。',
        '试验/研究组xx例，对照组xx例，共计xx例<g id="1" type="tag1" text="tag property=&quot;x-color:000000;&quot;">。</g type="tag1" text="/tag">',
        '试验/研究组xx例，对照组xx例，共计xx例<g id="1">。</g>',
        '试验/研究组xx例，对照组xx例，共计xx例<__NT001>',
        '试验/研究组xx例，<__TERM001>xx例，共计xx例<__NT001>',
        '试验/研究组xx例，对照组xx例，共计xx例<g id="1">。</g>',
        '试验/研究组xx例，对照组xx例，实验组xx例，试验组xx例，共计xx例<g id="1">。</g>',
        '试验/研究组xx例，对照组 xx例，实验组xx例，试验组 xx例，共计xx例<g id="1">。</g>',
        '<__RL001>用例二：通过智能偏差管理简化生产维护 管理偏差和纠正预防措施(CAPAs)的端到端流程需要制造现场4%至6%的资源，且充满挑战。<__RL002><__RL003>4  <__RL004>常见痛点包括检测延迟、手动任务、首次正确解决方案率低、效率低、文档不一致以及被动反应流程。',
        '<__RL001><__RL005><__RL006>案例研究3：实施数字化质量管理系统，减少文档错误并改善多个制造现场的合规跟踪。<__RL007><__RL008><__RL009>从纸质系统过渡到数字系统使文档错误减少了78%，审计响应时间加快了45%。<__RL010>',
        '<__RL001><__RL011><__RL012><__RL013>图4：传统与AI增强质量控制流程的比较<__RL014><__RL015><__RL016>AI增强系统检测到99.7%的缺陷，而传统视觉检查方法为92.3%，同时将检查时间减少了65%。<__RL017><__RL018>',
        '<__RL001><__RL019><__RL020><__RL021><__RL022>表2.3：制药生产优化的关键绩效指标<__RL023><__RL024><__RL025><__RL026>实施集成生产监控系统后，整体设备效率(OEE)从67%提高到85%。<__RL027><__RL028><__RL029>',
        '<__TERM001>',
        "<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-bold;color:FFFFFF;&quot;\">附</g type=\"tag1\" text=\"/tag\"><g id=\"2\" type=\"tag2\" text=\"tag property=&quot;x-bold;color:FFFFFF;&quot;\">将</g type=\"tag2\" text=\"/tag\"><g id=\"3\" type=\"tag3\" text=\"tag property=&quot;x-bold;color:FFFFFF;&quot;\">览咎</g type=\"tag3\" text=\"/tag\"><g id=\"4\" type=\"tag4\" text=\"tag property=&quot;x-bold;color:FFFFFF;&quot;\">雌</g type=\"tag4\" text=\"/tag\"><g id=\"5\" type=\"tag5\" text=\"tag property=&quot;x-bold;color:FFFFFF;&quot;\">廿</g type=\"tag5\" text=\"/tag\"><g id=\"6\" type=\"tag6\" text=\"tag property=&quot;x-color:FFFFFF;&quot;\"> <x id=\"7\" type=\"tag7\" text=\"tag property=&quot;x&quot;/\"/><g id=\"8\" type=\"tag8\" text=\"tag property=&quot;x-bold;color:FFFFFF;&quot;\">姿<x id=\"9\" type=\"tag9\" text=\"tag property=&quot;x&quot;/\"/>厢<x id=\"10\" type=\"tag10\" text=\"tag property=&quot;x&quot;/\"/></g type=\"tag8\" text=\"/tag\"></g type=\"tag6\" text=\"/tag\">",
        "医学研究伦理通讯 <g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-color:898989;&quot;\">MEDICAL RESEARCH ETHICS NEWSLETTER</g type=\"tag1\" text=\"/tag\">",
        "第 3 条 申请范围与资格 <g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-bold;color:298788;&quot;\">申请范围：</g type=\"tag1\" text=\"/tag\">",
        "<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-bold;color:298788;&quot;\">一、产学合作之 AI 模型： </g type=\"tag1\" text=\"/tag\">由本校自行开发或与其他各级学校或产业界共同研发之人工智能模 型者，需检附本校产学营运处所承办之共同开发协议书或智慧财产协议书等资料佐证。",
        "<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-bold;color:298788;&quot;\">二、医材软件产品： </g type=\"tag1\" text=\"/tag\">经台湾相关主管机关核发许可证之医材软件产品或免证 AI 医疗应用产品为 限。",
        
    ]
    src_text_list = zh_text_list

    if use_terms:
        # 定义双向术语列表
        zh_to_en_terms = [
            {
                "src_term": "免疫性肠炎",
                "tgt_term": "immunological enteritis",
                "is_case_sensitive": False,
                "is_not_translate": True
            },
            {
                "src_term": "尿蛋白阳性",
                "tgt_term": "protein urine positive",
                "is_case_sensitive": False,
                "is_not_translate": False
            },
            {
                "src_term": "尿蛋白阳性 1级",
                "tgt_term": "URINE PROTEIN POSITIVE GRADE 1",
                "is_case_sensitive": False,
                "is_not_translate": False
            },
            {
                "src_term": "尿蛋自阳性 1级结束",
                "tgt_term": "URINE PROTEIN POSITIVE GRADE 1 END",
                "is_case_sensitive": False,
                "is_not_translate": False
            },
            {
                "src_term": "凝血酶原时间延长",
                "tgt_term": "Prothrombin time prolonged",
                "is_case_sensitive": False,
                "is_not_translate": False
            },
            {
                "src_term": "贫血",
                "tgt_term": "Anaemia",
                "is_case_sensitive": False,
                "is_not_translate": False
            },
            {
                "src_term": "贫血加重",
                "tgt_term": "ANEMIA AGGRAVATION",
                "is_case_sensitive": False,
                "is_not_translate": False
            },
            {
                "src_term": "指导原则草案",
                "tgt_term": "draft guidelines",
                "is_case_sensitive": False,
                "is_not_translate": False
            },
            {
                "src_term": "MACCE",
                "tgt_term": "MACCE"
            },
            {
                "src_term": "不良事件",
                "tgt_term": "adverse events"
            },
            {
                "src_term": "严重不良事件",
                "tgt_term": "serious adverse events"
            },
            {
                "src_term": "试验组",
                "tgt_term": "test group"
            },
            {
                "src_term": "对照组",
                "tgt_term": "Control group"
            },
            {
                "src_term": "试验产品DissolveTM冠脉药物球囊扩张导管",
                "tgt_term": "investigational product DissolveTM coronary drug coated balloon"
            },
            {
                "src_term": "冠状动脉支架内再狭窄",
                "tgt_term": "coronary in-stent restenosis"
            },
            # {
            #     "is_case_sensitive": False,
            #     "is_not_translate": False,
            #     "src_term": "其他（QN）",
            #     "tgt_term": "Other (QN)"
            # },
            # {
            #     "is_case_sensitive": False,
            #     "is_not_translate": False,
            #     "src_term": "/男/",
            #     "tgt_term": "/ Male /"
            # },
            # {
            #     "is_case_sensitive": False,
            #     "is_not_translate": False,
            #     "src_term": "其他",
            #     "tgt_term": "Other"
            # },
            # {
            #     "is_case_sensitive": False,
            #     "is_not_translate": False,
            #     "src_term": "汉族",
            #     "tgt_term": "Han"
            # },
            # {
            #     "is_case_sensitive": False,
            #     "is_not_translate": False,
            #     "src_term": "/汉族",
            #     "tgt_term": "/ Han"
            # }
        ]
    else:
        zh_to_en_terms = []
    data = {
        "src_text_list": src_text_list,
        "src_lang": src_lang,
        "tgt_lang": tgt_lang,
        "appname": "yiya",
        "app_secret": "yy-4F224BBCAC32CD7E6509080164796D08",
        "terms": zh_to_en_terms
    }
    start_time = time.time()
    headers = {
        # "enable-special-format": "true",
        # "disable_llm_correction": "false"
    }
    r = requests.post(url, json=data, headers=headers)
    res_json_list = json.loads(r.text)['result']
    for src, res in zip(src_text_list, res_json_list):
        # print(f"src: {src}")
        # print(f"res: {res}")
        # print("-"*100)
        # print(f"{src}")
        # print("-"*100)
        print(f"{res}")
        

    end_time = time.time()
    total = len(src_text_list)
    total_time = end_time - start_time
    avg_time = total_time / total if total > 0 else 0
    logging.info(f"test_quality 样例个数: {total}，总耗时: {total_time:.4f}秒，平均耗时: {avg_time:.4f}秒/条")


def split_list(original_list, chunk_size):
    return [original_list[i:i + chunk_size] for i in range(0, len(original_list), chunk_size)]


def send_translate(src_text_list, src_lang, trg_lang):
    data = {
        "src_text_list": src_text_list,
        "src_lang": src_lang,
        "tgt_lang": trg_lang,
        "appname": "yiya",
        "app_secret": "yy-4F224BBCAC32CD7E6509080164796D08"
    }
    r = requests.post(f'{ENDPOINT}/translate', json=data)
    result = json.loads(r.text)
    return result

# 测试速度
def test_speed(src_lang, tgt_lang):
    tmx_src_lang = "zh-CN"
    tmx_tgt_lang = "en-US"

    testfile = './testdir/20240621152739.tmx'
    src_lang = os.environ.get('SOURCE_LANG', src_lang)
    tgt_lang = os.environ.get('TARGET_LANG', tgt_lang)
    text_list = []
    xml_root = et.parse(testfile).getroot()
    namespaces = {'xml': 'http://www.w3.org/XML/1998/namespace'}
    for tu_element in xml_root.findall('.//tu', namespaces):
        src_seg = tu_element.find(f'.//tuv[@xml:lang="{tmx_src_lang}"]//seg', namespaces)
        tgt_seg = tu_element.find(f'.//tuv[@xml:lang="{tmx_tgt_lang}"]//seg', namespaces)

        if src_seg is not None and tgt_seg is not None:
            src_text = src_seg.text.strip()
            tgt_text = tgt_seg.text.strip()

            text = src_text

            if len(text) > 15 and len(text) < 128:
                text_list.append(text)
    
    '''
    text_list=['Article 13 After the establishment of a company, a certificate of capital contribution shall be issued to the shareholders who have paid their capital contribution.']
    text_list *= 1024
    '''
    text_list = text_list[:]
    # print(f'text_list: {text_list[:]}')
    text_list *= 4
    random.shuffle(text_list)

    word_count_list = [count_chinese_chars(t) if src_lang == 'zh' else count_english_words(t) for t in text_list]
    token_size = sum(word_count_list)

    text_list_trunks = split_list(text_list, 50)  # 减小每批次的数量

    threadPool = ThreadPoolExecutor(max_workers=30, thread_name_prefix="trans_")

    with TimeProfiler(title=f"测试文档：{os.path.basename(testfile)}",
                      desc=f"翻译条目数:{len(text_list)}, token 数量: {token_size}"):
        futures = [threadPool.submit(send_translate, chunk, src_lang, tgt_lang) for chunk in text_list_trunks]
                
        for future in futures:
            try:
                result = future.result()
                print(result)
            except Exception as exc:
                logging.error(f"Task {future} finished with exception: {exc}")

def parse_args():
    parser = ArgumentParser()
    parser.add_argument("--src_lang", type=str, default="en")
    parser.add_argument("--tgt_lang", type=str, default="zh")
    # 是否使用术语，命令行如何赋值：--use_terms True
    parser.add_argument("--use_terms", type=bool, default=False, choices=[True, False])
    parser.add_argument("--type", type=str, default="speed", choices=["speed", "quality"])
    return parser.parse_args()

if __name__ == '__main__':
    args = parse_args()
    src_lang = args.src_lang
    tgt_lang = args.tgt_lang
    use_terms = args.use_terms
    ENDPOINT = os.environ.get('ENDPOINT', 'http://localhost:7860')
    
    if args.type == "speed":
        test_speed(src_lang, tgt_lang)
    else:
        test_quality(src_lang, tgt_lang, use_terms)

    os._exit(0)

"""
python post_demo.py --type speed --src_lang zh --tgt_lang en

python post_demo.py --type quality --src_lang zh --tgt_lang en
python post_demo.py --type quality --src_lang zh --tgt_lang en --use_terms True
"""
