from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response
import contextvars

from logger.logger import request_id

disable_llm_correction = contextvars.ContextVar('disable_llm_correction', default=None)
enable_special_format_processing = contextvars.ContextVar('enable_special_format_processing', default=None)
use_small_llm = contextvars.ContextVar('use_small_llm', default=None)


class ContextMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        request_id_str = request.headers.get("x-request-id", "")
        request_id.set(request_id_str)

        _enable_llm_correction = request.headers.get("disable-llm-correction", "false")
        disable_llm_correction.set(_enable_llm_correction)

        _enable_special_format = request.headers.get("enable-special-format", "false")
        enable_special_format_processing.set(_enable_special_format)

        _use_small_llm = request.headers.get("use-small-llm", "false")
        use_small_llm.set(_use_small_llm)

        response = await call_next(request)
        request_id.set(None)
        return response


def get_disable_llm_correction() -> bool:
    return disable_llm_correction is None or disable_llm_correction.get() == "true"


def get_enable_special_format_processing() -> bool:
    return enable_special_format_processing is not None and enable_special_format_processing.get() == "true"


def get_use_small_llm() -> bool:
    return use_small_llm is not None and use_small_llm.get() == "true"

