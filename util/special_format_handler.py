import re
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
from logger.logger import biz_logger
from aop.context import get_enable_special_format_processing


@dataclass
class SpecialFormatItem:
    """特殊格式文本的结构化表示"""
    original_text: str          # 原始文本
    original_index: int         # 在原始列表中的索引
    parts: List[str]           # 拆分后的所有部分
    chinese_indices: List[int]  # 中文部分在parts中的索引
    chinese_texts: List[str]    # 需要翻译的中文文本
    chinese_global_ids: List[int]  # 中文部分在全局翻译列表中的ID


class SpecialFormatHandler:
    """
    特殊格式处理器 - 在API层面处理特殊格式文本
    流程：
    1. 检测特殊格式文本
    2. 拆分并缓存大写部分
    3. 提取中文部分进行平铺
    4. 翻译完成后根据ID重新组合
    """
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置处理器状态"""
        self.special_items: List[SpecialFormatItem] = []
        self.normal_indices: List[int] = []  # 普通文本的索引
        self.chinese_texts_for_translation: List[str] = []  # 需要翻译的中文文本
        self.id_to_item_mapping: Dict[int, Tuple[int, int]] = {}  # global_id -> (item_index, chinese_index)
    
    def is_special_format(self, text: str) -> bool:
        """
        检测文本是否符合特殊格式
        格式特征：
        1. 包含下划线分隔符
        2. 开头是大写字母
        3. 包含中文字符
        4. 符合：大写(_大写)*(_中文部分)*的模式
        """
        if not text or '_' not in text:
            return False
        
        # 必须包含中文字符
        if not re.search(r'[\u4e00-\u9fff]', text):
            return False
        
        # 必须以大写字母开头
        if not re.match(r'^[A-Z]', text):
            return False
        
        # 按下划线分割
        parts = text.split('_')
        if len(parts) < 2:
            return False
        
        # 检查是否有前缀的纯大写部分
        has_leading_uppercase = False
        for i, part in enumerate(parts):
            if part.isupper():
                has_leading_uppercase = True
            else:
                # 一旦遇到非纯大写部分，就停止检查
                break
        
        # 检查是否有中文部分
        has_chinese_part = False
        for part in parts:
            if re.search(r'[\u4e00-\u9fff]', part):
                has_chinese_part = True
                break
        
        is_special = has_leading_uppercase and has_chinese_part
        
        if is_special:
            biz_logger.info(f"检测到特殊格式文本: '{text}'")
        else:
            biz_logger.debug(f"非特殊格式文本: '{text}' (has_leading_uppercase: {has_leading_uppercase}, has_chinese_part: {has_chinese_part})")
        
        return is_special
    
    def parse_special_format(self, text: str) -> Tuple[List[str], List[int]]:
        """
        解析特殊格式文本，返回所有部分和中文部分的索引
        
        新的逻辑：
        1. 识别每个包含中文的部分作为独立的中文部分
        2. 不合并，保持原有的下划线分割结构
        
        例如：
        "SIT_SYS_用户界面_无线遥控器_基本操作" -> 
        parts: ["SIT", "SYS", "用户界面", "无线遥控器", "基本操作"]
        chinese_indices: [2, 3, 4]
        
        返回:
            (parts, chinese_indices): 所有部分的列表，中文部分的索引列表
        """
        parts = text.split('_')
        chinese_indices = []
        
        # 识别每个包含中文字符的部分
        for i, part in enumerate(parts):
            if part and re.search(r'[\u4e00-\u9fff]', part):
                chinese_indices.append(i)
        
        biz_logger.info(f"解析特殊格式 '{text}': parts={parts}, chinese_indices={chinese_indices}")
        biz_logger.info(f"  提取的中文部分: {[parts[i] for i in chinese_indices]}")
        
        return parts, chinese_indices
    
    def process_input_texts(self, src_list: List[str]) -> Tuple[List[str], bool]:
        """
        处理输入文本列表，拆分特殊格式文本
        
        Args:
            src_list: 原始文本列表
            
        Returns:
            (processed_texts, has_special): 处理后的文本列表，是否包含特殊格式
        """
        if not get_enable_special_format_processing():
            return src_list, False
        
        self.reset()
        processed_texts = []
        has_special = False
        
        biz_logger.info("开始处理输入文本，检测特殊格式")
        
        for i, text in enumerate(src_list):
            if self.is_special_format(text):
                has_special = True
                
                # 解析特殊格式
                parts, chinese_indices = self.parse_special_format(text)
                chinese_texts = [parts[idx] for idx in chinese_indices]
                
                # 为每个中文部分分配全局ID
                chinese_global_ids = []
                for j, chinese_text in enumerate(chinese_texts):
                    global_id = len(self.chinese_texts_for_translation)
                    chinese_global_ids.append(global_id)
                    self.chinese_texts_for_translation.append(chinese_text)
                    self.id_to_item_mapping[global_id] = (len(self.special_items), j)
                
                # 创建特殊格式项
                special_item = SpecialFormatItem(
                    original_text=text,
                    original_index=i,
                    parts=parts,
                    chinese_indices=chinese_indices,
                    chinese_texts=chinese_texts,
                    chinese_global_ids=chinese_global_ids
                )
                self.special_items.append(special_item)
                
                biz_logger.info(f"处理特殊格式文本 {i}: '{text}' -> 提取 {len(chinese_texts)} 个中文部分: {chinese_texts}")
                
                # 暂时用占位符替代，后面会被替换
                processed_texts.append(f"__SPECIAL_FORMAT_{len(self.special_items)-1}__")
                
            else:
                # 普通文本，直接添加
                self.normal_indices.append(i)
                processed_texts.append(text)
        
        biz_logger.info(f"输入文本处理完成: 特殊格式文本 {len(self.special_items)} 个，"
                       f"普通文本 {len(self.normal_indices)} 个，"
                       f"需要翻译的中文片段 {len(self.chinese_texts_for_translation)} 个")
        
        return processed_texts, has_special
    
    def get_chinese_texts_for_translation(self) -> List[str]:
        """获取需要翻译的中文文本列表"""
        return self.chinese_texts_for_translation
    
    def smart_capitalize(self, text: str) -> str:
        """
        智能首字母大写处理
        规则：
        1. 对于英文单词，首字母大写，其余小写
        2. 但是保持原有的连续大写字母（如UI、AWS等）
        3. 保持中文字符不变
        """
        if not text:
            return text
        
        # 使用正则表达式分割文本，保持空格和标点符号
        tokens = re.findall(r'\S+|\s+', text)
        
        result_tokens = []
        for token in tokens:
            if token.isspace():
                # 保持空格不变
                result_tokens.append(token)
            else:
                # 处理单词
                processed_token = self._process_word(token)
                result_tokens.append(processed_token)
        
        return ''.join(result_tokens)
    
    def _process_word(self, word: str) -> str:
        """
        处理单个单词的大小写
        """
        if not word:
            return word
        
        # 如果包含中文字符，不做处理
        if re.search(r'[\u4e00-\u9fff]', word):
            return word
        
        # 如果是连续大写字母（如UI、AWS、CAN），保持不变
        if len(word) >= 2 and word.isupper():
            return word
        
        # 检查是否包含连续的大写字母
        uppercase_parts = re.findall(r'[A-Z]{2,}', word)
        if uppercase_parts:
            # 保持连续大写部分不变，只处理其他部分
            result = word
            # 先将整个单词首字母大写，其余小写
            if re.match(r'^[a-zA-Z]', word):
                result = word[0].upper() + word[1:].lower()
            
            # 然后恢复连续大写部分
            for uppercase_part in uppercase_parts:
                # 找到小写后的位置并恢复
                lowercase_part = uppercase_part.lower()
                if lowercase_part in result:
                    result = result.replace(lowercase_part, uppercase_part)
            
            return result
        
        # 普通英文单词，首字母大写
        if re.match(r'^[a-zA-Z]', word):
            return word[0].upper() + word[1:].lower()
        
        return word
    
    def reconstruct_results(self, 
                          original_src_list: List[str], 
                          normal_results: List[str], 
                          chinese_translations: List[str]) -> List[str]:
        """
        重构最终结果
        
        Args:
            original_src_list: 原始输入文本列表
            normal_results: 普通翻译结果（可能包含占位符）
            chinese_translations: 中文部分的翻译结果
            
        Returns:
            最终的翻译结果列表
        """
        if not get_enable_special_format_processing() or not self.special_items:
            return normal_results
        
        biz_logger.info("开始重构特殊格式翻译结果")
        final_results = []
        
        for i, result in enumerate(normal_results):
            # 检查是否是特殊格式占位符
            if result.startswith("__SPECIAL_FORMAT_") and result.endswith("__"):
                # 提取特殊格式项索引
                try:
                    item_index = int(result.replace("__SPECIAL_FORMAT_", "").replace("__", ""))
                    special_item = self.special_items[item_index]
                    
                    # 重构特殊格式文本
                    reconstructed = self._reconstruct_special_text(special_item, chinese_translations)
                    final_results.append(reconstructed)
                    
                    biz_logger.info(f"重构特殊格式文本 {i}: '{special_item.original_text}' -> '{reconstructed}'")
                    
                except (ValueError, IndexError) as e:
                    biz_logger.error(f"重构特殊格式文本失败: {e}")
                    # fallback到原文
                    final_results.append(original_src_list[i] if i < len(original_src_list) else result)
            else:
                # 普通翻译结果
                final_results.append(result)
        
        biz_logger.info("特殊格式翻译结果重构完成")
        return final_results
    
    def _reconstruct_special_text(self, special_item: SpecialFormatItem, chinese_translations: List[str]) -> str:
        """重构单个特殊格式文本"""
        reconstructed_parts = special_item.parts.copy()
        
        # 替换中文部分为翻译结果
        for i, chinese_idx in enumerate(special_item.chinese_indices):
            global_id = special_item.chinese_global_ids[i]
            if global_id < len(chinese_translations):
                translation = chinese_translations[global_id]
                # 智能首字母大写处理
                capitalized_translation = self.smart_capitalize(translation)
                reconstructed_parts[chinese_idx] = capitalized_translation
                
                biz_logger.info(f"替换中文部分: '{special_item.chinese_texts[i]}' -> '{capitalized_translation}'")
        
        return '_'.join(reconstructed_parts)
    
    def prepare_texts_for_translation(self, processed_texts: List[str]) -> Tuple[List[str], List[int]]:
        """
        准备送翻译的文本列表
        
        Args:
            processed_texts: 经过特殊格式处理的文本列表
            
        Returns:
            (texts_for_translation, special_placeholder_indices): 
            需要翻译的文本列表，特殊格式占位符的索引列表
        """
        texts_for_translation = []
        special_placeholder_indices = []
        
        for i, text in enumerate(processed_texts):
            if text.startswith("__SPECIAL_FORMAT_") and text.endswith("__"):
                special_placeholder_indices.append(i)
                # 不添加到翻译列表中
                texts_for_translation.append("")  # 占位符，保持索引对应
            else:
                texts_for_translation.append(text)
        
        # 添加中文片段到翻译列表末尾
        texts_for_translation.extend(self.chinese_texts_for_translation)
        
        biz_logger.info(f"准备翻译文本: 普通文本 {len(processed_texts) - len(special_placeholder_indices)} 个，"
                       f"中文片段 {len(self.chinese_texts_for_translation)} 个")
        
        return texts_for_translation, special_placeholder_indices
    
    def extract_chinese_translations(self, all_translations: List[str]) -> List[str]:
        """从所有翻译结果中提取中文部分的翻译"""
        # 中文翻译在列表的末尾
        start_index = len(all_translations) - len(self.chinese_texts_for_translation)
        chinese_translations = all_translations[start_index:]
        
        biz_logger.info(f"提取中文翻译结果: {len(chinese_translations)} 个")
        return chinese_translations 