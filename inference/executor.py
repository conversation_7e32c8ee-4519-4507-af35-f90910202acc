import logging
import queue
import threading
import time
import traceback
from typing import Tuple, Callable, List, Any


class Executor(threading.Thread):
    class DataQueue:

        def __init__(self, max_queue_size) -> None:
            self.max_queue_size = max_queue_size
            self._locker = threading.Lock()
            self._cond = threading.Condition(self._locker)
            self._q: List[Tuple[int, Any]] = list()

        def put(self, data):
            with self._cond:
                if len(self._q) >= self.max_queue_size:
                    raise queue.Full
                self._q.insert(0, data)
                self._cond.notify_all()

        def put_batch(self, datas: List[Tuple[int, Any]]):
            for data in datas:
                self.put(data)

        def front(self):
            with self._locker:
                if len(self._q) > 0:
                    return self._q[-1]
                else:
                    return None

        def get(
                self,
                max_size=None,
                timeout=None,
        ) -> List[Tuple[int, Any]]:
            # 支持批量返回
            with self._cond:
                if max_size is not None:
                    res = self._cond.wait_for(lambda: len(self._q) >= max_size, timeout=timeout)
                    if res:
                        data_list = self._q[-max_size:]
                        self._q = self._q[:(len(self._q) - len(data_list))]
                        return data_list
                    else:
                        if len(self._q) > 0:
                            data_list = self._q[:]
                            self._q.clear()
                            return data_list
                        else:
                            return None
                else:
                    res = self._cond.wait_for(lambda: len(self._q) > 0, timeout=timeout)
                    if res:
                        data_list = self._q[:]
                        self._q.clear()
                        return data_list
                    else:
                        return None

        def get_by_tidx(self, tidx, timeout=None):
            '''
            根据tran_index获取相应的数据
            '''
            with self._cond:
                def predict():
                    if len(self._q) > 0:
                        trans_idxs, _ = zip(*self._q)
                        return tidx in trans_idxs
                    else:
                        return False

                res = self._cond.wait_for(predict, timeout=timeout)
                if res:
                    data_list = []
                    reserved_data_list = []
                    for data in self._q:
                        if data[0] == tidx:
                            data_list.append(data)
                        else:
                            reserved_data_list.append(data)
                    self._q = reserved_data_list
                    return data_list
                else:
                    return None

        def get_by_max_size(self, max_size, timeout) -> List[Tuple[int, Any]]:
            return self.get(max_size=max_size, timeout=timeout)

        def get_by_idxs(self, idxs: List[int]):
            raise NotImplementedError(f"{self.__class__.__name__}:get_by_idxs is not implemented")

        def data_dimensions(self) -> List[int]:
            with self._locker:
                return [len(data[1]) for data in self._q]

        @property
        def size(self):
            with self._locker:
                return len(self._q)

        def __rshift__(self, other):
            other._in_q = self
            return other

        def full(self):
            with self._locker:
                return len(self._q) >= self.max_queue_size

    def __init__(self, exec_id, exec_func: Callable, max_queue_size) -> None:
        super().__init__()
        self.exec_id = exec_id
        self._in_q: Executor.DataQueue = None
        self._out_q: Executor.DataQueue = Executor.DataQueue(max_queue_size)
        self._exec_func = exec_func

    def __rshift__(self, other):
        if isinstance(other, Executor):
            other._in_q = self._out_q
            return other
        else:
            return self._out_q

    def run(self) -> None:
        while True:
            if self._out_q.full():
                time.sleep(0.001)
                continue
            max_batch_size = self._out_q.max_queue_size - self._out_q.size
            data_list = self._in_q.get(max_size=max_batch_size, timeout=0.01)
            if data_list is not None:
                self._run(data_list)

    def _run(self, data_list):
        trans_idxs, datas = zip(*data_list)
        # 对运行时异常的捕获，避免可能存在的异常导致阻塞
        try:
            normal_idxs = []
            if None not in datas:
                normal_idxs = [i for i in range(len(trans_idxs))]
                res_list = self._exec_func(list(datas))
            else:
                # 出现None数据，说明前置流程已经出现异常，需要透传None值
                n_datas = []
                for i, d in enumerate(datas):
                    if d is not None:
                        normal_idxs.append(i)
                        n_datas.append(d)
                res_list = self._exec_func(n_datas)

            if len(normal_idxs) == len(datas):
                res_data_list = zip(trans_idxs, res_list)
            else:
                res_data_list = []
                for i, t_id in enumerate(trans_idxs):
                    if i in normal_idxs:
                        res_data_list.append((t_id, res_list[normal_idxs.index(i)]))
                    else:
                        res_data_list.append((t_id, None))

            self._out_q.put_batch(res_data_list)
        except Exception as e:
            traceback.print_exc()
            logging.error(f"execute exe_func occur exception,influenced trans data size:{len(trans_idxs)}")
            res_data_list = zip(trans_idxs, [None] * len(trans_idxs))
            self._out_q.put_batch(res_data_list)


class BatchingExecutor(Executor):

    def __init__(self, exec_id, exec_func: Callable, max_queue_size, max_batch_size, batch_wait_timeout_s) -> None:
        super().__init__(exec_id, exec_func, max_queue_size)
        self.max_batch_size = max_batch_size
        self.batch_wait_timeout_s = batch_wait_timeout_s
        logging.info(
            f"BatchingExecutor max_batch_size:{self.max_batch_size},batch_wait_timeout_s:{self.batch_wait_timeout_s}")

    def run(self) -> None:

        while True:
            if self._out_q.full():
                time.sleep(0.001)
                continue

            max_batch_size = min(self.max_batch_size, self._out_q.max_queue_size - self._out_q.size)
            # s_t = time.time()
            data_list = self._in_q.get(max_size=max_batch_size, timeout=self.batch_wait_timeout_s)
            '''
            e_t = time.time()
            if data_list is not None and  len(data_list) < 64:
                logging.debug(f"max_batch_size:{max_batch_size} batching size:{len(data_list)},batching time:{e_t - s_t}s,in_q size:{self._in_q.size} out_q size:{self._out_q.size}")
            '''
            if data_list is not None:
                self._run(data_list)
