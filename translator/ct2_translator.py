# ct2_translator.py
# -*- coding: utf-8 -*-

import ctranslate2
from transformers import AutoTokenizer
from typing import List
import logging # Import logging
from tqdm import tqdm

logger = logging.getLogger(__name__) # Setup logger

class CT2Translator:
    """
    基于 ctranslate2 模型的翻译封装。
    合并 tokenize -> translate -> detokenize，
    提供一个 translate_batch(...) 入口即可从文本到文本。

    注意:
      - 如果是多语言模型(M2M100)，可能需要 target_prefix=["__zh"] 等。
      - 如果是单语模型(比如专门zh->en或en->zh)则无需 lang prefix。
    """

    def __init__(self,
                 model_path: str,
                 token_path: str,
                 src_lang: str = "en",
                 tgt_lang: str = "zh",
                 device: str = "cuda",
                 device_index: list[int] | int = 0, # Allow list for CPU index
                 beam_size: int = 4,
                 max_length: int = 512,
                 compute_type: str = "auto"): # Add compute_type parameter
        """
        :param model_path: ctranslate2 转换后的模型目录
        :param token_path: HF的 tokenizer目录，用以做分词/去分词
        :param src_lang: 源语言简码 (e.g., "en", "zh"). Used for M2M100 prefix.
        :param tgt_lang: 目标语言简码 (e.g., "en", "zh"). Used for M2M100 prefix.
        :param device: "cuda" or "cpu"
        :param device_index: 指定GPU编号 (list or int) or CPU cores (list of ints)
        :param beam_size: 默认为1(贪心解码速度更快)
        :param max_length: 翻译最大长度
        :param compute_type: "auto", "int8", "float16" etc. Defaults to "auto".
        """
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self.beam_size = beam_size
        self.max_length = max_length

        # 初始化 tokenizer - M2M100 needs src_lang set for correct tokenization if model expects it
        try:
            # For M2M100, set src_lang for the tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(token_path, src_lang=src_lang)
            logger.info(f"Tokenizer loaded with src_lang='{src_lang}'")
        except TypeError: # If the tokenizer doesn't accept src_lang
             logger.warning(f"Tokenizer from {token_path} does not accept src_lang argument. Loading without it.")
             self.tokenizer = AutoTokenizer.from_pretrained(token_path)


        # Prepare M2M100 target prefix token(s) if needed
        # M2M100 models expect the target language code as the first token(s) to generate.
        self.tgt_lang_token_id = self.tokenizer.lang_code_to_id[self.tgt_lang]
        self.target_prefix_tokens = [[self.tokenizer.convert_ids_to_tokens(self.tgt_lang_token_id)]]
        logger.info(f"Target prefix tokens for '{self.tgt_lang}': {self.target_prefix_tokens}")


        # 初始化 ctranslate2
        self.translator = ctranslate2.Translator(
            model_path,
            device=device,
            device_index=device_index,
            compute_type=compute_type, # Use provided compute_type
            inter_threads=1 if device=="cuda" else 4, # Optimize CPU threading
            intra_threads=1 if device=="cuda" else 4  # Optimize CPU threading
        )
        logger.info(f"CTranslate2 model loaded from {model_path} on {device}:{device_index} with compute_type='{compute_type}'")


    def translate_batch(self, texts: List[str]) -> List[str]:
        """
        一次性翻译一批文本 => 返回等长List[str]
        """
        if not texts:
            return []

        # 1) Tokenize
        # Using batch_encode_plus for potential efficiency and handling special tokens
        source_tokens_list = []
        try:
             # M2M100 tokenizer needs the language set
             # Important: Ensure tokenizer adds BOS automatically if model needs it. M2M usually does.
             # We don't explicitly add BOS/EOS here, relying on tokenizer's default behavior.
             self.tokenizer.src_lang = self.src_lang # Ensure src_lang is set for this batch
             tokenized_batch = self.tokenizer.batch_encode_plus(texts, return_tensors=None, add_special_tokens=True) # Let tokenizer add special tokens like BOS

             for input_ids in tokenized_batch['input_ids']:
                 # We need list of token strings for ctranslate2
                 tokens = self.tokenizer.convert_ids_to_tokens(input_ids)
                 # M2M100 tokenizer might add the src_lang token at the beginning - check and remove if needed by CTranslate2 model
                 # Typically, CTranslate2 expects *only* the target prefix, not source lang prefix in the input tokens.
                 # Let's check if the first token is the source lang token and remove it.
                 # M2M100 tokenizer *shouldn't* add src lang token by default when encoding source text, but let's be safe.
                 # src_lang_token = f"__{self.src_lang}__"
                 # if tokens and tokens[0] == src_lang_token:
                 #    logger.debug(f"Removing source lang token '{src_lang_token}' from input.")
                 #    tokens = tokens[1:]
                 source_tokens_list.append(tokens)

        except Exception as e:
            logger.error(f"Tokenization failed: {e}")
            # Fallback to simpler tokenization if batch fails
            source_tokens_list = []
            for txt in texts:
                 input_ids = self.tokenizer.encode(txt) # Might need src_lang context here too
                 tokens = self.tokenizer.convert_ids_to_tokens(input_ids)
                 source_tokens_list.append(tokens)

        if not source_tokens_list:
             logger.warning("Tokenization resulted in empty list.")
             return [""] * len(texts) # Return empty strings if tokenization fails


        # 2) 执行 ctranslate2 推理
        # Provide the target_prefix for M2M100 models
        # Duplicate the prefix list for each item in the batch
        batch_target_prefix = self.target_prefix_tokens * len(source_tokens_list)
        try:
            results = self.translator.translate_batch(
                source_tokens_list,
                beam_size=self.beam_size,
                max_decoding_length=self.max_length,
                target_prefix=batch_target_prefix, # Pass target language prefix
                # return_scores=False, # Optional: set True if you need scores
                # num_hypotheses=1 # Optional: if beam_size > 1, how many results per input
                # repetition_penalty=1.02
            )
        except Exception as e:
             logger.error(f"CTranslate2 translation failed: {e}")
             return ["Error during translation"] * len(texts)

        # 3) Detokenize
        out_texts = []
        tgt_lang_token_str = self.tokenizer.convert_ids_to_tokens(self.tgt_lang_token_id) # e.g., "__zh__"

        for res in results:
            # 只取最优假设
            tokens = res.hypotheses[0]
            # M2M100 *should* generate the target language token first.
            # We need to remove it before decoding back to text.
            if tokens and tokens[0] == tgt_lang_token_str:
                tokens = tokens[1:]
            elif tokens:
                 logger.warning(f"Expected target lang token '{tgt_lang_token_str}' at start of output, but found '{tokens[0]}'. Proceeding without removal.")
            else:
                 logger.warning("Translation result is empty.")


            # Decode tokens back to text
            try:
                # Use batch_decode for potential consistency, even if processing one by one here
                decoded_text = self.tokenizer.batch_decode(
                    [self.tokenizer.convert_tokens_to_ids(tokens)], # Needs list of lists of ids
                    skip_special_tokens=True,
                    clean_up_tokenization_spaces=True # Often helpful
                )[0] # Get the first (and only) string from the batch result
                out_texts.append(decoded_text)
            except Exception as e:
                 logger.error(f"Detokenization failed for tokens {tokens}: {e}")
                 out_texts.append("Error during detokenization")


        return out_texts

    def translate(self, text_or_texts, batch_size=10):
        """
        如果只传单条字符串，也能处理。
        """
        if isinstance(text_or_texts, str):
            text_or_texts = [text_or_texts]
        if len(text_or_texts) <= batch_size:
            return self.translate_batch(text_or_texts)
        else:
            translations = []
            for i in tqdm(range(0, len(text_or_texts), batch_size), '翻译进度'):
                batch = text_or_texts[i:i+batch_size]
                translations.extend(self.translate_batch(batch))
            return translations
    
def test_bleu(
    model_path: str,
    token_path: str,
    src_lang: str,
    tgt_lang: str,
    device: str,
    compute_type: str,
    device_index: int,
    batch_size: int,
    src_file: str,
    cache_file: str,
):
    """
    1) 一次性批量调用 HFNativeTranslator.translate，生成翻译并保存到缓存；
    3) 计算并打印 sacreBLEU 分数。
    """
    # 创建翻译器实例
    translator = CT2Translator(
        model_path=model_path,
        token_path=token_path,
        src_lang=src_lang,
        tgt_lang=tgt_lang,
        device=device,
        device_index=device_index,
        max_length=512,
        beam_size=4,  # 只取最佳结果
        compute_type=compute_type,
    )

    # 加载 sacrebleu
    bleu_scorer = evaluate.load("sacrebleu")

    src_texts = []
    formatted_refs = []  # sacrebleu 要求的 [[ref1], [ref2], ...]
    tgt_texts = []

    # 2. 从原始 devtst 文件读取 src+ref
    with open(src_file, "r", encoding="utf-8") as f:
        for line in (f.readlines()[:]):
            data = json.loads(line.strip())["translation"]
            src_texts.append(data[src_lang])
            # sacrebleu 需要引用为列表的列表
            formatted_refs.append([data[tgt_lang]])

    # 批量翻译
    translations = translator.translate(src_texts, batch_size=batch_size)  # 返回 List[List[str]]
    tgt_texts.extend(translations)

    # 3. 缓存到文件
    with open(cache_file, "w", encoding="utf-8") as f:
        for src, ref, tgt in zip(src_texts, formatted_refs, tgt_texts):
            line = {"translation": {"src": src, "formatted_tgt": ref[0], "tgt": tgt}}
            print(line)
            json.dump(line, f, ensure_ascii=False)
            f.write("\n")

    # 4. 计算并打印 BLEU
    result = bleu_scorer.compute(predictions=tgt_texts, references=formatted_refs)
    print(f'BLEU: {result["score"]}')

    return result
    

    

def test(model_path, token_path, src_lang, tgt_lang, device, device_index, compute_type):
    # 创建翻译器实例
    translator = CT2Translator(
        model_path=model_path,
        token_path=token_path,
        src_lang=src_lang,
        tgt_lang=tgt_lang,
        device=device,
        device_index=device_index,
        max_length=512,
        beam_size=4,  # 只取最佳结果
        compute_type=compute_type,
    )
    ######### 测试 ##########
    zh_test_src_texts = [
#         "本申请要求于2023年2月17日递交的中国专利申请第202310153996.9号的优先权，在此全文引用上述中国专利申请公开的内容以作为本申请的一部分。",
#         "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；",
#         "<__TERM001>共记录103例（80.5%）<__TERM002>，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。",
#         "[__NTE001]因为空间分辨率较低、技术要求高及紧急情况下不适宜应用等缺点，在急性[_NTE_02]诊断中不作为一线诊断方法。",        "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
#         """AE 免疫性肠炎
# AE 尿蛋白阳性
# AE 尿蛋白阳性 1级
# AE 尿蛋自阳性 1级结束
# AE 尿蛋白阳性1级结束
# AE 凝血酶原时间延长
# AE 贫血
# AE 贫血加重""",
#         """供试品管理人员：刘双双
# 供试品配制人员：张敏、王怡婷、朱凡、周颖琪、张恬、张瑜
# 供试品分析：胡晓、王超超、陈泽政、张伟伟
# 张敏
# 王怡婷
# 朱凡""",
#         """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病“全身性重症肌无力”，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
#         "人工智能技术正在改变我们的生活方式。",
#         "风险管理文档",
#         "风险管理文档。",
#         "处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。",
#         "停止",
#         "除药明和泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商",
#         """金额合计(大写)壹佰陆拾壹元贰角陆分
# 金额合计(大写)壹佰贰拾捌元伍角伍分
# 金额合计(大写)贰佰零贰元捌角壹分
# 金额合计(大写)柒拾壹元叁角伍分
# 金额合计(大写)壹拾贰元整
# 金额合计(大写)肆仟柒佰伍拾陆元肆角柒分
# 金额合计(大写)柒拾陆元壹角肆分
# 金额合计(大写)壹佰捌拾元伍角捌分
# 金额合计(大写)壹佰肆拾玖元陆角叁分
# 金额合计(大写)壹佰捌拾元壹角
# 金额合计(大写)壹佰伍拾伍元柒角壹分
# 金额合计(大写)壹佰玖拾肆元伍角
# 金额合计(大写)叁佰肆拾肆元伍角
# 金额合计(大写)壹佰壹拾元伍角肆分
# 金额合计(大写)柒拾柒元零壹分
# 金额合计(大写)捌拾肆元伍角柒分
# 金额合计(大写)陆拾壹元壹角柒分
# 金额合计(大写)叁拾贰元叁角玖分
# 金额合计(大写)壹拾元整
# 金额合计(大写)壹佰柒拾捌元捌角捌分
# 金额合计(大写)壹佰柒拾柒元整
# 金额合计(大写)玖拾伍元伍角""",
#         "就诊类型：门诊，结算方式：支付宝支付：61. 17",
#         "否",
#         """附录K
# (规范性)
# 6.7中未涵盖的绝缘要求""",
#         "苏州禾川化学技术服务有限公司",
#         """【禾川官网】""",
#         """【禾川官微】""",
#         """<For the seal, refer to the source document>
# 【禾川官网】
# 【禾川官微】
# 苏州禾川化学技术服务有限公司
# 苏州禾川化学技术服务有限公司(以下简称本公司)为提供符合下述条款的测试和报告，而接受有关样品和货品，本公司基于下述条款提供服务，下述条款为本公司与""",
#         """该患因“咳嗽，咳痰三十余年，加重十余天”于2019年11月05日入院，血钾（K+）：3.1mmol/L，血钠（Na+）：141mmol/L，氧分压PO2）：71mmHg，氧化碳分压（PCO2）：38mmHg，二氧化碳总量（TCO2）：28.70mmo1/L.临床诊断为慢性阻塞性肺疾病急性加重期，于2019年11月07日给予吸入用复方异内托溴铵溶液2.5ml雾化吸入，一日两次，于2019年11月07日晚出现手抖，怀疑药物不良反屈引起，给予停药处置，于2019年11月8日好转，未再接触怀疑药品。""",
#         """患者因“1.肺原位癌2.胸膜钙化”于2023.4.4来我院住院治疗。""",
#         """患儿面部红疹于3日10：00自行消退，无其它不适。""",
#         """化学品及企业标识(chemical product and company identification)""",
#         """品牌
# 舒路通®(SurfLubri®)""",
#         """患儿于11月15日使用吸入用异丙托溴按溶液2m1+0.9%NS·2m1进行雾化吸入治疗后出现颜面、躯干可见红色皮疹，偶有咳嗽，无发热，无抽搐，无吼喘、呼吸困难，停止雾化后逐步恢复，查体：T36.5℃，P·110次/分，R·28次/分，神清、神可，颜面及躯干可见少许红色皮疹，唇周无绀，双肺呼吸音粗，未闻及干、湿啰音，心音有力，律齐，心前区未闻及明显杂音，腹软，肠鸣音3-4次/分，肢暖，处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。""",
#         """无锡泰格医药科技有限公司
# (除药明泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商)""",
#         """比利时""",
#         """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病“全身性重症肌无力”，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
#         """本品的制剂生产厂是位于德国Vetter Pharma-Fertigung GmbH& Co. KG公司，原液生产厂是位于中国的无锡药明生物技术股份有限公司，III期临床试验阶段和上市注册阶段生产厂一致，并供应全球。""",
#         """为了持续改进,提升产品性能,拟评估外周高压球囊扩张导管的充卸压时间的相关数据,拟制定充卸压时间的标准。""",
#         """一项多中心、开放标签、随机对照III期临床研究：比较FS-1502和T-DM1在HER2阳性不可手术切除的局部晚期或转移性乳腺癌患者中的疗效和安全性""",
#         """共2个治疗组，治疗组1：患者将接受FS-1502联合斯鲁利单抗+化疗(5-FU/卡培他滨)治疗；治疗组2：患者将接受FS-1502联合斯鲁利单抗治疗；""",
#         """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。""",
#         """FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
#         """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。
# FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
#         """**你好*""",
#         """阿奇霉素...红霉素.….克拉霉素...地红霉素""",
#         """要求患者在半卧或仰卧休息至少10分钟后进行采集，连续采集3次，每次间隔至少1分钟，记录数据（QT间期、QTcF间期、PR间期、QRS间期、RR间期)。""",
#         "白细胞计数0.44*10^9/L，中性粒细胞数0.16*10^9/L，考虑发生骨髓抑制不良反应，一般生命征平稳，予升白、输血小板处理。",
#         "Abstract：Objective   To establish the national standard materials of testosterone in frozen human serum，    to evaluate the accuracy，and to promote the standardization of testosterone assay. Methods Serum samples without lipemia，hemolysis and jaundice were collected. After multiple filtration sterilization，serum pools were packed into ampoules in 2-level candidate materials and were stored in -70 ℃. The homogeneity for candidate material was evaluated by single factor analysis of variance，and the stability was evaluated by linear regression analysis. The values of candidate material were assigned by reference method，and the uncertainty was calculated. The commutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. Conclusions The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.",
#         "利用0.035in导丝及造影导管置入输送导管头端置于右侧颈总动脉中段，退出泥鳅导丝及造影导管，造影示：右侧大脑中动脉M1段闭塞；利用0.014in微导丝及微导管置入远端通路导管于C2段远端，置入0.014in微导丝和微导管在路图指引下顺利通过右侧大脑中动脉闭塞部位，远端置于M2段中部，退出微导丝，行微导管造影显示：M2段中远端血流缓慢，沿微导管置入5.0*33mm取栓支架在大脑中动脉M1-2段顺利释放，复查造影显示：右侧大脑中动脉再通，未见明显造影剂渗出和动脉栓塞，利用锚定技术将远端通路导管置于大脑中动脉M1段，持续进行抽吸，3分钟后回收支架，并逐渐退出远端通路导管，取出血栓，沿输送导管复查血管造影示右侧大脑中动脉M1段闭塞；重复上述抽拉结合取栓1次后复查造影示：右侧大脑中动脉M1段再通，M2段局部可见血栓影；利用微导丝微导管将远端通路导管置于颈内动脉C3段，复查造影示：大脑中动脉未见明显血栓影，右侧M4段小分支及大脑前动脉A3段小分支闭塞，考虑血栓溶解；",
#         "MRPA因为空间分辨率较低、技术要求高及紧急情况下不适宜应用等缺点，在急性PTE诊断中不作为一线诊断方法。",
#         """医疗器械经营质量管理规范
# 第一章总则
# 第一条为了加强医疗器械经营质量管理,规范医疗器械
# 经营活动,保证医疗器械安全、有效,根据《医疗器械监督管
# 理条例》《医疗器械经营监督管理办法》等法规规章的规定,
# 制定本规范。
# 第二条 本规范是医疗器械经营质量管理的基本要求。从
# 事医疗器械经营活动,应当在医疗器械采购、验收、贮存、销
# 售、运输、售后服务等全过程采取有效的质量管理措施,确保
# 医疗器械产品在经营过程中的质量安全与可追溯。
# 第三条 医疗器械经营企业应当严格执行本规范。""",
#         '自首次给药至应答的天数',
#         '患者可能无法接受最有效的抗菌药物。',
#         '由于产生抗红细胞、白细胞和血小板的抗体而引起的自身免疫性血液病，通常涉及免疫调节的全身性疾病，具有多系统疾病的临床表现[Miller 1983, Coppo 2004, Haas 2009]。',
#         '国家卫生健康委卫生发展研究中心',
#         'Monarch平台支气管镜检查2.2.3软件设计验证计划',
#         "异常",

        # '试验/研究组xx例，对照组xx例，共计xx例<g id="1" type="tag1" text="tag property=&quot;x-color:000000;&quot;">。</g type="tag1" text="/tag">',
        # '试验/研究组xx例，对照组xx例，共计xx例<g id="1">。</g>',
        # '试验/研究组xx例，对照组xx例，共计xx例<__NT001>',
        # '试验/研究组xx例，<__TERM001>xx例，共计xx例<__NT001>',
#         '周六的朝阳刚爬上梧桐树梢，林小玉就提着水壶走进了社区花园。',
#         # '周六的朝阳刚爬上梧桐树梢，林小玉就提着水壶走进了社区花园'

#         """一项CM355治疗复发或难治性B细胞非霍奇金淋巴瘤（B-NHL）的I/II期研究已获得国家药品监督管理局（NMPA）批准，批件号为：2021LP01500。""",
#         "试验组共记录103例（80.5%）不良事件，对照组共记录99例（75.0%）不良事件（p=0.2889）",
#         "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；",
#         "试验组共记录103例（80.5%）不良事件，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。",
#         "不良事件",
#         "严重不良事件",

#         "同时加强翻身拍背等气道管理，并做好静脉血栓防治和感控，及时汇报并沟通患者病情，积极抢救处理。",
#         "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
#         """AE 免疫性肠炎
# AE 尿蛋白阳性
# AE 尿蛋白阳性 1级
# AE 尿蛋自阳性 1级结束
# AE 尿蛋白阳性1级结束
# AE 凝血酶原时间延长
# AE 贫血
# AE 贫血加重""",
#         """供试品管理人员：刘双双
# 供试品配制人员：张敏、王怡婷、朱凡、周颖琪、张恬、张瑜
# 供试品分析：胡晓、王超超、陈泽政、张伟伟
# 张敏
# 王怡婷
# 朱凡""",
#         """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病"全身性重症肌无力"，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
#         "人工智能技术正在改变我们的生活方式。",
#         "风险管理文档",
#         "风险管理文档。",
#         "处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。",
#         "停止",
#         "除药明和泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商",
#         """金额合计(大写)壹佰陆拾壹元贰角陆分
# 金额合计(大写)壹佰贰拾捌元伍角伍分
# 金额合计(大写)贰佰零贰元捌角壹分
# 金额合计(大写)柒拾壹元叁角伍分
# 金额合计(大写)壹拾贰元整
# 金额合计(大写)肆仟柒佰伍拾陆元肆角柒分
# 金额合计(大写)柒拾陆元壹角肆分
# 金额合计(大写)壹佰捌拾元伍角捌分
# 金额合计(大写)壹佰肆拾玖元陆角叁分
# 金额合计(大写)壹佰捌拾元壹角
# 金额合计(大写)壹佰伍拾伍元柒角壹分
# 金额合计(大写)壹佰玖拾肆元伍角
# 金额合计(大写)叁佰肆拾肆元伍角
# 金额合计(大写)壹佰壹拾元伍角肆分
# 金额合计(大写)柒拾柒元零壹分
# 金额合计(大写)捌拾肆元伍角柒分
# 金额合计(大写)陆拾壹元壹角柒分
# 金额合计(大写)叁拾贰元叁角玖分
# 金额合计(大写)壹拾元整
# 金额合计(大写)壹佰柒拾捌元捌角捌分
# 金额合计(大写)壹佰柒拾柒元整
# 金额合计(大写)玖拾伍元伍角""",
#         "就诊类型：门诊，结算方式：支付宝支付：61. 17",
#         "否",
#         """附录K
# (规范性)
# 6.7中未涵盖的绝缘要求""",
#         "苏州禾川化学技术服务有限公司",
#         """【禾川官网】""",
#         """【禾川官微】""",
#         """<For the seal, refer to the source document>
# 【禾川官网】
# 【禾川官微】
# 苏州禾川化学技术服务有限公司
# 苏州禾川化学技术服务有限公司(以下简称本公司)为提供符合下述条款的测试和报告，而接受有关样品和货品，本公司基于下述条款提供服务，下述条款为本公司与""",
#         """该患因"咳嗽，咳痰三十余年，加重十余天"于2019年11月05日入院，血钾（K+）：3.1mmol/L，血钠（Na+）：141mmol/L，氧分压PO2）：71mmHg，氧化碳分压（PCO2）：38mmHg，二氧化碳总量（TCO2）：28.70mmo1/L.临床诊断为慢性阻塞性肺疾病急性加重期，于2019年11月07日给予吸入用复方异内托溴铵溶液2.5ml雾化吸入，一日两次，于2019年11月07日晚出现手抖，怀疑药物不良反屈引起，给予停药处置，于2019年11月8日好转，未再接触怀疑药品。""",
#         """患者因"1.肺原位癌2.胸膜钙化"于2023.4.4来我院住院治疗。""",
#         """患儿面部红疹于3日10：00自行消退，无其它不适。""",
#         """化学品及企业标识(chemical product and company identification)""",
#         """品牌
# 舒路通®(SurfLubri®)""",
#         """患儿于11月15日使用吸入用异丙托溴按溶液2m1+0.9%NS·2m1进行雾化吸入治疗后出现颜面、躯干可见红色皮疹，偶有咳嗽，无发热，无抽搐，无吼喘、呼吸困难，停止雾化后逐步恢复，查体：T36.5℃，P·110次/分，R·28次/分，神清、神可，颜面及躯干可见少许红色皮疹，唇周无绀，双肺呼吸音粗，未闻及干、湿啰音，心音有力，律齐，心前区未闻及明显杂音，腹软，肠鸣音3-4次/分，肢暖，处理：停止吸入用异丙托溴按溶液雾化，观察患儿情况。""",
#         """无锡泰格医药科技有限公司
# (除药明泰格作为 SMO 分包商外，一些研究中心还将指定SMO 供应商)""",
#         """比利时""",
#         """尼卡利单抗注射液是强生公司（申请人：Janssen-Cilag International NV，比利时）全球同步开发同步上市的1 类创新型生物制品，用于治疗罕见病"全身性重症肌无力"，该适应症已纳入2018年国家卫生健康委等五部委联合印发的《第一批罕见病目录》，用于成人和儿科用药（含12岁以上青少年患者）。""",
#         """本品的制剂生产厂是位于德国Vetter Pharma-Fertigung GmbH& Co. KG公司，原液生产厂是位于中国的无锡药明生物技术股份有限公司，III期临床试验阶段和上市注册阶段生产厂一致，并供应全球。""",
#         """为了持续改进,提升产品性能,拟评估外周高压球囊扩张导管的充卸压时间的相关数据,拟制定充卸压时间的标准。""",
#         """一项多中心、开放标签、随机对照III期临床研究：比较FS-1502和T-DM1在HER2阳性不可手术切除的局部晚期或转移性乳腺癌患者中的疗效和安全性""",
#         """共2个治疗组，治疗组1：患者将接受FS-1502联合斯鲁利单抗+化疗(5-FU/卡培他滨)治疗；治疗组2：患者将接受FS-1502联合斯鲁利单抗治疗；""",
#         """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。""",
#         """FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
#         """FS-1502联合斯鲁利单抗和卡培他滨Q3W给药治疗的6例受试者中，4例经中心实验室确认为HER2阳性，其中：PR 2例（已确认PR 1例，未认PR 1例），SD 2例。
# FS-1502联合斯鲁利单抗Q3W给药的13例受试者中，7例经中心实验室确认为HER2阳性且至少有一次基线后影像评估。""",
#         """**你好*""",
#         """阿奇霉素...红霉素.….克拉霉素...地红霉素""",
#         """要求患者在半卧或仰卧休息至少10分钟后进行采集，连续采集3次，每次间隔至少1分钟，记录数据（QT间期、QTcF间期、PR间期、QRS间期、RR间期)。""",
#         """Abstract：Objective   To establish the national standard materials of testosterone in frozen human serum，    to evaluate the accuracy，and to promote the standardization of testosterone assay. Methods Serum samples without lipemia，hemolysis and jaundice were collected. After multiple filtration sterilization，serum pools were packed into ampoules in 2-level candidate materials and were stored in -70 ℃. The homogeneity for candidate material was evaluated by single factor analysis of variance，and the stability was evaluated by linear regression analysis. The values of candidate material were assigned by reference method，and the uncertainty was calculated. The commutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. Conclusions The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.""",
#         """白细胞计数0.44*10^9/L，中性粒细胞数0.16*10^9/L，考虑发生骨髓抑制不良反应，一般生命征平稳，予升白、输血小板处理。""",
#         """Abstract：Objective   To establish the national standard materials of testosterone in frozen human serum，    to evaluate the accuracy，and to promote the standardization of testosterone assay. Methods Serum samples without lipemia，hemolysis and jaundice were collected. After multiple filtration sterilization，serum pools were packed into ampoules in 2-level candidate materials and were stored in -70 ℃. The homogeneity for candidate material was evaluated by single factor analysis of variance，and the stability was evaluated by linear regression analysis. The values of candidate material were assigned by reference method，and the uncertainty was calculated. The commutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. Conclusions The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.""",
#         """相关方法和结果的描述见模块2，第2.7.1节临床生物药剂学总结。
#         Isatuximab的分布容积较低（8.75 L），表明其主要分布在循环系统中。
#     Isatuximab的分布容积较低（8.75 L），表明其主要分布在循环系统中。
#     值得注意的是，所有缓解者的RO至少为70%（2.7.2【第*******节】，【第3.4.1节】）。
#     Isatuximab与泊马度胺联合用药的剂量/方案选择考虑了许多因素（2.7.2【第3.4节】）。

#     .不好处理。这种情况该怎么判断要不要从.改成。""",
#         """Abstract：Objective   To establish the national standard materials of testosterone in frozen human serum，    to evaluate the accuracy，and to promote the standardization of testosterone assay. Methods Serum samples without lipemia，hemolysis and jaundice were collected. After multiple filtration sterilization，serum pools were packed into ampoules in 2-level candidate materials and were stored in -70 ℃. The homogeneity for candidate material was evaluated by single factor analysis of variance，and the stability was evaluated by linear regression analysis. The values of candidate material were assigned by reference method，and the uncertainty was calculated. The commutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. Conclusions The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.""",
#         """MRPA因为空间分辨率较低、技术要求高及紧急情况下不适宜应用等缺点，在急性PTE诊断中不作为一线诊断方法。""",
#         """Abstract：Objective   To establish the national standard materials of testosterone in frozen human serum，    to evaluate the accuracy，and to promote the standardization of testosterone assay. Methods Serum samples without lipemia，hemolysis and jaundice were collected. After multiple filtration sterilization，serum pools were packed into ampoules in 2-level candidate materials and were stored in -70 ℃. The homogeneity for candidate material was evaluated by single factor analysis of variance，and the stability was evaluated by linear regression analysis. The values of candidate material were assigned by reference method，and the uncertainty was calculated. The commutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. Conclusions The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.""",
#         """摘要：目的 建立冻干人血清中睾酮国家标准物质，评价其准确度，促进睾酮检测的标准化。方法 收集无脂血、溶血和黄疸的血清样本。经多次过滤灭菌后，将血清池分装到安瓿瓶中，制成2级候选材料，并储存在-70℃。通过单因素方差分析评价候选材料的均匀性，通过线性回归分析评价稳定性。采用参考方法对候选材料进行赋值，并计算不确定度。评价了互换性。结果 候选材料水平Ⅰ和水平Ⅱ的均匀性F值分别为1.448 5和1.569 6，均小于F0.05。水平Ⅰ和水平Ⅱ在-20℃下至少可稳定30天。水平Ⅰ和水平Ⅱ的赋值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%置信区间内，表明互换性良好。结论 候选材料的均匀性、稳定性和互换性良好，其值被准确可靠地赋予。它们可以作为国家标准使用。""",
#         """mutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. 
# Conclusions
# The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.""",
#         """结果候选材料水平I和水平II的均一性F值分别为1.448 5和1.569 6，均小于F0.05，水平I和水平II在-20°C下均稳定至少30d，水平I和水平II的均一性F值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%的置信限内，表明候选材料具有良好的互换性。
# 结论：
# 候选物均匀性好，稳定性好，互换性好，赋值准确可靠，可作为国家标准品。""",
#         """You can take the EC pill up to 5 days after unprotected sex.
# The sooner you take either EC pill, the better it works.
# Some packs contain 1 pill, and some packs contain 2 pills. You can take the 2 pills together.""",
#         """您可以在无保护性行为后最多5天内服用紧急避孕药。
# 越早服用任一种紧急避孕药，效果越好。
# 有些包装含有1片药，有些包装含有2片药。您可以同时服用这2片药。""",
#         """(Full name in Chinese): 厦门建发集团有限公司""",
#         """（中文全称）：厦门建发集团有限公司""",
#         """13 Mar 2024
# 31 Mar 2020
# 12 Dec 2016
# 16 Nov 2009
# 20 Oct 2005

# Administration 
# The ACE1831 investigational drug product is provided cryopreserved in a formulation containing DMSO for IV administration.""",
#         """2024年3月13日
# 2020年3月31日
# 2016年12月12日
# 2009年11月16日
# 2005年10月20日

# 给药
# ACE1831研究性药物以含有DMSO的冷冻保存配方提供，用于静脉给药。""",
#         """Serum testosterone was measured on Day -14 (ie, the day of starting relugolix administration), Day 1 (ie, the day of starting co-administration with apalutamide), and Day 28.""",
#         """在第-14天（即开始服用relugolix的当天）、第1天（即开始与apalutamide联合给药的当天）和第28天测量血清睾酮。""",
#         """Concomitant Medication""",
#         """合并用药""",
#         """Subject Information""",
#         """受试者信息""",
#         """Relevant Medical History""",
#         """相关病史""",
#         """Physical Examination""",
#         """体格检查""",
#         """Full Clinical Chemistry""",
#         """完整临床化学检查""",
#         """Protocol versions - Number""",
#         """方案版本 - 编号""",
#         """Daewoong Pharmaceutical Co., Ltd.""",
#         """大熊制药有限公司""",
#         """ADRs were reported in 26 (21.7%) patients with 69 events in the DWP16001 group compared to 20 (16.7%) patients with 41 events in the placebo group.""",
#         """DWP16001组有26例（21.7%）患者报告了69起不良反应事件，而安慰剂组有20例（16.7%）患者报告了41起不良反应事件。""",
#         """Removal of Participants from Study""",
#         """研究中受试者的撤出""",
#         """Reagent""",
#         """试剂""",
#         """Name: Cesium chloride""",
#         """名称：氯化铯""",
#         """Manufacture: Canto chemical""",
#         """制造商：Canto化学""",
#         """Storage: 15 ~ 25°C""",
#         """储存：15 ~ 25°C""",
#         """Biofluid processed""",
#         """生物流体处理""",
#         """During the Phase II clinical trial, patients with immunological enteritis showed significant improvement after treatment with the new monoclonal antibody, with 85% of cases reporting reduced symptoms and normalized biomarkers within 8 weeks.""",
#         """在II期临床试验期间，免疫性肠炎患者在接受新型单克隆抗体治疗后显示出显著改善，85%的病例在8周内报告症状减轻和生物标志物正常化。""",
#         """The study documented 23 cases of protein urine positive among the treatment group, with 7 patients progressing to URINE PROTEIN POSITIVE GRADE 1 requiring dose modification according to the draft guidelines established by the safety monitoring committee.""",
#         """研究记录了治疗组中23例尿蛋白阳性病例，其中7例患者进展为尿蛋白阳性1级，根据安全监测委员会制定的草案指南需要调整剂量。""",
#         """Laboratory tests revealed Prothrombin time prolonged in 15% of patients receiving the investigational drug, while 8% developed Anaemia that was generally mild to moderate in severity, with only two cases of ANEMIA AGGRAVATION requiring blood transfusion.""",
#         """实验室检查显示，接受研究药物的患者中有15%出现凝血酶原时间延长，而8%出现贫血，严重程度通常为轻度至中度，仅有两例贫血加重需要输血。""",
#         """The draft guidelines for managing immune-related adverse events recommend immediate discontinuation of treatment upon confirmation of immunological enteritis Grade 3 or higher, followed by high-dose corticosteroid therapy and close monitoring of protein urine positive status.""",
#         """免疫相关不良事件管理草案指南建议，一旦确认为3级或更高级别的免疫性肠炎，立即停止治疗，随后进行大剂量皮质类固醇治疗，并密切监测尿蛋白阳性状态。""",
#         """draft guidelines""",
#         """草案指南""",
#         """13 Mar 2024\n31 Mar 2020\n12 Dec 2016\n16 Nov 2009\n20 Oct 2005\nAdministration\nThe ACE1831 investigational drug product is provided cryopreserved in a formulation containing DMSO for IV administration.""",
#         """2024年3月13日\n2020年3月31日\n2016年12月12日\n2009年11月16日\n2005年10月20日\n给药\nACE1831研究性药物以含有DMSO的冷冻保存配方提供，用于静脉给药。""",
#         """I eat from 16:30 to 17:30""",
#         """我在16:30到17:30之间进食""",
#         """I eat from 16:30 to 17:30 on 20 Oct 2005""",
#         """我在2005年10月20日16:30到17:30之间进食""",
#         """toxic megacolon [Megacolon toxic (10027115)*], Seriousness: Caused /prolonged hospitalisation, Outcome: recovering/resolving""",
#         """中毒性巨结肠[中毒性巨结肠(10027115)*]，严重性：导致/延长住院，结果：恢复中/缓解中""",
#         """CRP [C-reactive protein (10006824)*], 2025-04-05, 126""",
#         """CRP [C反应蛋白 (10006824)*], 2025-04-05, 126""",
#         """CRP [C-reactive protein (10006824)*], 269""",
#         """CRP [C反应蛋白 (10006824)*], 269""",
#         """CRP [C-reactive protein (<__NUM001>)*], <__DATE001>, <__NUM002>""",
#         """CRP [C反应蛋白 (<__NUM001>)*], <__DATE001>, <__NUM002>""",
#         """Sample libraries were prepared using the <g id=\"23\">NEBNext Ultra II</g> kit, with barcodes <bx id=\"24\"/> A01–H12<ex id=\"25\"/> assigned, sequenced on the <g id=\"26\">Illumina MiSeq</g> with 2×300 bp reads; library prep ID<bx id=\"27\"/> recorded<ex id=\"28\"/> and uploaded to the <mrk id=\"29\"/> LIMS v4.2.""",
#         """样品文库使用<g id=\"23\">NEBNext Ultra II</g>试剂盒制备，分配条形码<bx id=\"24\"/> A01–H12<ex id=\"25\"/>，在<g id=\"26\">Illumina MiSeq</g>上进行2×300 bp测序；文库制备ID<bx id=\"27\"/>已记录<ex id=\"28\"/>并上传至<mrk id=\"29\"/> LIMS v4.2。""",
#         """Sample libraries were arr[0]、A[i].""",
#         """样品文库为arr[0]、A[i]。""",
#         """Please use the function compute_ratio(input_value) to calculate the ROI.""",
#         """请使用函数compute_ratio(input_value)计算ROI。""",
#         """Access arr[0] and A[i] indices to retrieve elements from the array.""",
#         """访问arr[0]和A[i]索引以从数组中检索元素。""",
#         """The compound Mg(OH)2 exhibited a pH of 7.4 (neutral) in the solution.""",
#         """化合物Mg(OH)2在溶液中表现出7.4的pH值（中性）。""",
#         """Risk reduction was reported as HR=0.65[95% CI:0.52-0.81], p<0.0001 in the final analysis.""",
#         """在最终分析中，风险降低报告为HR=0.65[95% CI:0.52-0.81]，p<0.0001。""",
#         """Results showed a 35% (statistically significant) improvement in response rates compared to baseline.""",
#         """结果显示与基线相比，应答率提高了35%（具有统计学意义）。""",
#         """Execute re.match('(ab|cd)', text) to match patterns within the string.""",
#         """执行re.match('(ab|cd)', text)以匹配字符串中的模式。""",
#         """Dosage was set at 5 mg/kg (administered orally) for all participants.""",
#         """所有参与者的剂量设定为5 mg/kg（口服给药）。""",
#         """Ensure to cast values using (int)value in C before processing.""",
#         """在处理前确保使用(int)value在C中进行值转换。""",
#         """Refer to Figure 2 (Treatment Group) for the detailed breakdown.""",
#         """详细分析请参见图2（治疗组）。""",
#         """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-italic;color:1D1D1B;&quot;\">Automate shift preparation</g type=\"tag1\" text=\"/tag\">.""",
#         """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-italic;color:1D1D1B;&quot;\">自动化班次准备</g type=\"tag1\" text=\"/tag\">.""",
#         """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-italic;color:1D1D1B;&quot;\">— Enhance team leadership</g type=\"tag1\" text=\"/tag\">.""",
#         """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-italic;color:1D1D1B;&quot;\">— 增强团队领导力</g type=\"tag1\" text=\"/tag\">.""",
#         """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-color:1D1D1B;&quot;\">Use case two: Streamlining production maintenance with smart deviation management The end-to-end process for managing both deviation and corrective and preventive actions (CAPAs) requires 4 to 6 percent of a manufacturing site's resources and is fraught with challenges.</g type=\"tag1\" text=\"/tag\"><g id=\"2\" type=\"tag2\" text=\"tag property=&quot;x-color:1D1D1B;&quot;\">4  </g type=\"tag2\" text=\"/tag\">Common pain points include delayed detection, manual tasks, a low rate of right- first-time solutions, low effectiveness, inconsistent documentation, and a reactive process.""",
#         """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-color:1D1D1B;&quot;\">用例二：通过智能偏差管理简化生产维护 管理偏差和纠正预防措施(CAPAs)的端到端流程需要制造现场4%至6%的资源，且充满挑战。</g type=\"tag1\" text=\"/tag\"><g id=\"2\" type=\"tag2\" text=\"tag property=&quot;x-color:1D1D1B;&quot;\">4  </g type=\"tag2\" text=\"/tag\">常见痛点包括检测延迟、手动任务、首次正确解决方案率低、效率低、文档不一致以及被动反应流程。""",
#         """Intent: The Risk Matrix Report documents the RoHS certifications, compliance testing results, and related risk assessments for <x id="2" type="tag2" text="tag property=&quot;x&quot;/"/><g id="3" type="tag3" text="tag property=&quot;x-bold;color:auto;&quot;">PUREVUE <x id="4" type="tag4" text="tag property=&quot;x&quot;/"/></g type="tag3" text="/tag">""",
#         """意图：风险矩阵报告记录了<x id="2" type="tag2" text="tag property=&quot;x&quot;/"/><g id="3" type="tag3" text="tag property=&quot;x-bold;color:auto;&quot;">PUREVUE <x id="4" type="tag4" text="tag property=&quot;x&quot;/"/></g type="tag3" text="/tag">的RoHS认证、合规测试结果和相关风险评估""",
#         """John Smith visited on 15 Jan 2023""",
#         """约翰·史密斯于2023年1月15日来访""",
#         """Dr. Maria Rodriguez performed the procedure on 07/08/2024""",
#         """玛丽亚·罗德里格斯医生于2024年7月8日执行了该程序""",
#         """Patient: William Johnson, DOB: 1985-03-22""",
#         """患者：威廉·约翰逊，出生日期：1985年3月22日""",
#         """Meeting scheduled with Zhang Wei at 14:30 on Tuesday, April 5th""",
#         """周二4月5日14:30与张伟安排会议""",
#         """Last follow-up with Ms. Tanaka Yuki was conducted on 2022/12/10""",
#         """最后一次与田中由纪女士的随访于2022年12月10日进行""",
#         """The sample was collected by Dr. Carlos Mendez at 08:45 AM, September 30, 2023""",
#         """样本由卡洛斯·门德斯医生于2023年9月30日上午08:45采集""",
#         """李明于2024年5月18日至2024年6月2日期间请假""",
#         """李明 took leave from May 18, 2024 to June 2, 2024""",
#         """王芳医生将于2023年10月15日星期一上午9:00进行手术""",
#         """Dr. Wang Fang will perform surgery at 9:00 AM on Monday, October 15, 2023""",
#         """陈伟（ID: 98765432）于2022年4月1日入职""",
#         """Chen Wei (ID: 98765432) joined the company on April 1, 2022""",
#         '<__RL001>Use case two: Streamlining production maintenance with smart deviation management The end-to-end process for managing both deviation and corrective and preventive actions (CAPAs) requires 4 to 6 percent of a manufacturing site\'s resources and is fraught with challenges.<__RL002><__RL003>4  <__RL004>Common pain points include delayed detection, manual tasks, a low rate of right- first-time solutions, low effectiveness, inconsistent documentation, and a reactive process.',
#         '<__RL001>用例二：通过智能偏差管理简化生产维护 管理偏差和纠正预防措施(CAPAs)的端到端流程需要制造现场4%至6%的资源，且充满挑战。<__RL002><__RL003>4  <__RL004>常见痛点包括检测延迟、手动任务、首次正确解决方案率低、效率低、文档不一致以及被动反应流程。',
#         '<__RL001><__RL005><__RL006>Case study 3: Implementing digital quality management systems to reduce documentation errors and improve compliance tracking across multiple manufacturing sites.<__RL007><__RL008><__RL009>The transition from paper-based to digital systems resulted in 78% fewer documentation errors and 45% faster audit response times.<__RL010>',
#         '<__RL001><__RL005><__RL006>案例研究3：实施数字化质量管理系统，减少文档错误并改善多个制造现场的合规跟踪。<__RL007><__RL008><__RL009>从纸质系统过渡到数字系统使文档错误减少了78%，审计响应时间加快了45%。<__RL010>',
#         '<__RL001><__RL011><__RL012><__RL013>Figure 4: Comparison of traditional vs. AI-enhanced quality control processes<__RL014><__RL015><__RL016>The AI-enhanced system detected 99.7% of defects compared to 92.3% with traditional visual inspection methods, while reducing inspection time by 65%.<__RL017><__RL018>',
#         '<__RL001><__RL011><__RL012><__RL013>图4：传统与AI增强质量控制流程的比较<__RL014><__RL015><__RL016>AI增强系统检测到99.7%的缺陷，而传统视觉检查方法为92.3%，同时将检查时间减少了65%。<__RL017><__RL018>',
#         'Table 2.3: Key performance indicators for pharmaceutical manufacturing optimization Overall Equipment Effectiveness (OEE) improved from 67% to 85% following implementation of the integrated production monitoring system.',
#         '<__RL001><__RL019><__RL020><__RL021><__RL022>表2.3：制药生产优化的关键绩效指标<__RL023><__RL024><__RL025><__RL026>实施集成生产监控系统后，整体设备效率(OEE)从67%提高到85%。<__RL027><__RL028><__RL029>',
#         '2024-06-05',
#         'ABCD',
#         'CAPA CPU WPO',
#         'During the experiment, the voltage V<g id="2" type="tag2" text="tag property=&quot;x-sup;&quot;">2</g type="tag2" text="/tag"> of capacitance C<g id="1" type="tag1" text="tag property=&quot;x-sub;&quot;">1</g type="tag1" text="/tag"> was measured; see reference <g id="15" type="tagLink" text="tag property=&quot;x-link;color:007FAC;&quot;">[1]<g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tagLink" text="/tag">. Additional Table<bx id="10" type="tagBox" text="tag property=&quot;x-link;color:FF00FF;&quot;"/>A and footnote<ex id="11" type="tagEx" text="/tag"/>² are included; meanwhile, in <mrk id="12" mtype="emph"/> depth testing, nested<g id="3" type="tag3" text="tag property=&quot;x-sup;&quot;"><g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tag3" text="/tag"> appears, and the <span>ordinary tag</span> is preserved; additionally, there is a separator<x id="14" type="tagX" text="tag property=&quot;x&quot;"/> and the entities < & " >.',
#         '在实验中，测得电容 C<g id="1" type="tag1" text="tag property=&quot;x-sub;&quot;">1</g type="tag1" text="/tag"> 的电压 V<g id="2" type="tag2" text="tag property=&quot;x-sup;&quot;">2</g type="tag2" text="/tag">，详见文献 <g id="LNK" type="tagLink" text="tag property=&quot;x-link;color:007FAC;&quot;">[1]<g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tagLink" text="/tag">；另附表<bx id="10" type="tagBox" text="tag property=&quot;x-link;color:FF00FF;&quot;"/>A 和脚注<ex id="11" type="tagEx" text="/tag"/>²；同时在<mrk id="12" mtype="emph"/>深度测试中出现嵌套<g id="3" type="tag3" text="tag property=&quot;x-sup;&quot;"><g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tag3" text="/tag">，并且保留普通标签；此外还有分隔符<x id="X1" type="tagX" text="tag property=&quot;x&quot;"/>和实体&lt; &amp; &quot; &gt;。',
#         '在实验中，</g type="tag1" text="/tag">测得电容 C<g id="1" type="tag1" text="tag property=&quot;x-sub;&quot;">1</g type="tag1" text="/tag"> 的电压 V<g id="2" type="tag2" text="tag property=&quot;x-sup;&quot;">2</g type="tag2" text="/tag">，详见文献 <g id="LNK" type="tagLink" text="tag property=&quot;x-link;color:007FAC;&quot;">[1]<g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tagLink" text="/tag">；另附表<bx id="BX1" type="tagBox" text="tag property=&quot;x-link;color:FF00FF;&quot;"/>A 和脚注<ex id="EX1" type="tagEx" text="/tag"/>²；同时在<mrk id="M1" mtype="emph"/>深度测试中出现嵌套<g id="3" type="tag3" text="tag property=&quot;x-sup;&quot;"><g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag"></g type="tag3" text="/tag">，并且</g type="tag1" text="/tag">保留<span>普通标签</span>；此外还有分隔符<x id="X1" type="tagX" text="tag property=&quot;x&quot;"/>和实体&lt; &amp; &quot; &gt;。',
        
#         "是",
#         "LIRAGLUTIDE",
#         "2021-07-24 (-969)",
#         "2023-07-06 (-257)",
#         "0.6-1.2（毫克）",
#         "其他（QN）",
#         "皮下注射",
#         "否",
#         "14（其他：IU）",
#         "QD",
#         "皮下注射"
#         '''<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-bold;&quot;\">目录<g id=\"2\" type=\"tag2\" text=\"tag property=&quot;x-bold;italic;color:0000FF;&quot;\">（根据方案需要设计）</g type=\"tag2\" text=\"/tag\"></g type=\"tag1\" text=\"/tag\"><x id=\"3\" type=\"tag3\" text=\"tag property=&quot;x&quot;/\"/>保密声明<x id=\"4\" type=\"tag4\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"5\" type=\"tag5\" text=\"tag property=&quot;x&quot;/\"/>方案签署页<x id=\"6\" type=\"tag6\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"7\" type=\"tag7\" text=\"tag property=&quot;x&quot;/\"/>方案摘要<x id=\"8\" type=\"tag8\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"9\" type=\"tag9\" text=\"tag property=&quot;x&quot;/\"/>缩略语表<x id=\"10\" type=\"tag10\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"11\" type=\"tag11\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"12\" type=\"tag12\" text=\"tag property=&quot;x&quot;/\"/>、项目简介<x id=\"13\" type=\"tag13\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"14\" type=\"tag14\" text=\"tag property=&quot;x&quot;/\"/>1.1<x id=\"15\" type=\"tag15\" text=\"tag property=&quot;x&quot;/\"/>研究背景<x id=\"16\" type=\"tag16\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"17\" type=\"tag17\" text=\"tag property=&quot;x&quot;/\"/>1.2<x id=\"18\" type=\"tag18\" text=\"tag property=&quot;x&quot;/\"/>药理毒理学研究资料<x id=\"19\" type=\"tag19\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"20\" type=\"tag20\" text=\"tag property=&quot;x&quot;/\"/>1.2.1<x id=\"21\" type=\"tag21\" text=\"tag property=&quot;x&quot;/\"/>药效学研究<x id=\"22\" type=\"tag22\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"23\" type=\"tag23\" text=\"tag property=&quot;x&quot;/\"/>1.2.2<x id=\"24\" type=\"tag24\" text=\"tag property=&quot;x&quot;/\"/>急性毒性实验研究<x id=\"25\" type=\"tag25\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"26\" type=\"tag26\" text=\"tag property=&quot;x&quot;/\"/>1.2.3<x id=\"27\" type=\"tag27\" text=\"tag property=&quot;x&quot;/\"/>长期毒性实验<x id=\"28\" type=\"tag28\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"29\" type=\"tag29\" text=\"tag property=&quot;x&quot;/\"/>1.3<x id=\"30\" type=\"tag30\" text=\"tag property=&quot;x&quot;/\"/>临床<x id=\"31\" type=\"tag31\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"32\" type=\"tag32\" text=\"tag property=&quot;x&quot;/\"/>有效性<x id=\"33\" type=\"tag33\" text=\"tag property=&quot;x&quot;/\"/>/<x id=\"34\" type=\"tag34\" text=\"tag property=&quot;x&quot;/\"/>前期临床资料<x id=\"35\" type=\"tag35\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"36\" type=\"tag36\" text=\"tag property=&quot;x&quot;/\"/>1.4<x id=\"37\" type=\"tag37\" text=\"tag property=&quot;x&quot;/\"/>XX<x id=\"38\" type=\"tag38\" text=\"tag property=&quot;x&quot;/\"/>既往<x id=\"39\" type=\"tag39\" text=\"tag property=&quot;x&quot;/\"/>XX<x id=\"40\" type=\"tag40\" text=\"tag property=&quot;x&quot;/\"/>领域研究情况<x id=\"41\" type=\"tag41\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"42\" type=\"tag42\" text=\"tag property=&quot;x&quot;/\"/>1.5<x id=\"43\" type=\"tag43\" text=\"tag property=&quot;x&quot;/\"/>总结<x id=\"44\" type=\"tag44\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"45\" type=\"tag45\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"46\" type=\"tag46\" text=\"tag property=&quot;x&quot;/\"/>、<x id=\"47\" type=\"tag47\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"48\" type=\"tag48\" text=\"tag property=&quot;x&quot;/\"/>目的<x id=\"49\" type=\"tag49\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"50\" type=\"tag50\" text=\"tag property=&quot;x&quot;/\"/>3<x id=\"51\" type=\"tag51\" text=\"tag property=&quot;x&quot;/\"/>、临床<x id=\"52\" type=\"tag52\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"53\" type=\"tag53\" text=\"tag property=&quot;x&quot;/\"/>总体设计及方法<x id=\"54\" type=\"tag54\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"55\" type=\"tag55\" text=\"tag property=&quot;x&quot;/\"/>4<x id=\"56\" type=\"tag56\" text=\"tag property=&quot;x&quot;/\"/>、疾病诊断标准<x id=\"57\" type=\"tag57\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"58\" type=\"tag58\" text=\"tag property=&quot;x&quot;/\"/>4.1<x id=\"59\" type=\"tag59\" text=\"tag property=&quot;x&quot;/\"/> XXXX<x id=\"60\" type=\"tag60\" text=\"tag property=&quot;x&quot;/\"/>病<x id=\"61\" type=\"tag61\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"62\" type=\"tag62\" text=\"tag property=&quot;x&quot;/\"/>4.<x id=\"63\" type=\"tag63\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"64\" type=\"tag64\" text=\"tag property=&quot;x&quot;/\"/>中医辨证及量化评分<x id=\"65\" type=\"tag65\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"66\" type=\"tag66\" text=\"tag property=&quot;x&quot;/\"/>4.<x id=\"67\" type=\"tag67\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"68\" type=\"tag68\" text=\"tag property=&quot;x&quot;/\"/>.1<x id=\"69\" type=\"tag69\" text=\"tag property=&quot;x&quot;/\"/>中医辨证标准<x id=\"70\" type=\"tag70\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"71\" type=\"tag71\" text=\"tag property=&quot;x&quot;/\"/>4.<x id=\"72\" type=\"tag72\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"73\" type=\"tag73\" text=\"tag property=&quot;x&quot;/\"/>.2<x id=\"74\" type=\"tag74\" text=\"tag property=&quot;x&quot;/\"/>中医证候评分表<x id=\"75\" type=\"tag75\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"76\" type=\"tag76\" text=\"tag property=&quot;x&quot;/\"/>5<x id=\"77\" type=\"tag77\" text=\"tag property=&quot;x&quot;/\"/>、研究人群<x id=\"78\" type=\"tag78\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"79\" type=\"tag79\" text=\"tag property=&quot;x&quot;/\"/>5.1<x id=\"80\" type=\"tag80\" text=\"tag property=&quot;x&quot;/\"/>纳入病例标准<x id=\"81\" type=\"tag81\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"82\" type=\"tag82\" text=\"tag property=&quot;x&quot;/\"/>5.2<x id=\"83\" type=\"tag83\" text=\"tag property=&quot;x&quot;/\"/>排除病例标准<x id=\"84\" type=\"tag84\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"85\" type=\"tag85\" text=\"tag property=&quot;x&quot;/\"/>5.3<x id=\"86\" type=\"tag86\" text=\"tag property=&quot;x&quot;/\"/>受试者退出<x id=\"87\" type=\"tag87\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"88\" type=\"tag88\" text=\"tag property=&quot;x&quot;/\"/>的条件<x id=\"89\" type=\"tag89\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"90\" type=\"tag90\" text=\"tag property=&quot;x&quot;/\"/>5.3.1<x id=\"91\" type=\"tag91\" text=\"tag property=&quot;x&quot;/\"/>研究者决定的退出<x id=\"92\" type=\"tag92\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"93\" type=\"tag93\" text=\"tag property=&quot;x&quot;/\"/>5.3.2<x id=\"94\" type=\"tag94\" text=\"tag property=&quot;x&quot;/\"/>受试者自行退出<x id=\"95\" type=\"tag95\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"96\" type=\"tag96\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"97\" type=\"tag97\" text=\"tag property=&quot;x&quot;/\"/>5.4<x id=\"98\" type=\"tag98\" text=\"tag property=&quot;x&quot;/\"/>剔除病例标准<x id=\"99\" type=\"tag99\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"100\" type=\"tag100\" text=\"tag property=&quot;x&quot;/\"/>5.5<x id=\"101\" type=\"tag101\" text=\"tag property=&quot;x&quot;/\"/>全面中止<x id=\"102\" type=\"tag102\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"103\" type=\"tag103\" text=\"tag property=&quot;x&quot;/\"/>的标准<x id=\"104\" type=\"tag104\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"105\" type=\"tag105\" text=\"tag property=&quot;x&quot;/\"/>6<x id=\"106\" type=\"tag106\" text=\"tag property=&quot;x&quot;/\"/>、研究药物及治疗方法<x id=\"107\" type=\"tag107\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"108\" type=\"tag108\" text=\"tag property=&quot;x&quot;/\"/>6.1<x id=\"109\" type=\"tag109\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"110\" type=\"tag110\" text=\"tag property=&quot;x&quot;/\"/>用药<x id=\"111\" type=\"tag111\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"112\" type=\"tag112\" text=\"tag property=&quot;x&quot;/\"/>6.2<x id=\"113\" type=\"tag113\" text=\"tag property=&quot;x&quot;/\"/>给药方案及疗程<x id=\"114\" type=\"tag114\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"115\" type=\"tag115\" text=\"tag property=&quot;x&quot;/\"/>6.3<x id=\"116\" type=\"tag116\" text=\"tag property=&quot;x&quot;/\"/>标准治疗（<x id=\"117\" type=\"tag117\" text=\"tag property=&quot;x&quot;/\"/>SoC<x id=\"118\" type=\"tag118\" text=\"tag property=&quot;x&quot;/\"/>）方案<x id=\"119\" type=\"tag119\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"120\" type=\"tag120\" text=\"tag property=&quot;x&quot;/\"/>6.4<x id=\"121\" type=\"tag121\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"122\" type=\"tag122\" text=\"tag property=&quot;x&quot;/\"/>用药包装<x id=\"123\" type=\"tag123\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"124\" type=\"tag124\" text=\"tag property=&quot;x&quot;/\"/>6.5<x id=\"125\" type=\"tag125\" text=\"tag property=&quot;x&quot;/\"/>药物分配、保存与回收<x id=\"126\" type=\"tag126\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"127\" type=\"tag127\" text=\"tag property=&quot;x&quot;/\"/>6.6<x id=\"128\" type=\"tag128\" text=\"tag property=&quot;x&quot;/\"/>合并用药及治疗<x id=\"129\" type=\"tag129\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"130\" type=\"tag130\" text=\"tag property=&quot;x&quot;/\"/>6.7<x id=\"131\" type=\"tag131\" text=\"tag property=&quot;x&quot;/\"/>应急治疗<x id=\"132\" type=\"tag132\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"133\" type=\"tag133\" text=\"tag property=&quot;x&quot;/\"/>7<x id=\"134\" type=\"tag134\" text=\"tag property=&quot;x&quot;/\"/>、观测项目与指标<x id=\"135\" type=\"tag135\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"136\" type=\"tag136\" text=\"tag property=&quot;x&quot;/\"/>7.1<x id=\"137\" type=\"tag137\" text=\"tag property=&quot;x&quot;/\"/>安全性观测<x id=\"138\" type=\"tag138\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"139\" type=\"tag139\" text=\"tag property=&quot;x&quot;/\"/>7.2<x id=\"140\" type=\"tag140\" text=\"tag property=&quot;x&quot;/\"/>影像学检查<x id=\"141\" type=\"tag141\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"142\" type=\"tag142\" text=\"tag property=&quot;x&quot;/\"/>7.3<x id=\"143\" type=\"tag143\" text=\"tag property=&quot;x&quot;/\"/>疗效评估指标<x id=\"144\" type=\"tag144\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"145\" type=\"tag145\" text=\"tag property=&quot;x&quot;/\"/>7.3.1<x id=\"146\" type=\"tag146\" text=\"tag property=&quot;x&quot;/\"/>主要疗效指标<x id=\"147\" type=\"tag147\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"148\" type=\"tag148\" text=\"tag property=&quot;x&quot;/\"/>7.3.2<x id=\"149\" type=\"tag149\" text=\"tag property=&quot;x&quot;/\"/>次要疗效指标<x id=\"150\" type=\"tag150\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"151\" type=\"tag151\" text=\"tag property=&quot;x&quot;/\"/>7.3.3<x id=\"152\" type=\"tag152\" text=\"tag property=&quot;x&quot;/\"/>特异性指标<x id=\"153\" type=\"tag153\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"154\" type=\"tag154\" text=\"tag property=&quot;x&quot;/\"/>8<x id=\"155\" type=\"tag155\" text=\"tag property=&quot;x&quot;/\"/>、<x id=\"156\" type=\"tag156\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"157\" type=\"tag157\" text=\"tag property=&quot;x&quot;/\"/>步骤与访视流程<x id=\"158\" type=\"tag158\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"159\" type=\"tag159\" text=\"tag property=&quot;x&quot;/\"/>8.1<x id=\"160\" type=\"tag160\" text=\"tag property=&quot;x&quot;/\"/>筛选期<x id=\"161\" type=\"tag161\" text=\"tag property=&quot;x&quot;/\"/>/<x id=\"162\" type=\"tag162\" text=\"tag property=&quot;x&quot;/\"/>基线期（访视<x id=\"163\" type=\"tag163\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"164\" type=\"tag164\" text=\"tag property=&quot;x&quot;/\"/>，<x id=\"165\" type=\"tag165\" text=\"tag property=&quot;x&quot;/\"/>-<x id=\"166\" type=\"tag166\" text=\"tag property=&quot;x&quot;/\"/>X<x id=\"167\" type=\"tag167\" text=\"tag property=&quot;x&quot;/\"/>~0<x id=\"168\" type=\"tag168\" text=\"tag property=&quot;x&quot;/\"/>天）<x id=\"169\" type=\"tag169\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"170\" type=\"tag170\" text=\"tag property=&quot;x&quot;/\"/>8.2<x id=\"171\" type=\"tag171\" text=\"tag property=&quot;x&quot;/\"/>治疗期（访视<x id=\"172\" type=\"tag172\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"173\" type=\"tag173\" text=\"tag property=&quot;x&quot;/\"/>，第<x id=\"174\" type=\"tag174\" text=\"tag property=&quot;x&quot;/\"/>X<x id=\"175\" type=\"tag175\" text=\"tag property=&quot;x&quot;/\"/>周<x id=\"176\" type=\"tag176\" text=\"tag property=&quot;x&quot;/\"/>±<x id=\"177\" type=\"tag177\" text=\"tag property=&quot;x&quot;/\"/>X<x id=\"178\" type=\"tag178\" text=\"tag property=&quot;x&quot;/\"/>天）<x id=\"179\" type=\"tag179\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"180\" type=\"tag180\" text=\"tag property=&quot;x&quot;/\"/>8.3<x id=\"181\" type=\"tag181\" text=\"tag property=&quot;x&quot;/\"/>治疗期（访视<x id=\"182\" type=\"tag182\" text=\"tag property=&quot;x&quot;/\"/>3<x id=\"183\" type=\"tag183\" text=\"tag property=&quot;x&quot;/\"/>，第<x id=\"184\" type=\"tag184\" text=\"tag property=&quot;x&quot;/\"/>X<x id=\"185\" type=\"tag185\" text=\"tag property=&quot;x&quot;/\"/>周<x id=\"186\" type=\"tag186\" text=\"tag property=&quot;x&quot;/\"/>±<x id=\"187\" type=\"tag187\" text=\"tag property=&quot;x&quot;/\"/>X<x id=\"188\" type=\"tag188\" text=\"tag property=&quot;x&quot;/\"/>天）<x id=\"189\" type=\"tag189\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"190\" type=\"tag190\" text=\"tag property=&quot;x&quot;/\"/>8.4<x id=\"191\" type=\"tag191\" text=\"tag property=&quot;x&quot;/\"/>治疗期（访视<x id=\"192\" type=\"tag192\" text=\"tag property=&quot;x&quot;/\"/>4<x id=\"193\" type=\"tag193\" text=\"tag property=&quot;x&quot;/\"/>，第<x id=\"194\" type=\"tag194\" text=\"tag property=&quot;x&quot;/\"/>X<x id=\"195\" type=\"tag195\" text=\"tag property=&quot;x&quot;/\"/>周<x id=\"196\" type=\"tag196\" text=\"tag property=&quot;x&quot;/\"/>±<x id=\"197\" type=\"tag197\" text=\"tag property=&quot;x&quot;/\"/>X<x id=\"198\" type=\"tag198\" text=\"tag property=&quot;x&quot;/\"/>天）<x id=\"199\" type=\"tag199\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"200\" type=\"tag200\" text=\"tag property=&quot;x&quot;/\"/>8.5<x id=\"201\" type=\"tag201\" text=\"tag property=&quot;x&quot;/\"/>随访期<x id=\"202\" type=\"tag202\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"203\" type=\"tag203\" text=\"tag property=&quot;x&quot;/\"/>9<x id=\"204\" type=\"tag204\" text=\"tag property=&quot;x&quot;/\"/>、疗效评定标准<x id=\"205\" type=\"tag205\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"206\" type=\"tag206\" text=\"tag property=&quot;x&quot;/\"/>9.1<x id=\"207\" type=\"tag207\" text=\"tag property=&quot;x&quot;/\"/>主要疗效指标<x id=\"208\" type=\"tag208\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"209\" type=\"tag209\" text=\"tag property=&quot;x&quot;/\"/>9.2<x id=\"210\" type=\"tag210\" text=\"tag property=&quot;x&quot;/\"/>次要疗效指标<x id=\"211\" type=\"tag211\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"212\" type=\"tag212\" text=\"tag property=&quot;x&quot;/\"/>9.3<x id=\"213\" type=\"tag213\" text=\"tag property=&quot;x&quot;/\"/>特异性指标<x id=\"214\" type=\"tag214\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"215\" type=\"tag215\" text=\"tag property=&quot;x&quot;/\"/>9.4<x id=\"216\" type=\"tag216\" text=\"tag property=&quot;x&quot;/\"/>安全性评价标准<x id=\"217\" type=\"tag217\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"218\" type=\"tag218\" text=\"tag property=&quot;x&quot;/\"/>10<x id=\"219\" type=\"tag219\" text=\"tag property=&quot;x&quot;/\"/>、质量控制与保证<x id=\"220\" type=\"tag220\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"221\" type=\"tag221\" text=\"tag property=&quot;x&quot;/\"/>11<x id=\"222\" type=\"tag222\" text=\"tag property=&quot;x&quot;/\"/>、不良事件的观察、记录和报告方法<x id=\"223\" type=\"tag223\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"224\" type=\"tag224\" text=\"tag property=&quot;x&quot;/\"/>11.1<x id=\"225\" type=\"tag225\" text=\"tag property=&quot;x&quot;/\"/>不良事件（<x id=\"226\" type=\"tag226\" text=\"tag property=&quot;x&quot;/\"/>AE<x id=\"227\" type=\"tag227\" text=\"tag property=&quot;x&quot;/\"/>）<x id=\"228\" type=\"tag228\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"229\" type=\"tag229\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"230\" type=\"tag230\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"231\" type=\"tag231\" text=\"tag property=&quot;x&quot;/\"/>.2<x id=\"232\" type=\"tag232\" text=\"tag property=&quot;x&quot;/\"/>获取不良事件信息<x id=\"233\" type=\"tag233\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"234\" type=\"tag234\" text=\"tag property=&quot;x&quot;/\"/>11<x id=\"235\" type=\"tag235\" text=\"tag property=&quot;x&quot;/\"/>.3<x id=\"236\" type=\"tag236\" text=\"tag property=&quot;x&quot;/\"/>不良事件记录<x id=\"237\" type=\"tag237\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"238\" type=\"tag238\" text=\"tag property=&quot;x&quot;/\"/>11.4<x id=\"239\" type=\"tag239\" text=\"tag property=&quot;x&quot;/\"/>不良事件的严重程度<x id=\"240\" type=\"tag240\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"241\" type=\"tag241\" text=\"tag property=&quot;x&quot;/\"/>11.5<x id=\"242\" type=\"tag242\" text=\"tag property=&quot;x&quot;/\"/>不良事件与药物因果关系判断<x id=\"243\" type=\"tag243\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"244\" type=\"tag244\" text=\"tag property=&quot;x&quot;/\"/>11.5.1<x id=\"245\" type=\"tag245\" text=\"tag property=&quot;x&quot;/\"/>因果判断的有关指标<x id=\"246\" type=\"tag246\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"247\" type=\"tag247\" text=\"tag property=&quot;x&quot;/\"/>11.5.2<x id=\"248\" type=\"tag248\" text=\"tag property=&quot;x&quot;/\"/>因果关系的判断<x id=\"249\" type=\"tag249\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"250\" type=\"tag250\" text=\"tag property=&quot;x&quot;/\"/>11.6<x id=\"251\" type=\"tag251\" text=\"tag property=&quot;x&quot;/\"/>不良事件处理<x id=\"252\" type=\"tag252\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"253\" type=\"tag253\" text=\"tag property=&quot;x&quot;/\"/>11.6.1<x id=\"254\" type=\"tag254\" text=\"tag property=&quot;x&quot;/\"/>记录与报告<x id=\"255\" type=\"tag255\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"256\" type=\"tag256\" text=\"tag property=&quot;x&quot;/\"/>11.6.2<x id=\"257\" type=\"tag257\" text=\"tag property=&quot;x&quot;/\"/>紧急破盲信封的拆阅与处理<x id=\"258\" type=\"tag258\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"259\" type=\"tag259\" text=\"tag property=&quot;x&quot;/\"/>11.6.3<x id=\"260\" type=\"tag260\" text=\"tag property=&quot;x&quot;/\"/>不良事件处理<x id=\"261\" type=\"tag261\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"262\" type=\"tag262\" text=\"tag property=&quot;x&quot;/\"/>11.7<x id=\"263\" type=\"tag263\" text=\"tag property=&quot;x&quot;/\"/>药物不良反应<x id=\"264\" type=\"tag264\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"265\" type=\"tag265\" text=\"tag property=&quot;x&quot;/\"/>11.8<x id=\"266\" type=\"tag266\" text=\"tag property=&quot;x&quot;/\"/>非预期不良反应定义<x id=\"267\" type=\"tag267\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"268\" type=\"tag268\" text=\"tag property=&quot;x&quot;/\"/>11.9<x id=\"269\" type=\"tag269\" text=\"tag property=&quot;x&quot;/\"/>妊娠事件<x id=\"270\" type=\"tag270\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"271\" type=\"tag271\" text=\"tag property=&quot;x&quot;/\"/>11.10<x id=\"272\" type=\"tag272\" text=\"tag property=&quot;x&quot;/\"/>严重不良事件（<x id=\"273\" type=\"tag273\" text=\"tag property=&quot;x&quot;/\"/>SAE<x id=\"274\" type=\"tag274\" text=\"tag property=&quot;x&quot;/\"/>）<x id=\"275\" type=\"tag275\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"276\" type=\"tag276\" text=\"tag property=&quot;x&quot;/\"/>11.10.1<x id=\"277\" type=\"tag277\" text=\"tag property=&quot;x&quot;/\"/>发生<x id=\"278\" type=\"tag278\" text=\"tag property=&quot;x&quot;/\"/>SAE<x id=\"279\" type=\"tag279\" text=\"tag property=&quot;x&quot;/\"/>时对受试者的处理<x id=\"280\" type=\"tag280\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"281\" type=\"tag281\" text=\"tag property=&quot;x&quot;/\"/>11.10.2 SAE<x id=\"282\" type=\"tag282\" text=\"tag property=&quot;x&quot;/\"/>的记录与报告<x id=\"283\" type=\"tag283\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"284\" type=\"tag284\" text=\"tag property=&quot;x&quot;/\"/>12<x id=\"285\" type=\"tag285\" text=\"tag property=&quot;x&quot;/\"/>、<x id=\"286\" type=\"tag286\" text=\"tag property=&quot;x&quot;/\"/>CRF<x id=\"287\" type=\"tag287\" text=\"tag property=&quot;x&quot;/\"/>的记录与保存<x id=\"288\" type=\"tag288\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"289\" type=\"tag289\" text=\"tag property=&quot;x&quot;/\"/>13<x id=\"290\" type=\"tag290\" text=\"tag property=&quot;x&quot;/\"/>、观察、记录、总结的有关要求<x id=\"291\" type=\"tag291\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"292\" type=\"tag292\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"293\" type=\"tag293\" text=\"tag property=&quot;x&quot;/\"/>4<x id=\"294\" type=\"tag294\" text=\"tag property=&quot;x&quot;/\"/>.1<x id=\"295\" type=\"tag295\" text=\"tag property=&quot;x&quot;/\"/>终止<x id=\"296\" type=\"tag296\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"297\" type=\"tag297\" text=\"tag property=&quot;x&quot;/\"/>标准<x id=\"298\" type=\"tag298\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"299\" type=\"tag299\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"300\" type=\"tag300\" text=\"tag property=&quot;x&quot;/\"/>4.<x id=\"301\" type=\"tag301\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"302\" type=\"tag302\" text=\"tag property=&quot;x&quot;/\"/>方案偏离或违背<x id=\"303\" type=\"tag303\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"304\" type=\"tag304\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"305\" type=\"tag305\" text=\"tag property=&quot;x&quot;/\"/>4<x id=\"306\" type=\"tag306\" text=\"tag property=&quot;x&quot;/\"/>.3<x id=\"307\" type=\"tag307\" text=\"tag property=&quot;x&quot;/\"/>方案修订情况<x id=\"308\" type=\"tag308\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"309\" type=\"tag309\" text=\"tag property=&quot;x&quot;/\"/>15<x id=\"310\" type=\"tag310\" text=\"tag property=&quot;x&quot;/\"/>、伦理规范及知情同意<x id=\"311\" type=\"tag311\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"312\" type=\"tag312\" text=\"tag property=&quot;x&quot;/\"/>15.1<x id=\"313\" type=\"tag313\" text=\"tag property=&quot;x&quot;/\"/>伦理规范<x id=\"314\" type=\"tag314\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"315\" type=\"tag315\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"316\" type=\"tag316\" text=\"tag property=&quot;x&quot;/\"/>5<x id=\"317\" type=\"tag317\" text=\"tag property=&quot;x&quot;/\"/>.2<x id=\"318\" type=\"tag318\" text=\"tag property=&quot;x&quot;/\"/>知情同意<x id=\"319\" type=\"tag319\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"320\" type=\"tag320\" text=\"tag property=&quot;x&quot;/\"/>16<x id=\"321\" type=\"tag321\" text=\"tag property=&quot;x&quot;/\"/>、受试者用药的依从性<x id=\"322\" type=\"tag322\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"323\" type=\"tag323\" text=\"tag property=&quot;x&quot;/\"/>17<x id=\"324\" type=\"tag324\" text=\"tag property=&quot;x&quot;/\"/>、样本量计算<x id=\"325\" type=\"tag325\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"326\" type=\"tag326\" text=\"tag property=&quot;x&quot;/\"/>18<x id=\"327\" type=\"tag327\" text=\"tag property=&quot;x&quot;/\"/>、数据管理<x id=\"328\" type=\"tag328\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"329\" type=\"tag329\" text=\"tag property=&quot;x&quot;/\"/>18.1<x id=\"330\" type=\"tag330\" text=\"tag property=&quot;x&quot;/\"/>数据记录的监查<x id=\"331\" type=\"tag331\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"332\" type=\"tag332\" text=\"tag property=&quot;x&quot;/\"/>18.2<x id=\"333\" type=\"tag333\" text=\"tag property=&quot;x&quot;/\"/>数据的录入与修改<x id=\"334\" type=\"tag334\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"335\" type=\"tag335\" text=\"tag property=&quot;x&quot;/\"/>19<x id=\"336\" type=\"tag336\" text=\"tag property=&quot;x&quot;/\"/>、统计分析<x id=\"337\" type=\"tag337\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"338\" type=\"tag338\" text=\"tag property=&quot;x&quot;/\"/>19.1<x id=\"339\" type=\"tag339\" text=\"tag property=&quot;x&quot;/\"/>统计分析数据的选择<x id=\"340\" type=\"tag340\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"341\" type=\"tag341\" text=\"tag property=&quot;x&quot;/\"/>19.2<x id=\"342\" type=\"tag342\" text=\"tag property=&quot;x&quot;/\"/>统计分析内容<x id=\"343\" type=\"tag343\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"344\" type=\"tag344\" text=\"tag property=&quot;x&quot;/\"/>19.3<x id=\"345\" type=\"tag345\" text=\"tag property=&quot;x&quot;/\"/>统计分析计划<x id=\"346\" type=\"tag346\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"347\" type=\"tag347\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"348\" type=\"tag348\" text=\"tag property=&quot;x&quot;/\"/>0<x id=\"349\" type=\"tag349\" text=\"tag property=&quot;x&quot;/\"/>、方案的修改<x id=\"350\" type=\"tag350\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"351\" type=\"tag351\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"352\" type=\"tag352\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"353\" type=\"tag353\" text=\"tag property=&quot;x&quot;/\"/>��资料总结及保存<x id=\"354\" type=\"tag354\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"355\" type=\"tag355\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"356\" type=\"tag356\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"357\" type=\"tag357\" text=\"tag property=&quot;x&quot;/\"/>.1<x id=\"358\" type=\"tag358\" text=\"tag property=&quot;x&quot;/\"/>资料总结<x id=\"359\" type=\"tag359\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"360\" type=\"tag360\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"361\" type=\"tag361\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"362\" type=\"tag362\" text=\"tag property=&quot;x&quot;/\"/>.2<x id=\"363\" type=\"tag363\" text=\"tag property=&quot;x&quot;/\"/>资料保存<x id=\"364\" type=\"tag364\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"365\" type=\"tag365\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"366\" type=\"tag366\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"367\" type=\"tag367\" text=\"tag property=&quot;x&quot;/\"/>、参考���献<x id=\"368\" type=\"tag368\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"369\" type=\"tag369\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"370\" type=\"tag370\" text=\"tag property=&quot;x&quot;/\"/>3<x id=\"371\" type=\"tag371\" text=\"tag property=&quot;x&quot;/\"/>、临床<x id=\"372\" type=\"tag372\" text=\"tag property=&quot;x&quot;/\"/>试验/研究<x id=\"373\" type=\"tag373\" text=\"tag property=&quot;x&quot;/\"/>流程图<x id=\"374\" type=\"tag374\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"375\" type=\"tag375\" text=\"tag property=&quot;x&quot;/\"/>附录<x id=\"376\" type=\"tag376\" text=\"tag property=&quot;x&quot;/\"/>1<x id=\"377\" type=\"tag377\" text=\"tag property=&quot;x&quot;/\"/>：避孕措施、育龄女性的定义和避孕要求<x id=\"378\" type=\"tag378\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"379\" type=\"tag379\" text=\"tag property=&quot;x&quot;/\"/>附录<x id=\"380\" type=\"tag380\" text=\"tag property=&quot;x&quot;/\"/>2<x id=\"381\" type=\"tag381\" text=\"tag property=&quot;x&quot;/\"/>：临床和实验室不良事件的处理<x id=\"382\" type=\"tag382\" text=\"tag property=&quot;x&quot;/\"/>\t<x id=\"383\" type=\"tag383\" text=\"tag property=&quot;x&quot;/\"/>附录<x id=\"384\" type=\"tag384\" text=\"tag property=&quot;x&quot;/\"/>3<x id=\"385\" type=\"tag385\" text=\"tag property=&quot;x&quot;/\"/>：<x id=\"386\" type=\"tag386\" text=\"tag property=&quot;x&quot;/\"/>样品采集、运输与储存规范''',
#         '试验/研究组xx例，对照组xx例，共计xx例<g id="1" type="tag1" text="tag property=&quot;x-color:000000;&quot;">。</g type="tag1" text="/tag">',
        '试验/研究组xx例，对照组xx例，共计xx例<g id="1">。</g>',
#         '试验/研究组xx例，对照组xx例，共计xx例<__NT001>',
#         '试验/研究组xx例，<__TERM001>xx例，共计xx例<__NT001>',
#         '周六的朝阳刚爬上梧桐树梢，林小玉就提着水壶走进了社区花园。',
#         # 协议／接口类
#         "在本次试验中，使用了API、HTTP/2和TCP/IP协议进行数据传输。",
#         "请将日志文件通过FTP://DATA.SERVER.COM/UPLOAD上传。",
#         # 术语缩写
#         "患者出现了AE（ADVERSE EVENT）和SAE（SERIOUS ADVERSE EVENT）。",
#         "本研究严格遵循GCP和ICH-E6指导原则。",
#         # 设备／代码
#         "设备型号为XYZ-1234，序列号为SN/5678-90/AB。",
#         "QC阶段，CPU使用率超过90%，GPU使用率超过80%。",
#         # URL／邮箱
#         "详细文档请访问https://WWW.EXAMPLE.COM/PATH或联系*******************。",
#         # 其它混排
#         "在QA/QC测试中，所有结果均符合PASS标准。",

#         # 链接
#         # 1. 简单 HTTP 链接
#         "请前往 http://foo.com 获取更多信息。",
#         # 2. HTTPS 带路径和查询
#         "可以通过 https://foo.com/bar?arg=值 下载资源。",
#         # 3. FTP 下载链接
#         "报告请使用 ftp://数据.服务器.cn/文件.zip 下载。",
#         # 4. 括号内的 URL
#         "请查看（https://foo.com/doc）了解详情。",
#         # 5. 含用户名/密码、端口的链接
#         "您可以登录 https://user:<EMAIL>:443/dashboard。",
#         # 6. HTML 标签内的 URL
#         '资源链接：<img src="https://cdn.example.com/image.png" />',
#         # 7. IP 地址 + 端口
#         "设备管理界面位于 http://********:8000/。",
#         # 8. 紧跟中文逗号的 URL
#         "请访问 https://foo.com/path，获取最新数据。",
#         # 9. URL 紧跟中文左括号
#         "参见链接：https://foo.com/path(详情)。",
#         # 10. 多个 URL 混合
#         "备用地址 http://backup.example.com 和 https://mirror.example.org 都可使用。",
#         "Ki-67:25%",
#         "HGB : 98 g/L (↓)",
#         "Version:V2",
#         "比例 : 50%",
#         "错误码 404: 未找到",
#         "Key:值",
#         "1、在急性期患者中，使用CT扫描评估肺部病变。",
#         "1) 在病程评估中通过MRI观察脑部结构改变。",
#         "1）采用AUC₀–∞/dose比值评价药物清除率。",
#         "1-动态心电监护可连续记录24小时心率变异性。",
#         "-1 剂量调整取决于肝功能测试结果。",
#         "（1）阶段使用NCT01234567登记的试验方案进行PK分析。",
#         "(1) 在I期临床试验中，患者接受新型单克隆抗体治疗。",
#         "1、2）对比组(n=30)在第2天进行血常规检查。",
#         "1)2、联合用药可能影响血浆蛋白结合率。",
#         "1-2）剂量梯度设置为0.5 mg/kg - 2.0 mg/kg。",
#         "-1-）这是一段含有特殊序号和符号的描述。",
#         "(1) （2）测试术语IC₅₀在实验结果中的影响。",
#         "（1）(2) 公式：V_d = CL/F × τ，用于计算分布容积。",
#         "1)（1）医疗器械型号：MD-220A，主要用于心脏起搏。",
#         "1、心电转复装置型号：EC-200，检查序号1、2、3。",
#         "1) 静脉滴注(IV)给药后Tₘₐₓ平均为2 h。",
#         "（1）患者报告头痛评分为VAS 4分。",
#         "(1) 患者接受剂量为10 mg/m²的化疗方案。",
#         "1- 支气管镜检查显示气道狭窄程度达到Ⅱ级。",
#         "-1 (1) 这是一条混合各种括号和符号的序号测试。",
#         '验/研究组xx例，对照组xx例，共计xx例<g id="1">。</g>',
#         "Ki-67:25%",
#         "HGB : 98 g/L (↓)",
#         "Version:V2",
#         "比例 : 50%",
#         "错误码 404: 未找到",
#         "Key:值",
        '无',
        '周六的朝阳刚爬上梧桐树梢，林小玉就提着水壶走进了社区花园。',
        '周六的朝阳刚爬上梧桐树梢，Xiaoyu Lin就提着水壶走进了社区花园。',
        '周六的sun刚爬上梧桐树梢，Xiaoyu Lin就提着水壶走进了社区花园。',
        'SIT_SYS_逻辑视图_CAN_限束器及遥控器控制',
        'SIT_SYS_UIV_TUI_基本控件',
        'SIT_SYS_UIV_TUI_悬吊相关UI',
        'SIT_SYS_UIV_胸片架_控制模块',
        'SIT_SYS_用户界面_无线遥控器_基本操作',
        'SIT_SYS_UIV_AWS_平板状态显示',
        'SIT_SYS_UIV_AWS_检查界面_变更',
        'SIT_SYS_UIV_TUI_随动控制'
    ]
    en_test_src_texts = [
#         """mutability was evaluated. Results   The F values of homogeneity for candidate material level Ⅰ and level   Ⅱ were 1.448 5 and 1.569 6，respectively. All of them were <F0.05. The stabilities of level Ⅰ and level Ⅱ can be  last at least 30 d at -20 ℃. The assigned values of level Ⅰ and level Ⅱ were（0.22±0.05）ng/mL（k=2） and（3.67±0.16）ng/mL （k=2），respectively. The values were all within 95% conﬁdence interval，which showed that the commutability was good. 
# Conclusions
# The homogeneity，stability and commutability of candidate material are good，and their values are assigned accurately and reliably. They can be used as national standard.""",
#         """结果候选材料水平I和水平II的均一性F值分别为1.448 5和1.569 6，均小于F0.05，水平I和水平II在-20°C下均稳定至少30d，水平I和水平II的均一性F值分别为（0.22±0.05）ng/mL（k=2）和（3.67±0.16）ng/mL（k=2），均在95%的置信限内，表明候选材料具有良好的互换性。
# 结论：
# 候选物均匀性好，稳定性好，互换性好，赋值准确可靠，可作为国家标准品。""",
#         """You can take the EC pill up to 5 days after unprotected sex.
# The sooner you take either EC pill, the better it works.
# Some packs contain 1 pill, and some packs contain 2 pills. You can take the 2 pills together.""",
#         """(Full name in Chinese): 厦门建发集团有限公司""",
#         """13 Mar 2024
# 31 Mar 2020
# 12 Dec 2016
# 16 Nov 2009
# 20 Oct 2005

# Administration 
# The ACE1831 investigational drug product is provided cryopreserved in a formulation containing DMSO for IV administration.""",

#         """Serum testosterone was measured on Day -14 (ie, the day of starting relugolix administration), Day 1 (ie, the day of starting co-administration with apalutamide), and Day 28.""",
#         """Concomitant Medication""",
#         """Subject Information""",
#         """Relevant Medical History""",
#         """Physical Examination""",
#         """Full Clinical Chemistry""",
#         """Protocol versions - Number""",
#         """Daewoong Pharmaceutical Co., Ltd.""",
#         """ADRs were reported in 26 (21.7%) patients with 69 events in the DWP16001 group compared to 20 (16.7%) patients with 41 events in the placebo group.""",
#         """Removal of Participants from Study""",
#         """Reagent""",
#         """Name: Cesium chloride""",
#         """Manufacture: Canto chemical""",
#         """Storage: 15 ~ 25°C""",
#         """Biofluid processed""",
#         """During the Phase II clinical trial, patients with immunological enteritis showed significant improvement after treatment with the new monoclonal antibody, with 85% of cases reporting reduced symptoms and normalized biomarkers within 8 weeks.""",
#         """The study documented 23 cases of protein urine positive among the treatment group, with 7 patients progressing to URINE PROTEIN POSITIVE GRADE 1 requiring dose modification according to the draft guidelines established by the safety monitoring committee.""",
#         """Laboratory tests revealed Prothrombin time prolonged in 15% of patients receiving the investigational drug, while 8% developed Anaemia that was generally mild to moderate in severity, with only two cases of ANEMIA AGGRAVATION requiring blood transfusion.""",
#         """The draft guidelines for managing immune-related adverse events recommend immediate discontinuation of treatment upon confirmation of immunological enteritis Grade 3 or higher, followed by high-dose corticosteroid therapy and close monitoring of protein urine positive status.""",
#         "draft guidelines",
#         """13 Mar 2024\n31 Mar 2020\n12 Dec 2016\n16 Nov 2009\n20 Oct 2005\nAdministration\nThe ACE1831 investigational drug product is provided cryopreserved in a formulation containing DMSO for IV administration.""",
#         "I eat from 16:30 to 17:30",
#         "I eat from 16:30 to 17:30 on 20 Oct 2005",
#         "toxic megacolon [Megacolon toxic (10027115)*], Seriousness: Caused /prolonged hospitalisation, Outcome: recovering/resolving",
#         "CRP [C-reactive protein (10006824)*], 2025-04-05, 126",
#         "CRP [C-reactive protein (10006824)*], 269",
#         "CRP [C-reactive protein (<__NUM001>)*], <__DATE001>, <__NUM002>",
#         "<__NUM002>",
#         "<__DATE001>",
#         "<__NUM001>",
#         ' <NT_0001> ',
#         '<NT_0001>',
#         " <NT_0001>指导原则草案</NT_0001> ",
#         " <NT>指导原则草案</NT> ",
#         '<NT>Dow futures jumped 1.03%, or 427.66 points.</NT>',
#         "<__TERM001>",
#         """Dow futures jumped 1.03%, or 427.66 points. S&P 500 futures rose 1.31%, or 75.8 points, while the tech-heavy Nasdaq Composite futures went up 1.71%, or 348.19 points, as of 7:45 p.m. ET.""",
#         """Protocol No.: TV48125-MH-40294 (Teva); 3130275 (IQVIA)""",
#         """Manufacturer: 12F, 12F-1 and 12F-3, No. 508, Sec. 7, Zhongxiao E. Rd., Nangang Dist., Taipei City, 115, Taiwan""",
#         """Dose Cohorts: -1 (n=10)b""",
#         """ACE1831 Dose Level (cells): RD""",
#         """Administration
# The ACE1831 investigational drug product is provided cryopreserved in a formulation containing DMSO for IV administration.""",
#         """Females of childbearing potential and males must agree to effective contraception for 24 weeks after lymphodepletion chemotherapy or last dose of ACE1831, largely because of the potential teratogenic effect of lymphodepletion therapy cyclophosphamide, while participating in the study and for an appropriate follow-up period, as described in the study protocols.""",
#         """Serum testosterone was measured on Day -14 (ie, the day of starting relugolix administration), Day 1 (ie, the day of starting co-administration with apalutamide), and Day 28.""",
#         """Concomitant Medication""",
#         """Subject Information""",
#         """Relevant Medical History""",
#         """Physical Examination""",
#         """Full Clinical Chemistry""",
#         """Protocol versions - Number""",
#         """一项CM355治疗复发或难治性B细胞非霍奇金淋巴瘤（B-NHL）的I/II期研究已获得国家药品监督管理局（NMPA）批准，批件号为：2021LP01500。""",
#         """Reagent
# Name: Cesium chloride
# Manufacture: Canto chemical
# Storage: 15 ~ 25°C""",
#         """Reagent\nName: Cesium chloride\nManufacture: Canto chemical\nStorage: 15 ~ 25°C""",
#         """Reagent\\nName: Cesium chloride\\nManufacture: Canto chemical\\nStorage: 15 ~ 25°C""",
#         """Reagent\n\n\nName: Cesium chloride\n\n\nManufacture: Canto chemical\n\nStorage: 15 ~ 25°C""",
#         """In case of case of chemotherapy treatment delays due to toxicities, additional window of +14 days is allowed.\nIQ test abnormal\nFINAL PRODUCT TEST\nFor the KB version:\nTo collaborate with you on medical events, publications, or advisory meetings.\n""",
#         """By default time span is 24 hours.\n\nScreened participants set: All participants who sign the informed consent.\n\n\nBody weights of qualified individuals spanned less than a 50-pound range for each gender.\n\nAge group 1 (years)\n\n\nTiters of ADA antibodies to efgartigimod and/or rHuPH20 in the overall population\n\n\n""",
#         """CRP [C反应蛋白 (10006824)*], 2025-04-05, 126""",
#         """CRP [C反应蛋白 (10006824)*], 269""",
#         """焦虑[焦虑 (10002855)*]""",
#         """HLD [高脂血症(10020667)*]""",
#         """<g id=\"1\" type=\"tag1\" text=\"tag property=&quot;x-bold;color:000000;&quot;\">流程概述：</g type=\"tag1\" text=\"/tag\">以下步骤概述了遵循作业指导书WI-9677所采取的流程。""",

#         """Anxiety [Anxiety (10002855)*]""",
#         """HLD [Hyperlipidemia(10020667)*]""",
#         """HTN [Hypertension (10020772)*], Onset: 2018-05-12, Status: Ongoing, Treatment: Lisinopril 10mg QD""",
#         """AE: Neutropenia [Neutropenia (10029354)*], Grade: 3, Causality: Related, Action taken: Dose reduced""",
#         """Headache [Headache (10019211)*], Severity: Moderate, Duration: 3 days, Concomitant medication: Acetaminophen 500mg PRN""",
#         """CTCAE: Diarrhea [Diarrhea (10012735)*], Grade 2, Onset: Cycle 2 Day 3, Resolution: Cycle 2 Day 7""",
#         """Adverse Event: Infusion reaction [Infusion related reaction (10051792)*], Seriousness: Medically significant, Outcome: Resolved with sequelae""",
#         """Laboratory abnormality: ALT increased [Alanine aminotransferase increased (10001551)*], Value: 128 U/L (Ref: 7-55), Grade: 2""",
#         """Medical History: T2DM [Type 2 diabetes mellitus (10067585)*], Diagnosed: 2015, Controlled: Yes, HbA1c: 6.8%""",
#         """Concomitant Medication: Metformin [Metformin (10027599)*], Dose: 1000mg BID, Indication: T2DM, Start date: 2015-06-10""",
#         """Serious Adverse Event: Pneumonia [Pneumonia (10035664)*], Hospitalization: Yes (5 days), Treatment: IV antibiotics, Outcome: Resolved""",
#         """ECG finding: QTc prolongation [Electrocardiogram QT prolonged (10014387)*], Value: 512 msec, Baseline: 450 msec, Clinical significance: Yes""",
#         """<g id="1" type="tag1" text="tag property=&quot;x-bold;color:000000;&quot;">Process Overview:</g type="tag1" text="/tag"> The following steps outline the process taken to comply with work instruction WI-9677.""",
#         """<g id=\"1\" type=\"tag1\" text=\"tag\">For the verification results, pls refer to the test results of SVP in CH3.5.5.8-2_ verification report <x id=\"2\" type=\"tag2\" text=\"tag property=&quot;x&quot;/\"/><g id=\"3\" type=\"tag3\" text=\"tag\">DOC2964067<x id=\"4\" type=\"tag4\" text=\"tag property=&quot;x&quot;/\"/></g type=\"tag3\" text=\"/tag\"></g type=\"tag1\" text=\"/tag\"><g id=\"5\" type=\"tag5\" text=\"tag\">, and the test ID are DVa.SVP.24998, DVa.SVP.3611, DVa.SVP.24404, DVa.SVP.23159, DVa.SVP.25490, DVa.SVP.26355, DVa.SVP.23760, DVa.SVP.25109, DVa.SVP.25299, DVa.SVP.25488, DVa.SVP.25581, DVa.SVP.25669, DVa.SVP.26008,</g type=\"tag5\" text=\"/tag\"> <g id=\"6\" type=\"tag6\" text=\"tag\">DVa.SVP.26011, DVa.SVP.24748, DVa.SVP.25132, DVa.SVP.23153, DVa.SVP.23154.</g type=\"tag6\" text=\"/tag\">""",
#         """Intent: The Risk Matrix Report documents the RoHS certifications, compliance testing results, and related risk assessments for <x id="2" type="tag2" text="tag property=&quot;x&quot;/"/><g id="3" type="tag3" text="tag property=&quot;x-bold;color:auto;&quot;">PUREVUE <x id="4" type="tag4" text="tag property=&quot;x&quot;/"/></g type="tag3" text="/tag">.""",
#         '''Intent: The Risk Matrix Report documents the RoHS certifications, compliance testing results, and related risk assessments for <x id="2" type="tag2" text="tag property=&quot;x&quot;/"/><g id="3" type="tag3" text="tag property=&quot;x-bold;color:auto;&quot;">PUREVUE <x id="4" type="tag4" text="tag property=&quot;x&quot;/"/></g type="tag3" text="/tag">''',
#         """WBC: 5.6×10^9/L (↓), RBC: 3.2×10^12/L (↓), HGB: 98 g/L (↓), PLT: 145×10^9/L, ALT: 45 U/L, AST: 38 U/L, BUN: 6.8 mmol/L, Cr: 78 μmol/L""",
#         """Patient presented with SOB [Shortness of breath (10041232)*], HR=120 bpm, BP=90/60 mmHg, SpO2=88% on room air. ECG showed ST-segment elevation in leads II, III, aVF.""",
#         """MRI findings: T2-weighted hyperintensity in the left temporal lobe with restricted diffusion (DWI+/ADC-) suggestive of acute ischemic stroke. No evidence of hemorrhagic transformation.""",
#         """<table><tr><td>Parameter</td><td>Baseline</td><td>Week 12</td><td>Week 24</td></tr><tr><td>HbA1c (%)</td><td>8.2</td><td>7.4</td><td>6.8</td></tr><tr><td>FPG (mmol/L)</td><td>9.8</td><td>7.6</td><td>6.5</td></tr></table>""",
#         """Treatment-emergent adverse events (TEAEs) were reported in 78.3% (n=47/60) of patients in Arm A vs. 82.5% (n=52/63) in Arm B. Grade ≥3 TEAEs occurred in 23.3% (n=14/60) vs. 31.7% (n=20/63), respectively.""",
#         """Medication: KEYTRUDA® (pembrolizumab) 200mg IV Q3W + LENVIMA® (lenvatinib) 20mg PO QD until disease progression or unacceptable toxicity (max 35 cycles for pembrolizumab).""",
#         """<span class="highlight">IMPORTANT SAFETY INFORMATION</span>: Do not administer DRUG-X to patients with known hypersensitivity to any component of the formulation.""",
#         """Patient ID: <PATIENT_ID_001> | Visit Date: <DATE_001> | Study Site: <SITE_ID_003> | Investigator: <INVESTIGATOR_NAME>""",
#         """The primary endpoint was met with statistical significance (HR=0.65 [95% CI: 0.52-0.81], p<0.0001), demonstrating a 35% reduction in the risk of disease progression or death.""",
#         """Vital Signs:\nT: 38.5°C\nP: 92/min\nR: 20/min\nBP: 142/88 mmHg\nSpO₂: 95% on RA""",
#         """Histopathology Report [Specimen #BX-2023-45678]:\nDiagnosis: Invasive ductal carcinoma, Grade 2 (Nottingham score 6/9)\nER: Positive (90%)\nPR: Positive (80%)\nHER2: Negative (IHC 1+)\nKi-67: 25%""",
#         """<protocol-deviation id="PD-2023-005" category="Inclusion/Exclusion" severity="major">Subject was enrolled despite not meeting inclusion criterion #4 (HbA1c ≥7.5% and ≤10.0%).</protocol-deviation>""",
#         """Pharmacokinetic Parameters:
# AUC₀₋ₜ: 4586.2 ± 1245.8 ng·h/mL
# Cₘₐₓ: 856.3 ± 201.7 ng/mL
# Tₘₐₓ: 2.5 h (range: 1.0-4.0)
# t₁/₂: 12.4 ± 3.2 h""",
#         """<clinical-observation date="2023-09-15T09:30:00" observer-id="INV-001">Patient exhibits parkinsonian gait with reduced arm swing, stooped posture, and festination. Cogwheel rigidity present in bilateral upper extremities. UPDRS Part III score: 28.</clinical-observation>""",
#         """Imaging Results [CT Chest/Abdomen/Pelvis with contrast]:
# - Multiple hypodense lesions in liver (largest 3.2 cm in segment VII)
# - Bilateral pulmonary nodules (largest 1.8 cm in RLL)
# - Lytic lesion in L3 vertebral body
# - No significant lymphadenopathy
# Impression: Findings consistent with metastatic disease.""",
#         """Study Drug Administration Log:
# Cycle 1, Day 1: 100% dose (150 mg/m²) - Completed
# Cycle 1, Day 8: 75% dose (112.5 mg/m²) - Dose reduction due to Grade 3 neutropenia
# Cycle 1, Day 15: Dose held - ANC <1.0×10⁹/L
# Cycle 2, Day 1: 75% dose (112.5 mg/m²) - Completed""",
#         """<lab-result test-id="CBC-001" collection-time="2023-10-12T08:15:00" analysis-time="2023-10-12T10:30:00" status="final">
# <parameter name="WBC" value="2.1" units="×10⁹/L" reference-range="4.0-11.0" flag="L"/>
# <parameter name="ANC" value="0.8" units="×10⁹/L" reference-range="1.8-7.7" flag="L"/>
# <parameter name="Hgb" value="9.2" units="g/dL" reference-range="12.0-16.0" flag="L"/>
# <parameter name="Plt" value="98" units="×10⁹/L" reference-range="150-450" flag="L"/>
# </lab-result>""",
#         '''<g id=\"1\">For the verification results, pls refer to the test results of SVP in CH3.5.5.8-2_ verification report <x id=\"2\"/><g id=\"3\">DOC2964067<x id=\"4\"/></g></g><g id=\"5\">, and the test ID are DVa.SVP.24998, DVa.SVP.3611, DVa.SVP.24404, DVa.SVP.23159, DVa.SVP.25490, DVa.SVP.26355, DVa.SVP.23760, DVa.SVP.25109, DVa.SVP.25299, DVa.SVP.25488, DVa.SVP.25581, DVa.SVP.25669, DVa.SVP.26008,</g> <g id=\"6\">DVa.SVP.26011, DVa.SVP.24748, DVa.SVP.25132, DVa.SVP.23153, DVa.SVP.23154.</g>''',
#         '''<g id="1">For the verification results, pls refer to the test results of SVP in CH3.5.5.8-2_ verification report <x id="2"/><g id="3">DOC2964067<x id="4"/></g></g><g id="5">, and the test ID are DVa.SVP.24998, DVa.SVP.3611, DVa.SVP.24404, DVa.SVP.23159, DVa.SVP.25490, DVa.SVP.26355, DVa.SVP.23760, DVa.SVP.25109, DVa.SVP.25299, DVa.SVP.25488, DVa.SVP.25581, DVa.SVP.25669, DVa.SVP.26008,</g> <g id="6">DVa.SVP.26011, DVa.SVP.24748, DVa.SVP.25132, DVa.SVP.23153, DVa.SVP.23154.</g>''',
#         '''<g id=\"1\">Process Overview:</g> The following steps outline the process taken to comply with work instruction WI-9677.''',
#         '''<g id="1">Process Overview:</g> The following steps outline the process taken to comply with work instruction WI-9677.''',
#         '''During the R&D period of MRE, several factors were identified that might impact MRE safety and <x id=\"2\"/>performance<x id=\"3\"/><x id=\"4\"/><x id=\"5\"/><x id=\"6\"/>.''',
#         '''During the R&D period of MRE, several factors were identified that might impact MRE safety and <x id="2"/>performance<x id="3"/><x id="4"/><x id="5"/><x id="6"/>.''',
#         '''Population (e.g., age, <g id=\"1\">size,</g> obesity):''',
#         '''Population (e.g., age, <g id="1">size,</g> obesity):''',
#         """Reagent<__LBREAK__>Name: Cesium chloride<__LBREAK__>Manufacture: Canto chemical<__LBREAK__>Storage: 15 ~ 25°C""",
#         '<__LBREAK__>',
#         '<__LBREAk>',
#         """Reagent<__LBREAK>Name: Cesium chloride<__LBREAK>Manufacture: Canto chemical<__LBREAK>Storage: 15 ~ 25°C""",
#         """Reagent<__LBK>Name: Cesium chloride<__LBK>t<__LBK>Manufacture: Canto chemical<__LBK>Storage: 15 ~ 25°C""",
#         "CK_MB/CKMB/CK [СК (10009218)*], 2025-04-11, 4 %",
#         "In total, 7 previously reported cases of Pneumonitis were identified in studies PM8002-A001, PM8002-B002C-SCLC-R, PM8002-B006C-HCC-R, PM8002-BC011C-SCLC-R and PM8002-BC010C-NSCLC-R. 1 case was identified for the study PM8002-A001: CN-BIOTHEUS-80022023CN07098 (severe, related to BNT327, recovered/resolved).",
#         "13 Mar 2024\n31 Mar 2020\\n12 Dec 2016\\\n16 Nov 2009\\\\\n20 Oct 2005\\\\\\\n\\nAdministration \\nThe ACE1831 investigational drug product is provided cryopreserved in a formulation containing DMSO for IV administration.",
#         "Hello, @user! Check out https://example.com and my <NAME_EMAIL>",
#         "Abstract: The study (doi:10.1234/abcd.5678) examined the effects of XYZ on patient outcomes.",
#         "Error log: `Exception in thread \"main\" java.lang.NullPointerException at com.example.Main.method(Main.java:25)`",
#         "References: Smith, J. (2023). Title of paper. Journal Name, 45(2), 201-215. https://doi.org/10.1234/journal.5678",
#         "Please submit an issue on GitHub: https://github.com/organization/project/issues/new and cc @maintainer",
#         "I eat at 15:35PM",
#         "In the <g id=\"2\">Phase I/II combination regimen</g> evaluation, blood samples were collected <bx id=\"7\"/> before and after the third dose<ex id=\"11\"/>, and quantified using the <mrk id=\"19\"/> version 2.5.1 analysis software.",
#         "All <g id=\"3\">12-lead ECG</g> reports are stored in <bx id=\"8\"/> XML<ex id=\"15\"/> format, with heart rate and QT interval automatically calculated by the <mrk id=\"22\"/> algorithm, and units verified via <bx id=\"27\"/> <ex id=\"29\"/>.",
#         "In the <g id=\"5\">HPLC-MS/MS assay</g>, the <bx id=\"14\"/>CYP2D6 inhibition rate<ex id=\"23\"/> (>50%) was measured with a detection limit of <bx id=\"17\"/>0.1 ng·mL⁻¹<ex id=\"18\"/>, and results uploaded to the <mrk id=\"30\"/> v1.0 database.",
#         "In the <g id=\"9\">CRF v3.2.0</g>, fields #<bx id=\"4\"/> SBP<ex id=\"13\"/> and <bx id=\"6\"/> DBP<ex id=\"16\"/> require recording systolic and diastolic blood pressure (unit: mmHg), with data validated by the <mrk id=\"28\"/> data extraction script.",
#         "The study employed <g id=\"12\">next-generation sequencing (NGS)</g> on the <bx id=\"20\"/> Illumina NovaSeq 6000<ex id=\"26\"/> platform to generate FASTQ files, with genome reads<mrk id=\"24\"/> (length: 150 bp×2) subsequently aligned.",
#         "Using the <g id=\"1\">Luminex MAGPIX® platform</g>, serum biomarkers (IL-1β, TNF-α) were quantified after treatment with a <bx id=\"2\"/>5 mg/kg<ex id=\"3\"/> dose combination, and data were imported into the <mrk id=\"4\"/> v3.4 analysis pipeline, including <bx id=\"5\"/>QC thresholds<ex id=\"6\"/> and batch correction via the <g id=\"7\">ComBat algorithm</g>.",
#         "All <g id=\"8\">DICOM v2021.07</g> images were anonymized using <bx id=\"9\"/>ExifTool<ex id=\"10\"/> and stored on the <mrk id=\"11\"/> PACS server (<bx id=\"12\"/>10.0.1<ex id=\"13\"/>), with access logs periodically reviewed by the <g id=\"14\">Data Security Officer</g>.",
#         "The <g id=\"15\">randomization schedule</g> was generated using <bx id=\"16\"/> SAS v9.4<ex id=\"17\"/> with seed 12345, stratified by <bx id=\"18\"/> center<ex id=\"19\"/> and <bx id=\"20\"/> region<ex id=\"21\"/>, and loaded into the <mrk id=\"22\"/> EDC v5.3 system.",
#         "Sample libraries were prepared using the <g id=\"23\">NEBNext Ultra II</g> kit, with barcodes <bx id=\"24\"/> A01–H12<ex id=\"25\"/> assigned, sequenced on the <g id=\"26\">Illumina MiSeq</g> with 2×300 bp reads; library prep ID<bx id=\"27\"/> recorded<ex id=\"28\"/> and uploaded to the <mrk id=\"29\"/> LIMS v4.2.",
#         '<g id="1">Use case two: Streamlining production maintenance with smart deviation management The end-to-end process for managing both deviation and corrective and preventive actions (CAPAs) requires 4 to 6 percent of a manufacturing site’s resources and is fraught with challenges.</g><g id="2">4  </g>Common pain points include delayed detection, manual tasks, a low rate of right- first-time solutions, low effectiveness, inconsistent documentation, and a reactive process.',
#         '<__RL001>Use case two: Streamlining production maintenance with smart deviation management The end-to-end process for managing both deviation and corrective and preventive actions (CAPAs) requires 4 to 6 percent of a manufacturing site’s resources and is fraught with challenges.<__RL002><__RL003>4  <__RL004>Common pain points include delayed detection, manual tasks, a low rate of right- first-time solutions, low effectiveness, inconsistent documentation, and a reactive process.',
#         'Use case two: Streamlining production maintenance with smart deviation management The end-to-end process for managing both deviation and corrective and preventive actions (CAPAs) requires 4 to 6 percent of a manufacturing site’s resources and is fraught with challenges.4 Common pain points include delayed detection, manual tasks, a low rate of right- first-time solutions, low effectiveness, inconsistent documentation, and a reactive process.',

#         "Protocol No.: TV48125-MH-40294 (Teva); 3130275 (IQVIA)",
#         "A small number of patients who had normal or abnormal NCS clinical assessments at baseline subsequently showed changes to abnormal CS in post-baseline evaluations.",
#         "The inter-paticipant variability of both CVM-1118 and CVM-1125 concentrations in plasma was high.",

        "Novotech Laboratories",
        'Everest IWRS',
        "1. In acute-phase patients, CT scans were used to assess pulmonary lesions.",
        "1) Brain structural changes were observed by MRI during disease evaluation.",
        "1) The AUC₀–∞/dose ratio was used to evaluate drug clearance.",
        "1- Dynamic ECG monitoring can continuously record 24-hour heart rate variability.",
        "–1 Dose adjustment depended on liver function test results.",
        "(1) The Phase I clinical trial used the NCT01234567-registered protocol for PK analysis.",
        "(1) In the Phase I clinical trial, patients received treatment with the new monoclonal antibody.",
        "1.2) The control group (n=30) underwent complete blood count on day 2.",
        "1)2. Combined therapy may affect plasma protein binding rate.",
        "1-2) The dose gradient was set from 0.5 mg/kg to 2.0 mg/kg.",
        "–1–) This describes a sequence test with special numbering and symbols.",
        "(1) (2) Tested the impact of the term IC₅₀ on experimental outcomes.",
        "(1)(2) Formula: V_d = CL/F × τ, used to calculate distribution volume.",
        "1)(1) Medical device model MD-220A was primarily used for cardiac pacing.",
        "1. Cardioverter defibrillator model EC-200; check numbers 1, 2, 3.",
        "1) Intravenous (IV) infusion administration resulted in an average Tₘₐₓ of 2 h.",
        "(1) The patient reported a headache score of VAS 4.",
        "(1) The patient received a chemotherapy regimen with a dose of 10 mg/m².",
        "1- Bronchoscopy revealed grade II airway stenosis.",
        "–1 (1) This is a test of mixed parentheses and symbols numbering."
        
    ]
    from util.post_processor import post_process
    from util.pre_processor import pre_process
    # test_src_texts_preprocessed = pre_process(test_src_texts)
    
    if src_lang == "zh":
        test_src_texts = zh_test_src_texts
    else:
        test_src_texts = en_test_src_texts
    test_batch_size = 40
    translations = translator.translate(test_src_texts, batch_size=test_batch_size)
    # translations_postprocessed = post_process(translations)
    # for src, src_preprocessed, tgt, tgt_postprocessed in zip(test_src_texts, test_src_texts_preprocessed, translations,  translations_postprocessed):
    #     print(f"src: {src}")
    #     print(f'src_preprocessed: {src_preprocessed}')
    #     print(f'tgt: {tgt}')
    #     print(f'tgt_postprocessed: {tgt_postprocessed}')
    #     print('-' * 100)
    # for src, src_preprocessed, tgt in zip(test_src_texts, test_src_texts_preprocessed, translations):
    #     print(f"src: {src}")
    #     print(f'src_preprocessed: {src_preprocessed}')
    #     print(f'tgt: {tgt}')
    #     print('-' * 100)
    for src, tgt in zip(test_src_texts, translations):
        print(f"src: {src}")
        print(f'tgt: {tgt}')
        print('-' * 100)
    ######### 测试 ##########

# Add basic logging setup if run directly (optional)
if __name__ == '__main__':
    import os
    import evaluate
    import json
    from tqdm import tqdm
    checkpoint_step_list = [13000]
    for checkpoint_step in checkpoint_step_list:
        # model_path="/home/<USER>/data/models/trained/zh2en/m2m-1.2b-45/checkpoint-230-ct2"
        # token_path="/home/<USER>/data/models/trained/zh2en/m2m-1.2b-45/checkpoint-230"
        # model_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-40/checkpoint-64000-ct2"
        # token_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-40/checkpoint-64000"
        # model_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-41/checkpoint-1200-ct2"
        # token_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-41/checkpoint-1200"
        # model_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-42/checkpoint-5500-ct2"
        # token_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-42/checkpoint-5500"
        # model_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-45/checkpoint-1000-ct2"
        # token_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-45/checkpoint-1000"
        # model_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-47/checkpoint-12955-ct2"
        # token_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-47/checkpoint-12955"
        # model_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-50/checkpoint-13000-ct2"
        # token_path = "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-50/checkpoint-13000"

        # model_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-50/checkpoint-49000-ct2"
        # token_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-50/checkpoint-49000"
        # model_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-50/checkpoint-64000-ct2"
        # token_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-50/checkpoint-64000"
        # model_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-47/checkpoint-5000-ct2"
        # token_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-47/checkpoint-5000"
        # model_path="/home/<USER>/data/models/trained/zh2en/m2m-1.2b-45/checkpoint-{checkpoint_step}-ct2"
        # token_path="/home/<USER>/data/models/trained/zh2en/m2m-1.2b-45/checkpoint-{checkpoint_step}"
        # model_path="/home/<USER>/data/models/trained/zh2en/m2m-1.2b-51/checkpoint-9000-ct2"
        # token_path="/home/<USER>/data/models/trained/zh2en/m2m-1.2b-51/checkpoint-9000"
        model_path=f"/home/<USER>/data/models/trained/zh2en/m2m-1.2b-53/checkpoint-{checkpoint_step}-ct2"
        token_path=f"/home/<USER>/data/models/trained/zh2en/m2m-1.2b-53/checkpoint-{checkpoint_step}"
        if 'en2zh' in model_path:
            src_lang = "en"
            tgt_lang = "zh"
        else:
            src_lang = "zh"
            tgt_lang = "en"
        device = "cuda"
        compute_type = "int8"
        device_index = 7
        batch_size = 50
        src_file = "/home/<USER>/data/cache/yxcdata.en-zh.devtst"
        cache_file = "/home/<USER>/data/src-formatted_tgt-tgt.json"
        # test_bleu(model_path, token_path, src_lang, tgt_lang, device, compute_type, device_index, batch_size, src_file, cache_file)
        test(model_path, token_path, src_lang, tgt_lang, device, device_index, compute_type)

"""
python -m translator.ct2_translator
"""
