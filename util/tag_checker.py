# util/tag_checker.py

import re
from collections import defaultdict, Counter
from logger.logger import biz_logger

# —— 只剩下 <g> 是成对标签 —— 
PAIRED_TAGS = ['g']

# —— 自闭合标签：bx, ex, x, mrk —— 
SELF_CLOSING_TAGS = ['bx', 'ex', 'x', 'mrk']

# --- 仅与 <g> 成对标签相关的正则 ----
G_OPEN  = re.compile(r'<g\b[^>/]*?>',    flags=re.IGNORECASE)   # 开标签
G_CLOSE = re.compile(r'</g\b[^>]*?>',    flags=re.IGNORECASE)   # 闭标签

# 匹配成对标签 <g …> 和 </g …>
OPEN_TAG_RE   = re.compile(r'<(?P<tag>{tags})\b[^>/]*?>'.format(tags='|'.join(PAIRED_TAGS)), re.IGNORECASE)
CLOSE_TAG_RE  = re.compile(r'</(?P<tag>{tags})\b[^>]*?>'.format(tags='|'.join(PAIRED_TAGS)), re.IGNORECASE)

# 匹配自闭合标签 <bx …/>, <ex …/>, <x …/>, <mrk …/>
SELF_TAG_RE   = re.compile(r'<(?P<tag>{tags})\b[^>]*?/>'.format(tags='|'.join(SELF_CLOSING_TAGS)), re.IGNORECASE)

# # 提取 id="..."
# ID_RE = re.compile(
#     r'\b(?P<tag>{all})\b[^>]*?\bid="(?P<id>[^"]+)"'.format(
#         all='|'.join(PAIRED_TAGS + SELF_CLOSING_TAGS)
#     ),
#     re.IGNORECASE
# )
ID_RE = re.compile(
    r'<(?P<tag>{all})\b[^>]*?\bid="(?P<id>[^"]+)"[^>]*?>'.format(
        all='|'.join(PAIRED_TAGS + SELF_CLOSING_TAGS)
    ),
    re.IGNORECASE
)

def detect_self(text: str) -> dict:
    """
    检测单条文本中的标签配对和自闭合使用情况。
    返回字典，包含 paired_counts, self_counts, duplicate_ids。
    """
    info = {
        'paired_counts': {},
        'self_counts': {},
        'duplicate_ids': {}
    }

    # 1) 成对标签统计
    opens  = [m.group('tag').lower() for m in OPEN_TAG_RE.finditer(text)]
    closes = [m.group('tag').lower() for m in CLOSE_TAG_RE.finditer(text)]
    for tag in PAIRED_TAGS:
        info['paired_counts'][tag] = (opens.count(tag), closes.count(tag))

    # 2) 自闭合标签统计
    selfs = [m.group('tag').lower() for m in SELF_TAG_RE.finditer(text)]
    for tag in SELF_CLOSING_TAGS:
        info['self_counts'][tag] = selfs.count(tag)

    # 3) 重复 ID 检测
    id_list = [(m.group('tag').lower(), m.group('id')) for m in ID_RE.finditer(text)]
    id_map = defaultdict(list)
    for tag, _id in id_list:
        id_map[_id].append(tag)
    info['duplicate_ids'] = { _id: tags for _id, tags in id_map.items() if len(tags) > 1 }

    return info

def sanitize_translation(text: str) -> str:
    """
    **新版逻辑** —— 逐个扫描 <g> 成对标签，仅删除非法的：
        1. 遇到开标签 => 入栈（记录 (start,end)）
        2. 遇到闭标签：
            • 若栈非空 -> 弹栈配对，二者均保留  
              （保证正常嵌套：open1 open2 … close2 close1）
            • 若栈为空 -> 说明是“孤儿”闭标签，删除
            • 若出现交叉（open1 open2 close1 close2）：
              ‑ 判断栈顶不是当前应配对的开标签 ⇒ 认定此闭标签非法并删除
        3. 扫描结束后，栈中剩余的都是“孤儿”开标签 → 全部删除
    这样，不论开闭数量是否相等，都能 **最大限度保留合法配对**、只清理非法。
    """
    biz_logger.info("开始精确清理 g 标签（按栈匹配原则）")

    # 收集所有 <g> 开/闭标签位置信息
    tag_positions = []
    for m in G_OPEN.finditer(text):
        tag_positions.append((m.start(), m.end(), 'open'))
    for m in G_CLOSE.finditer(text):
        tag_positions.append((m.start(), m.end(), 'close'))
    tag_positions.sort(key=lambda x: x[0])        # 按出现顺序排序

    stack = []            # 存放 (start, end) 的开标签
    remove_spans = []     # 记录需要删除的 (start, end)

    for start, end, typ in tag_positions:
        if typ == 'open':
            stack.append((start, end))
        else:  # 关闭标签
            if not stack:
                # ① 栈空 ⇒ 孤儿闭标签
                remove_spans.append((start, end))
            else:
                # 栈顶开标签应当与当前闭标签匹配
                #   * 如果闭标签“跨越”了另一层未配对的开标签 ⇒ 非法（交叉）
                #   * 判定条件：闭标签位置早于栈顶开标签之后又有未闭合的开标签
                top_start, top_end = stack.pop()
                if any(st < start < en for st, en in stack):
                    # 交叉结构，当前闭标签非法
                    remove_spans.append((start, end))
                    # 刚才弹出的开标签重新压回去，等待正确闭标签
                    stack.append((top_start, top_end))

    # 扫描结束后，剩下的都是“孤儿”开标签
    if stack:
        remove_spans.extend(stack)

    # 实际删除：从后往前切片，避免索引错位
    out = text
    for s, e in sorted(remove_spans, reverse=True):
        out = out[:s] + out[e:]

    biz_logger.info(f"非法标签已删除 {len(remove_spans)} 个")
    return out


def sanitize_tags(text: str) -> str:
    """
    严格删除：
      1) 成对标签不平衡时，删除所有该标签；
      2) 自闭合标签重复 ID 时，只保留第一次，多余的全部删除。
    """
    biz_logger.info(f"开始清理标签，原文: {text}")
    info = detect_self(text)

    # 1) 删除不平衡的成对标签
    for tag, (opens, closes) in info['paired_counts'].items():
        if opens != closes:
            biz_logger.warning(f"标签<{tag}>不平衡，删除所有：开标签数={opens}, 闭标签数={closes}")
            text = re.sub(rf'</?{tag}\b[^>]*?>', '', text, flags=re.IGNORECASE)

    # 2) 删除多余的自闭合标签（同一 ID 只保留第一次）
    # 首先收集所有 self 标签及其 id
    all_self = [
        (m.group('tag').lower(), m.group('id'))
        for m in ID_RE.finditer(text)
        if m.group('tag').lower() in SELF_CLOSING_TAGS
    ]
    id_counts = Counter([_id for _, _id in all_self])
    
    # 记录重复ID
    duplicates = {_id: count for _id, count in id_counts.items() if count > 1}
    if duplicates:
        biz_logger.warning(f"发现重复ID的自闭合标签：{duplicates}，将只保留每个ID的第一个标签")

    # 遍历时只删除那些 id_counts > 1 的多余标签
    for tag, _id in all_self:
        if id_counts[_id] > 1:
            pattern = rf'<{tag}\b[^>]*?\bid="{_id}"[^>]*?/>'
            text = re.sub(pattern, '', text, count=1, flags=re.IGNORECASE)
            id_counts[_id] -= 1

    biz_logger.info(f"清理后文本: {text}")
    return text

def sanitize_translation_strict(orig: str, trans: str) -> str:
    """
    严格删除译文中所有不在原文中的 <g>…</g>：
      1) 先删掉所有 <g> 或 </g>，其 id 不在 orig_id_set 的；
      2) 再删掉剩下所有无 id 的成对 <g>…</g>；
      3) 最后对自闭合标签，重复 id 只保留第一条（复用 sanitize_tags 的逻辑）。
    """
    biz_logger.info(f"开始严格清理译文标签，原文: {orig}")
    biz_logger.info(f"待清理译文: {trans}")
    
    # 1) 提取原文中所有 <g> 的 id
    orig_ids = set(m for _, m in ID_RE.findall(orig) if _ == 'g')
    text = trans

    # 2) 删除译文中 <g id="X"> … 和 </g>，若 X 不在 orig_ids
    #    先删除开标签，再删除所有泛闭标签（因闭标签无 id）
    extra_ids = set(m for tag, m in ID_RE.findall(text) if tag == 'g') - orig_ids
    if extra_ids:
        biz_logger.warning(f"译文中发现{len(extra_ids)}个原文没有的g标签ID：{extra_ids}，将被删除")
        for id in extra_ids:
            # 开标签
            text = re.sub(rf'<g\b[^>]*\bid="{re.escape(id)}"[^>]*?>', '', text, flags=re.IGNORECASE)
            # 闭标签
            text = re.sub(rf'</g\b[^>]*?>', '', text, flags=re.IGNORECASE)

    # 3) 删除所有剩余无 id 的 <g>…</g>
    no_id_pattern = r'<g\b(?:(?!\bid=)[^>])*?>'
    if re.search(no_id_pattern, text, re.IGNORECASE):
        biz_logger.warning("译文中发现无ID的g标签，将被删除")
        text = re.sub(no_id_pattern, '', text, flags=re.IGNORECASE)
        text = re.sub(r'</g\b[^>]*?>', '', text, flags=re.IGNORECASE)

    # 4) 对自闭合标签重复 id 只保留第一次
    text = sanitize_tags(text)
    
    biz_logger.info(f"清理后译文: {text}")
    return text


def detect_cross(orig: str, trans: str) -> list:
    """
    双向检测原文⇔译文中所有不对照的成对标签和自闭合标签：
      - 成对标签 open/close 数量不匹配；
      - 翻译缺少原文中的标签 ID；
      - 翻译新增原文没有的标签 ID；
      - 自闭合标签数量不一致；
    返回所有差异描述的列表。
    """
    biz_logger.info(f"开始检测标签差异，原文: {orig}")
    biz_logger.info(f"译文: {trans}")
    
    o = detect_self(orig)
    t = detect_self(trans)
    diffs = []

    # 1) open/close 数量不匹配
    for tag in PAIRED_TAGS:
        o_o, o_c = o['paired_counts'][tag]
        t_o, t_c = t['paired_counts'][tag]
        if o_o != t_o or o_c != t_c:
            diffs.append(f"<{tag}> 标签不平衡：原文 开标签={o_o},闭标签={o_c}；译文 开标签={t_o},闭标签={t_c}")

    # 2) 自闭合标签数量不一致
    for tag in SELF_CLOSING_TAGS:
        o_n, t_n = o['self_counts'][tag], t['self_counts'][tag]
        if o_n != t_n:
            diffs.append(f"<{tag}/> 数量不匹配：原文={o_n}；译文={t_n}")

    # # 3) ID 差异
    # o_ids = set(m for tag, m in ID_RE.findall(orig))
    # t_ids = set(m for tag, m in ID_RE.findall(trans))
    # missing = o_ids - t_ids
    # extra   = t_ids - o_ids

    # 取出所有 id
    o_ids = {m for _, m in ID_RE.findall(orig)}
    t_ids = {m for _, m in ID_RE.findall(trans)}

    # 只保留“干净”的 id（根据你实际情况改正则，比如只允许数字或字母下划线）
    clean = re.compile(r'^[A-Za-z0-9_]+$')
    t_ids = {i for i in t_ids if clean.match(i)}
    
    missing = o_ids - t_ids
    extra   = t_ids - o_ids

    if missing:
        diffs.append(f"译文缺失{len(missing)}个原文标签ID：{missing}")
    if extra:
        diffs.append(f"译文多出{len(extra)}个原文没有的标签ID：{extra}")

    if diffs:
        biz_logger.warning(f"检测到{len(diffs)}处标签差异：{diffs}")
    else:
        biz_logger.info("未检测到标签差异")
    
    return diffs



if __name__ == '__main__':
    # 测试用例集合
    tests = [
        # 1. 日志行中嵌入 JSON + 正确/错误混合的 <g>
        '2025-05-20T00:37:33.216Z INFO mt {"translate_request":{"src_text":"<g id=\\"A1\\">test</g>","tgt_lang":"en"}} <g id="A1">oops</g></g>',

        # 2. JSON 字段值内含自闭合标签与未闭合 <g>
        'ERROR {"data":"value<bx id=\\"B1\\"/><ex id=\\"E1\\"/> and <g id=\\"G1\\">unclosed"} at module',

        # 3. 多层嵌套，属性顺序混乱
        '<g type="tag" id="X1" x-sup="true">Level1 <g x-sub="true" data-foo="bar" id="X2">Level2</g> End</g>',

        # 4. 大小写混用、无 id 的自闭合
        '<G id="C1">Text<MRK/>Keep<bx/> then </g>',

        # 5. 重复 ID 跨标签类型
        'Start <bx id="dup"/>1<mrk id="dup"/>2<bx id="dup"/>3 and <g id="dup">dup g</g>',

        # 6. URL 拆分 + HTML 实体 + 上下标标签
        '[LINK] <g id="L1" type="link">https://ex.com/path?<x id="X3"/>q=1&amp;r=2</g> Chapter 3<g x-sup="true">rd</g> edition',

        # 7. 复杂日志前缀 + 嵌套非法
        '2025-05-20 14:00:00,123 WARN [biz] <g id="G2" x-sub="true">A<g id="G3">B</g> still open</g>',

        # 8. 普通标签与 <g> 串行、自闭合紧邻
        'Info: <span>keep</span><g id="G4">good</g><bx id="B2"/><ex id="E2"/>done',

        # 9. 成对标签不平衡 + HTML 实体：
        'Data &lt;value&gt; <g id="GBad">open only</g><g>no close1</g> extra close </g>',

        # 10. 只有闭合或只有开放
        'Orphan close </g> and orphan open <g id="GO">',

        # 11. 跨行日志示例（换行符保留）
        '''2025-05-20 14:05:00 INFO Start
        <g id="G5">multi
        line<bx id="B3"/>tag</g>
        End''',

        # 12. 嵌套不同类型标签后闭合错序
        '<g id="G6"><bx id="B4"/>Hello<ex id="E3"/></g></bx>',

        # 13. 重复属性、不规范属性顺序
        '<g text="foo" id="G7" x-sup="true" data-extra="1">X</g>',

        # 14. 无 ID 的自闭合与正常 g 标签组合
        'Mix <mrk/>no-id<g id="G8">keep</g><mrk/>tail',

        # 15. 带路径的日志加中英文混合标签
        '路径：C:\\\\logs\\\\app.log <g id="G9">日志处理</g> OK',

        # 16. 多级嵌套，内层不平衡
        '<g id="G10">Level1<g id="G11">Level2<g id="G12">Level3</g>Level2 end</g>',

        # 17. JSON + XML 混用
        '<req>{"param":"<g id=\\"J1\\">v</g>"}<xml><g>inner</g></xml>',

        # 18. 自闭合标签错写成开闭组合
        '<bx id="B5">wrong</bx> keep',

        # 19. 含有 HTML 实体的上下标
        'Percent 100<g x-sup="true">%</g> &amp; sub<g x-sub="true">2</g>',

        # 20. 多个未闭合 <g> 按序出现
        '<g id="G13">A<g id="G14">B<g id="G15">C',
    ]

    # 1) detect_self 批量测试
    print("=== detect_self ===")
    for idx, txt in enumerate(tests, 1):
        info = detect_self(txt)
        print(f"[{idx}] 原文: {txt!r}")
        print(f"    paired_counts: {info['paired_counts']}")
        print(f"    self_counts:   {info['self_counts']}")
        print(f"    duplicate_ids: {info['duplicate_ids']}\n")

    # 2) sanitize_translation 单例测试
    st = 'A<g>1</g><g>2</g></g> and <bx id="b1"/>OK'
    print("=== sanitize_translation ===")
    print(" 原文: ", st)
    print(" 结果: ", sanitize_translation(st), "\n")

    # 3) sanitize_tags 单例测试
    stg = 'X<bx id="dup"/>…<bx id="dup"/>重复；<g>bad</g></g>'
    print("=== sanitize_tags ===")
    print(" 原文: ", stg)
    print(" 结果: ", sanitize_tags(stg), "\n")

    # 4) detect_cross 单例测试
    orig = 'E=mc<g x-sup="true">2</g>'
    trans = 'E=mc²'
    print("=== detect_cross ===")
    print(" 原文: ", orig)
    print(" 译文: ", trans)
    print(" 差异: ", detect_cross(orig, trans))

    # 5) 对所有测试用例批量做 sanitize_translation（删除所有不平衡的 <g> 标签）
    print("=== sanitize_translation 批量测试 (删除不平衡的 <g> 标签) ===")
    for idx, txt in enumerate(tests, 1):
        cleaned = sanitize_translation(txt)
        print(f"[{idx}] 原文:    {txt!r}")
        print(f"    清理后: {cleaned!r}\n")