[circus]
check_delay=5
endpoint=tcp://127.0.0.1:5555
pubsub_endpoint=tcp://127.0.0.1:5556
statsd=true
pidfile=/root/logs/circus.pid


[watcher:{{app_name}}]
working_dir=/root/{{app_name}}/target/{{app_name}}
cmd=gunicorn app:app --workers 1 --worker-class uvicorn.workers.UvicornH11Worker --worker-connections 1000 --bind 0.0.0.0:7860
stop_signal=QUIT
stdout_stream.class=FileStream
stdout_stream.filename=/root/{{app_name}}/logs/application.log
stdout_stream.max_bytes=20971520
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=/root/{{app_name}}/logs/error.log
stderr_stream.max_bytes=20971520
stderr_stream.backup_count=30


[env]
PATH=$PATH
APP_LOG_DIR=$APP_LOG_DIR
APP_STAGE=$APP_STAGE
MODEL_PATH=$MODEL_PATH
TOKEN_PATH=$TOKEN_PATH
SOURCE_LANG=$SOURCE_LANG
TARGET_LANG=$TARGET_LANG