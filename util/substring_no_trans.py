# util/substring_no_trans.py
import re
from typing import <PERSON><PERSON>, Dict, Pattern
from wordfreq import zipf_frequency

from logger.logger import biz_logger
from configurer.config_reader import get_non_translation_exact_strings
from util.regex_components_zh2en import PATTERNS_ZH2EN



NT_PREFIX = "NT"

PUNCT_AROUND = re.compile(r'^[，。？！,.?!…]+$')


def _compile_patterns(src_lang: str, tgt_lang: str) -> Pattern:
    # 1) 先选好 patterns（可以是字符串，也可以是已编译的 Pattern）
    patterns = PATTERNS_ZH2EN

    parts = []
    for name, pat in patterns.items():
        if isinstance(pat, re.Pattern):
            # 已编译的取它的源字符串
            pattern_str = pat.pattern
        else:
            pattern_str = pat

        # # 把真正的子模式插进去
        parts.append(f"(?P<{name}>{pattern_str})")

    combined = "|".join(parts)
    # 2) 一定要带 VERBOSE，这样子模式里写的注释、空格才能被正确忽略
    return re.compile(combined, flags=re.IGNORECASE | re.VERBOSE)


def is_common_english_word(token: str, threshold: float = 2.5) -> bool:
    """
    只对纯大写字母串生效，返回其 zipf 词频是否 >= threshold。
    """
    if not re.fullmatch(r"[A-Z]+", token):
        biz_logger.debug(f"词汇 '{token}' 不是纯大写字母串，跳过常见词检查")
        return False
    freq = zipf_frequency(token.lower(), "en")
    is_common = freq >= threshold
    biz_logger.debug(f"词汇 '{token}' 的 zipf 频率: {freq:.2f}, 阈值: {threshold}, 常见词: {is_common}")
    return is_common


def mask_substring_no_trans(
    text: str,
    src_lang: str = "en",
    tgt_lang: str = "zh"
) -> Tuple[str, Dict[str, str]]:
    """
    用正则屏蔽所有不需要翻译的子串，返回屏蔽后的文本和映射表。
    """
    biz_logger.info(f"开始屏蔽非译子串，语言方向: {src_lang} -> {tgt_lang}")
    biz_logger.info(f"输入文本: {text}")
    
    regex = _compile_patterns(src_lang, tgt_lang)
    mapping: Dict[str, str] = {}
    counter = 0

    def _repl(m: re.Match) -> str:
        nonlocal counter
        orig = m.group(0)
        if not orig:
            return orig
        
        grp = m.lastgroup
        biz_logger.info(f"匹配到非译元素，类型: {grp}, 内容: {orig}")
        

        counter += 1
        ph = f"<{'_'*2}{NT_PREFIX}{counter:03d}>"
        mapping[ph] = orig
        biz_logger.info(f"屏蔽非译元素: '{orig}' → '{ph}'")
        return ph

    masked = regex.sub(_repl, text)
    biz_logger.info(f"非译子串屏蔽完成，共屏蔽 {len(mapping)} 项非译内容")
    biz_logger.info(f"屏蔽后文本: {masked}")
    return masked, mapping



def restore_substring_no_trans(
    text: str,
    mapping: Dict[str, str],
    src_lang: str = "en",
    tgt_lang: str = "zh"
) -> str:
    """
    将所有 <__NT\d{3}> 等占位符还原回原文子串。
    en->zh 场景下，会在还原前后给英文/数字/标点两侧补空格。
    """
    if not mapping:
        biz_logger.info("无非译映射表，直接返回原文本")
        return text

    biz_logger.info(f"开始还原非译子串，语言方向: {src_lang} -> {tgt_lang}，共 {len(mapping)} 项")
    biz_logger.debug(f"还原前文本: {text}")
    

    # 构建数字编号到原串的映射
    num_map = { re.search(r'(\d{3})', ph).group(1): orig
                for ph, orig in mapping.items() }
    biz_logger.info(f"构建数字编号映射表完成，共 {len(num_map)} 项")

    def _repl(m: re.Match) -> str:
        idx = m.group(1)
        original = num_map.get(idx, m.group(0))
        if idx in num_map:
            biz_logger.debug(f"还原占位符: {m.group(0)} → '{original}'")
        return original

    restored = re.sub(r'<_*' + NT_PREFIX + r'_?(\d{3})>', _repl, text, flags=re.IGNORECASE)
    biz_logger.info("非译子串还原完成")
    biz_logger.debug(f"还原后文本: {restored}")
    return restored
