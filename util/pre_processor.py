import re 
from util.jiaobiao import preprocess_for_translation
from logger.logger import biz_logger

# 删除文本中的多余空白符
def delete_extra_spaces(text):
    # biz_logger.debug(f"开始删除文本中的多余空白符，原文: {text}")
    # 只替换连续的空格、制表符，不替换换行
    result = re.sub(r'[ \t]+', ' ', text).strip()
    # biz_logger.debug(f"完成多余空白符删除，结果: {result}")
    return result

# 换行符转义
def escape_newlines(text):
    # biz_logger.debug(f"开始处理换行符转义，原文: {text}")
    if '\n' in text:
        # 先将\r\n格式化为\n
        text = text.replace('\r\n', '\n')
        # 将\n转义为\\n
        text = text.replace('\n', '\\n')

    # biz_logger.debug(f"完成换行符转义处理，结果: {text}")
    return text


def pre_process(src_list, src_lang, tgt_lang):
    """
    1) 先做角标/下标替换  
    2) 再做换行转义和多余空格清理  
    """
    biz_logger.info(f"开始预处理文本，翻译方向: {src_lang}→{tgt_lang}")
    # # —— 第一步：角标替换 —— 
    # src_list = preprocess_for_translation(src_list)

    # —— 第二步：其余清理 —— 
    biz_logger.info("开始文本清理处理")
    processed_list = []
    for src in src_list:
        src = escape_newlines(src)
        # src = delete_extra_spaces(src)
        processed_list.append(src)

    biz_logger.info(f"预处理完成，共处理{len(processed_list)}条文本")
    return processed_list
