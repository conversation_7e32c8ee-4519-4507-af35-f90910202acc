apiVersion: v1
kind: Service
metadata:
  name: medical-zh2en-dev
  namespace: default
  labels:
    app: medical-zh2en-dev
    tier: model
spec:
  ports:
  - port: 8000
    targetPort: 7860
    nodePort: 32181
  selector:
    app: medical-zh2en-dev
    tier: model
  type: NodePort

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: medical-zh2en-dev
  namespace: default
  labels:
    app: medical-zh2en-dev
spec:
  selector:
    matchLabels:
      app: medical-zh2en-dev
  replicas: 1
  template:
    metadata:
      labels:
        app: medical-zh2en-dev
        tier: model
    spec:
      imagePullSecrets:
      - name: yiya-acr-key 

      containers:
        - name: medical-zh2en-dev
          image: "yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/yiya/mt-server:202408111659"
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 8
              memory: 16000
              nvidia.com/gpu: 1
            request:
              cpu: 6
              memory: 8000
              nvidia.com/gpu: 1
          ports:
            - containerPort: 7860
          livenessProbe:
            httpGet:
              path: /health_check
              port: 7860
              scheme: HTTP
            initialDelaySeconds: 600
            periodSeconds: 30
            timeoutSeconds: 60
            failureThreshold: 3
            successThreshold: 1
          readinessProbe:
            tcpSocket:
              port: 7860
            initialDelaySeconds: 30
            failureThreshold: 3
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
          envFrom:
            - configMapRef:
                name: yiya-cm
          env:
            - name: MODEL_LANG
              value: zh2en
            - name: MODEL_PATH
              value: /data/m2m100_1.2b_zh2en_quant
            - name: TOKEN_PATH
              value: /data/m2m100_1.2b_zh2en_origin
            - name: SOURCE_LANG
              value: zh
            - name: TARGET_LANG
              value: en
          volumeMounts:
            - mountPath: /data
              name: volume-1718246899090
      volumes:
        - name: volume-1718246899090
          persistentVolumeClaim:
            claimName: cnfs-nas-static-pvc
