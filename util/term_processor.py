# util/term_interv.py
# -*- coding: utf-8 -*-
import re
from logger.logger import biz_logger
from models.TranslateRequest import Terminology
from typing import List, Dict

class TerminologyProcessor:
    """
    术语替换与恢复处理：
      1. term_replace: 用占位符替换源文本中的术语；如果某条 src_text 恰好等于某个术语，则标记 is_not_translate=True
      2. post_translation: 用 tgt_term / src_term 替换翻译后文本中的占位符，纯术语行在 zh2en 场景下也恢复为 tgt_term
      3. 只在 en2EN 场景下给相邻占位符之间补空格
    """
    def __init__(self):
        pat = r"(?ix)<[_\s]*T[_\s]*E[_\s]*R[_\s]*M[_\s]*(\d{3})[_\s]*>"
        self._ph_re = re.compile(pat)


    def term_replace(self,
                     src_text_list: List[str],
                     term_list: List[Terminology],
                     src_lang: str) -> List[str]:
        # 1) 标记“纯术语”行
        for term in term_list:
            if not term.src_term:
                continue
            for txt in src_text_list:
                if txt.strip() == term.src_term.strip():
                    term.is_not_translate = True
                    # biz_logger.info(f"检测到纯术语行，标记不翻译：{term.src_term}")
                    break

        # 2) 按长度降序替换所有术语为占位符
        valid = [t for t in term_list if t.src_term and t.placeholder]
        valid.sort(key=lambda t: len(t.src_term), reverse=True)

        def _replace_one(text: str, term: Terminology) -> str:
            flags = 0 if term.is_case_sensitive else re.IGNORECASE
            try:
                return text.replace(term.src_term, term.placeholder)
            except Exception as e:
                # biz_logger.error(f"术语'{term.src_term}'替换异常: {e}", exc_info=True)
                return text

        out = []
        for txt in src_text_list:
            cur = txt
            for term in valid:
                cur = _replace_one(cur, term)
            # biz_logger.info(f"术语替换: 原文='{txt}' -> '{cur}'")
            out.append(cur)
        return out

    def create_term_dict_with_placeholders(self,
                                          term_list: List[Terminology]) -> Dict[str, Terminology]:
        d: Dict[str, Terminology] = {}
        for term in term_list:
            if term.placeholder:
                d[term.placeholder] = term
        return d

    def replace_placeholders(self,
                             text: str,
                             terminology_dict: Dict[str, Terminology],
                             src_lang: str,
                             tgt_lang: str) -> str:
        # 1) 标准化所有变体到 <__TERM###>
        def _norm(m): return f"<__TERM{m.group(1)}>"
        text = self._ph_re.sub(_norm, text)

        # 3) 最终替换成 src_term/tgt_term
        def _sub(m):
            ph = m.group(0)
            term = terminology_dict.get(ph)
            if not term:
                # biz_logger.warning(f"未知占位符 '{ph}'，保留原样。")
                return ph
            # 如果是标记不翻译的纯术语行
            if term.is_not_translate:
                # 中译英场景：也要输出英文 tgt_term
                if src_lang.lower().startswith('zh') and tgt_lang.lower().startswith('en'):
                    repl = term.tgt_term
                else:
                    repl = term.tgt_term
            else:
                repl = term.tgt_term
            # biz_logger.info(f"占位符替换: '{ph}' → '{repl}'")
            return repl

        return self._ph_re.sub(_sub, text)

    def post_translation(self,
                         translated_list: List[str],
                         src_lang: str,
                         tgt_lang: str,
                         term_list: List[Terminology]) -> List[str]:
        term_dict = self.create_term_dict_with_placeholders(term_list)
        out: List[str] = []
        for txt in translated_list:
            # biz_logger.info(f"模型直译: '{txt}'")
            restored = self.replace_placeholders(txt, term_dict, src_lang, tgt_lang)
            out.append(restored)
            # biz_logger.info(f"术语恢复后: '{restored}'")
        return out
