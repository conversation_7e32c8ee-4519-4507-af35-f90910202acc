# filename: htmlparser_custom.py
import re
import logging
import html
from html.parser import HTMLParser

logger = logging.getLogger(__name__)

class CustomHTMLParser(HTMLParser):
    """
    自定义 HTML 解析器。
    解析后，结构化存储在 self.parsed_data 中，数据形式约为:
    [
        ("label", "left" or "right", "<tag ...>", start_idx, end_idx),
        ("text", None, "纯文本内容", start_idx, end_idx),
        ...
    ]
    一旦出现无法正常解析的情况，则会设置 self.parse_error=True 并降级为纯文本处理。
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.parsed_data = []
        self._tags = []  # [(tag_position, tag_name, tag_text, start_idx, end_idx)]

        self.data_ori = None
        self.total_length_processed_escaped = 0
        self.total_length_processed_unescaped = 0
        self.latest_left_tag = ''
        self.latest_left_tag_end = -1
        self.parse_error = False

        # 实体解析栈
        # 每个元素: [entity原文, global_unescaped_start, global_unescaped_end, global_escaped_start, global_escaped_end, len_diff]
        self.entity_stack = []

    def feed(self, data: str):
        """
        额外处理一些非标准 HTML 的情况，以免 parse 出错。
        """
        self.data_ori = data

        # 简单检查是否包含 <tag>...</tag>结构，不是则视为普通文本
        # （可根据业务需要自行调整规则）
        if not re.search(r'<\s*\w+[^<>]*?>.*?</\s*\w+[^<>]*?>', data):
            self.parsed_data = [("text", None, data, 0, len(data))]
            return

        # 初始化 entity_stack
        self.entity_stack = []
        offset = 0
        # 找到所有 &xxx; 的实体，计算其位置、长度差
        for m in re.finditer(r'&[^;\s]*;', data):
            entity_text = m.group()
            entity_start = m.start()
            entity_end = m.end()
            self.entity_stack.append([
                entity_text,
                entity_start,
                entity_end,
                entity_start,
                entity_end,
                0  # len_diff
            ])
        # 计算实体解码的偏移量
        for entity in self.entity_stack:
            entity[1] -= offset
            unescaped_text = html.unescape(entity[0])
            len_diff = len(entity[0]) - len(unescaped_text)
            offset += len_diff
            entity[2] -= offset
            entity[5] = len_diff

        # 将 stack 逆序以便后续处理
        self.entity_stack.reverse()

        # 调用父类进行解析
        super().feed(data)

        # 如果解析中发生错误，则将整段数据视为纯文本
        if self.parse_error:
            self.parsed_data = [("text", None, data, 0, len(data))]
            self._tags = []

    def process_unescaped_index(self, unescaped_data: str):
        """
        处理并计算解析到的文本在原文 data_ori 中的 (start,end) 索引。
        并同步更新 total_length_processed_escaped 及相关 entity 信息。
        """
        global_unescaped_start = self.total_length_processed_unescaped
        global_unescaped_end = global_unescaped_start + len(unescaped_data)

        global_escaped_start = self.total_length_processed_escaped
        temp_escaped_end = global_escaped_start + len(unescaped_data)
        entity_len_diff = 0

        while len(self.entity_stack) > 0:
            (entity,
             entity_global_unescaped_start,
             entity_global_unescaped_end,
             entity_global_escaped_start,
             entity_global_escaped_end,
             len_diff) = self.entity_stack[-1]

            # 若该实体起始位置在当前解析段落之后，则后续不属于此段
            if entity_global_unescaped_start >= global_unescaped_end:
                break

            if entity_global_unescaped_end <= global_unescaped_end:
                tmp_start = entity_global_escaped_start - global_escaped_start
                tmp_end = entity_global_escaped_end - global_escaped_start
                if unescaped_data[tmp_start: tmp_end] == entity:
                    self.entity_stack.pop()
                    # 更新剩余实体
                    for tmp_entity_item in self.entity_stack:
                        tmp_entity_item[1] += len_diff
                        tmp_entity_item[2] += len_diff
                else:
                    entity_len_diff += len_diff
                    self.entity_stack.pop()
            else:
                # 异常情况
                logger.error(f"Parse Error: entity range mismatch. entity={entity}")
                self.entity_stack.pop()
                self.parse_error = True

        global_escaped_end = temp_escaped_end + entity_len_diff
        escaped_data = self.data_ori[global_escaped_start:global_escaped_end]
        data_ori = escaped_data

        self.total_length_processed_escaped = global_escaped_end
        self.total_length_processed_unescaped += len(unescaped_data)
        return data_ori, global_escaped_start, global_escaped_end

    def handle_starttag(self, tag, attrs):
        """
        处理开始标签，如 <p>, <div>, <a> 等。
        """
        tag_text_unescaped = self.get_starttag_text()
        tag_text_ori, global_escaped_start, global_escaped_end = self.process_unescaped_index(tag_text_unescaped)

        self.parsed_data.append(("label", 'left', tag_text_ori, global_escaped_start, global_escaped_end))
        self._tags.append(('left', tag, tag_text_ori, global_escaped_start, global_escaped_end))

        self.latest_left_tag = tag_text_ori
        self.latest_left_tag_end = global_escaped_end

    def is_void_element_tag(self) -> bool:
        """
        判断是否为自闭合标签(如 <br/>, <img/> 等)。
        """
        return (self.latest_left_tag_end == self.total_length_processed_escaped
                and self.latest_left_tag.endswith("/>"))

    def handle_endtag(self, tag):
        """
        处理结束标签，如 </p>, </div> 等。
        若是自闭合标签，则无需再次添加 'right'。
        """
        if self.is_void_element_tag():
            start_tag_item = self._tags[-1]
            self._tags.append(('right',) + start_tag_item[1:])
            return

        global_escaped_start = self.total_length_processed_escaped
        global_escaped_end = self.data_ori.find('>', global_escaped_start)
        if global_escaped_end == -1:
            self.parse_error = True
            return

        tag_text_unescaped = self.data_ori[global_escaped_start:global_escaped_end + 1]
        tag_text_ori, global_escaped_start, global_escaped_end = self.process_unescaped_index(tag_text_unescaped)

        self.parsed_data.append(("label", 'right', tag_text_ori, global_escaped_start, global_escaped_end))
        self._tags.append(('right', tag, tag_text_ori, global_escaped_start, global_escaped_end))

    def handle_data(self, data):
        """
        处理标签之间的纯文本。
        """
        data_ori, global_escaped_start, global_escaped_end = self.process_unescaped_index(data)
        self.parsed_data.append(("text", None, data_ori, global_escaped_start, global_escaped_end))
