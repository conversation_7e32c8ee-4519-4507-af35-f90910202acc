import logging
import os
import queue
import threading
import time
from typing import Any, Callable, List, Tuple, Union
from inference.executor import Executor

from inference.executor import BatchingExecutor

MAX_DATA_UNEXECUTED_TIME = 60 * 5


class TransIndex:
    _trans_id_counter = 0
    _locker = threading.Lock()

    @classmethod
    def increasement(cls):
        with cls._locker:
            cls._trans_id_counter += 1
            return cls._trans_id_counter


class Scheduler:

    def __init__(self, timeout, max_queue_size, max_batch_size, batch_wait_timeout_s, trans_func_chain: Tuple[Callable],
                 batching_func_idx=-1) -> None:
        self.max_queue_size = max_queue_size
        self.max_batch_size = max_batch_size
        self._trans_func_chain = trans_func_chain
        self._locker = threading.Lock()
        self._trans_id_counter = 0
        self._input_queue = Executor.DataQueue(max_queue_size)
        self._output_queue = None
        self._executors: List[Executor] = []
        self.timeout = timeout
        for i, func in enumerate(trans_func_chain):
            if i == batching_func_idx:
                self._executors.append(
                    BatchingExecutor(i, func, max_queue_size=max_queue_size, max_batch_size=max_batch_size,
                                     batch_wait_timeout_s=batch_wait_timeout_s))
            else:
                self._executors.append(Executor(i, func, max_queue_size=max_queue_size))

        self._executors[0] = self._input_queue >> self._executors[0]
        for i, e in enumerate(self._executors):
            if i < (len(self._executors) - 1):
                e >> self._executors[i + 1]
        self._output_queue = self._executors[-1] >> self._output_queue

        for e in self._executors:
            e.start()

        self._maintain_t = threading.Thread(target=self._maintain_loop, args=(self._input_queue, self._output_queue))

    def exec_sync(self, data: Union[Any, List], timeout=None) -> List:
        # logging.info(f"+++++data: {data}")
        trans_id = self.exec_async(data)
        # logging.info(f"=====+++++trans_id: {trans_id}")
        return self.get_response(trans_id, timeout)

    def exec_async(self, data: Union[Any, List]) -> int:
        if self._input_queue.size >= self.max_queue_size:
            raise queue.Full
        else:
            trans_id = TransIndex.increasement()
            if isinstance(data, List):
                for d in data:
                    # logging.info(f"=====+++++d: {d}")
                    self._input_queue.put((trans_id, d))
            else:
                # logging.info(f"=====+++++data: {data}")
                self._input_queue.put((trans_id, data))

            return trans_id

    def get_response(self, trans_id, timeout):
        data_list = self._output_queue.get_by_tidx(tidx=trans_id, timeout=timeout)
        if data_list is None:
            raise queue.Empty
        # logging.info(f"=====+++++data_list: {data_list}")
        if isinstance(data_list, List):
            _, response = zip(*data_list)
            # logging.info(f"=====+++++response: {response}")
            return response
        elif isinstance(data_list, Tuple):
            return [data_list[1]]
        else:
            raise ValueError(f"the response data type {type(data_list)} is invalid")

    def _maintain_loop(self, in_q: Executor.DataQueue, out_q: Executor.DataQueue):
        # 监听和维护出入队列的数据状态
        tmp_in_front_data_tid = None
        tmp_out_front_data_tid = None
        unexecuted_counter = 0
        abanduned_counter = 0
        while (True):
            # 监控入列数据是否未长时间被处理，如果是，认为调度器或者引擎存在异常挂起，需要重启
            # TODO(lingyue.ly):数据未被执行的时间需要根据实际执行能力评估
            if MAX_DATA_UNEXECUTED_TIME < unexecuted_counter:
                logging.error(
                    f"the scheduler has data unexecuted in {MAX_DATA_UNEXECUTED_TIME}s,the scheduler is hanging up,exit the process!!!")
                os._exit(-1)
            front_data = in_q.front()
            if front_data != None and front_data[0] == tmp_in_front_data_tid:
                unexecuted_counter += 1
            else:
                unexecuted_counter = 0
                tmp_in_front_data_tid = front_data[0]

            # 监控出队列数据是否长时间未被取走，如果是，认为调用方已断开或者退出该次调用，需要移除消息
            front_data = out_q.front()
            if front_data != None and front_data[0] == tmp_out_front_data_tid:
                abanduned_counter += 1
            else:
                abanduned_counter = 0

            if abanduned_counter > 3:
                logging.warning(
                    f"the response data of trans id:{front_data[0]} is abandoned,remove it from the output queue")
                self._output_queue.get_by_tidx([front_data[0]])

            time.sleep(1)
