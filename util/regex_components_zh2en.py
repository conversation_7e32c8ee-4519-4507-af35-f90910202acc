# util/regex_components_en2zh.py
import re
from collections import OrderedDict
from util.jiaobiao import SUPERSCRIPT_MAP, SUBSCRIPT_MAP
from configurer.config_reader import get_non_translation_exact_strings


# 本地定义的"精确非译"字符串
NON_TRANSLATION_EXACT_STRINGS = [
    # "Doc.Ref.No.DER-GDL-001-03",
    # 'SpO₂',
    # 'MFF-0300328',
    # 'cmc450623 1/81',
    # "Doc.",
    # "Ref.",
    # "No.",
    # "DER-GDL-001-03",
    # 'Everest IWRS',
    # 'Novotech',
    # 'Cₘₐₓ'
]

# 合并本地和线上配置的"精确非译"字符串
exact_match_strings = get_non_translation_exact_strings() or []  # 线上配置
all_exact_strings = list(set(exact_match_strings + NON_TRANSLATION_EXACT_STRINGS))  # 合并去重



# ——— 上下标字符 ——#
ALL_SCRIPT_CHARS = ''.join(SUPERSCRIPT_MAP.values()) + ''.join(SUBSCRIPT_MAP.values())
ALL_SCRIPTS = f"[{re.escape(ALL_SCRIPT_CHARS)}]+"
ALL_SCRIPTS_RE = re.compile(ALL_SCRIPTS)

# 允许左右不跟 [A-Za-z0-9] 也不跟上下标
_B = rf'(?<![A-Za-z0-9{ALL_SCRIPT_CHARS}])'
_B2 = rf'(?![A-Za-z0-9{ALL_SCRIPT_CHARS}])'
exact_match = _B + r'(?:' + '|'.join(map(re.escape, all_exact_strings)) + r')' + _B2
exact_match_re = re.compile(exact_match)

# —— 最终匹配字典（按顺序：先长后短/先精确后宽松） ——#
PATTERNS_ZH2EN = OrderedDict({
    'exact_match':           exact_match_re,

    # 7. 最后匹配所有上下标
    'all_scripts':           ALL_SCRIPTS_RE,
})

