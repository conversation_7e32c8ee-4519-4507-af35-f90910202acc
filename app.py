#!/usr/bin/env python
# encoding: utf-8
import multiprocessing
from fastapi import FastAP<PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware

from fastapi.params import Depends

from aop.auth import AuthMiddleware, auth_middleware

from aop.context import ContextMiddleware
from api.health import router as health_router
from api.translation import router as translate_router
from configurer.yy_nacos import Nacos

from logger.logger import app_logger


def create_app():
    _app = FastAPI()

    origins = [
        "*",
    ]

    _app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    # _app.add_middleware(AuthMiddleware)
    _app.add_middleware(ContextMiddleware)

    # API routers
    router = APIRouter()
    router.include_router(health_router, include_in_schema=False)
    router.include_router(translate_router, include_in_schema=False)

    _app.include_router(router, dependencies=[Depends(auth_middleware)])
    _app.add_event_handler("startup", Nacos)

    app_logger.info("app created.")
    return _app


app = create_app()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app:app", host="127.0.0.1", port=7860, reload=True, access_log=False)
