from typing import Any, Union

from pydantic import BaseModel, Field

from logger.biz_log import update_biz_log_context


class ResultDTO(BaseModel):
    success: bool = Field(False)
    code: int = Field(...)
    message: str = Field(None)
    result: Any = Field(None)
    time_cost: int = Field(None)


class TranslateResultDTO(ResultDTO):
    sent_count: int = Field(None)
    word_count: int = Field(None)


def make_success(data: Any) -> Union[ResultDTO]:
    return ResultDTO(
        success=True,
        code=0,
        result=data
    )


def make_fail(code: int, message: str) -> Union[ResultDTO]:
    update_biz_log_context(success=False, err_msg=message)
    return ResultDTO(success=False, code=code, message=message)
