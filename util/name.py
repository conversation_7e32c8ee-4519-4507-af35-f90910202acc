import re
from typing import List, Tuple, Dict
from configurer.config_reader import get_name_list

try:
    from pypinyin import lazy_pinyin, Style
except ImportError:
    print("Warning: pypinyin not installed. Please install with: pip install pypinyin")
    lazy_pinyin = None

# 本地硬编码的姓名列表
LOCAL_NAMES = ["李明", "林小玉"]

# 线上配置姓名列表
online_names = get_name_list() or []
ALL_NAMES = sorted(set(online_names + LOCAL_NAMES), key=len, reverse=True)

# 构建姓氏集合，用于快速判断需不需要处理
SURNAME_SET = set(name[0] for name in ALL_NAMES if name)

# 编译姓名匹配正则
escaped = [re.escape(name) for name in ALL_NAMES]
NAME_REGEX = re.compile(r"(?:%s)" % "|".join(escaped))

# 占位符正则，匹配多种形式
PLACEHOLDER_REGEX = re.compile(r"<_*(?:PERSON_NAME)_*_*(\d{3})_*>")

def chinese_to_pinyin(chinese_name: str) -> str:
    """
    将中文姓名转换为拼音格式：名在前 姓在后
    例如：林小玉 -> Xiaoyu Lin
    """
    if not lazy_pinyin:
        return f"[{chinese_name}]"
    if len(chinese_name) < 2:
        return chinese_name

    surname = chinese_name[0]
    given   = chinese_name[1:]

    # 姓氏全拼，首字母大写
    surname_py = lazy_pinyin(surname, style=Style.NORMAL)[0].capitalize()

    # 名字全拼，先 join 再整体 capitalize
    given_list = lazy_pinyin(given, style=Style.NORMAL)       # e.g. ["xiao","yu"]
    given_py   = "".join(given_list).capitalize()             # "xiaoyu" -> "Xiaoyu"

    return f"{given_py} {surname_py}"


class NameReplacer:
    def __init__(self):
        self.num_map: Dict[str, str] = {}
        self.counter = 0

    def replace_with_placeholder(self, text: str) -> str:
        # 只对含已知姓氏的句子处理
        if not any(ch in SURNAME_SET for ch in text):
            return text

        def _repl(m: re.Match) -> str:
            name = m.group(0)
            self.counter += 1
            num = f"{self.counter:03d}"
            placeholder = f"<__PERSON_NAME{num}>"
            self.num_map[num] = name
            return placeholder

        return NAME_REGEX.sub(_repl, text)

    def restore_with_pinyin(self, text: str) -> str:
        def _repl(m: re.Match) -> str:
            num = m.group(1)
            orig = self.num_map.get(num)
            if not orig:
                return m.group(0)
            # 只要拼音，不带中文
            return chinese_to_pinyin(orig)
        return PLACEHOLDER_REGEX.sub(_repl, text)


    def process(self, text: str) -> Tuple[str, str]:
        # 仅当句子含已知姓氏才走替换／还原
        if not any(ch in SURNAME_SET for ch in text):
            return text, text

        self.num_map.clear()
        self.counter = 0
        ph = self.replace_with_placeholder(text)
        final = self.restore_with_pinyin(ph)
        return ph, final



# 测试
if __name__ == "__main__":
    # 全局实例
    name_replacer = NameReplacer()

    def replace_names_in_text(text: str) -> Tuple[str, str]:
        return name_replacer.process(text)
    
    samples = [
        "今天天气很好，无需处理。",
        "今天林小玉和李明一起去散步。",
    ]
    for s in samples:
        ph, final = replace_names_in_text(s)
        print(f"原文：{s}")
        print(f"占位符：{ph}")
        print(f"最终：{final}\n")
