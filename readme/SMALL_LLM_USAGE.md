# 小参数大模型翻译功能使用说明

## 功能概述

本系统现在支持两种翻译模式：
1. **机翻模型**（默认）：使用传统的机器翻译模型，包含完整的前后处理流程
2. **小参数大模型**：使用小参数大模型进行翻译，**完全绕过前后处理**，直接调用接口

## 使用方法

### 1. 使用机翻模型（默认）

不设置特殊请求头，系统会自动使用机翻模型：

```bash
curl -X POST 'http://localhost:7861/translate' \
  --header 'Content-Type: application/json' \
  --data-raw '{
    "src_text_list": ["Hello world"],
    "src_lang": "en",
    "tgt_lang": "zh",
    "terms": []
  }'
```

### 2. 使用小参数大模型

设置请求头 `use-small-llm: true` 来启用小参数大模型：

```bash
curl -X POST 'http://localhost:7861/translate' \
  --header 'Content-Type: application/json' \
  --header 'use-small-llm: true' \
  --data-raw '{
    "src_text_list": ["Hello world"],
    "src_lang": "en",
    "tgt_lang": "zh",
    "terms": []
  }'
```

### 3. 特殊格式处理 + 小参数大模型

同时启用特殊格式处理和小参数大模型：

```bash
curl -X POST 'http://localhost:7861/translate' \
  --header 'Content-Type: application/json' \
  --header 'use-small-llm: true' \
  --header 'enable-special-format: true' \
  --data-raw '{
    "src_text_list": ["剂量设定为5 mg/kg（口服给药）。"],
    "src_lang": "zh",
    "tgt_lang": "en",
    "terms": []
  }'
```

## 请求头参数

| 请求头 | 值 | 说明 |
|--------|-----|------|
| `use-small-llm` | `true` | 启用小参数大模型翻译 |
| `use-small-llm` | `false` 或不设置 | 使用机翻模型（默认） |
| `enable-special-format` | `true` | 启用特殊格式处理 |
| `enable-special-format` | `false` 或不设置 | 不启用特殊格式处理（默认） |

## 配置说明

小参数大模型的配置在 `small_llm_client.py` 中：

```python
class SmallLLMClient:
    def __init__(self):
        self.base_url = "https://f-dev.yiya-ai.com/v1/workflows/run"
        self.auth_token = "app-7SBEg21uxyuabI7WjC6Z0l1o"
        self.workflow_id = "05e56425-59d3-49be-a9a3-a963ce1c34a8"
        self.timeout = 120  # 请求超时时间（秒）
        self.max_workers = 10  # 最大并发线程数
```

## 测试

运行测试脚本：

```bash
cd yxc/mt_server/test
python test_small_llm.py
```

## 注意事项

1. **小参数大模型完全绕过前后处理**：不会进行文本预处理、标签掩码、后处理等操作
2. **术语处理**：小参数大模型支持术语干预，术语会以 `|||` 分隔的字符串形式传递
3. **并发处理**：使用线程池并发处理大量文本，提高翻译效率
4. **网络依赖**：小参数大模型需要网络连接
5. **失败回退**：如果小参数大模型调用失败，系统会自动回退到机翻模型流程
6. **响应时间**：小参数大模型的响应时间可能比机翻模型长
7. **超时配置**：建议在生产环境中配置适当的超时时间
8. **并发配置**：可调整 `max_workers` 参数控制并发线程数

## 错误处理

### 小参数大模型失败时：
- 网络异常：自动回退到机翻模型流程
- 认证失败：自动回退到机翻模型流程  
- 响应格式错误：自动回退到机翻模型流程
- 超时：自动回退到机翻模型流程

### 机翻模型流程：
- 如果小参数大模型失败，系统会自动切换到完整的机翻模型流程
- 包含所有前后处理步骤，确保翻译质量

所有异常情况都会记录到日志中，便于问题排查。

## 性能特点

| 特性 | 小参数大模型 | 机翻模型 |
|------|-------------|----------|
| 响应时间 | 较长（网络调用） | 较短（本地处理） |
| 翻译质量 | 较高 | 中等 |
| 前后处理 | 无 | 完整 |
| 标签处理 | 无 | 完整 |
| 术语支持 | 基础（|||分隔） | 完整（复杂逻辑） |
| 网络依赖 | 是 | 否 |
| 并发处理 | 支持（10线程） | 不支持 |
| 批次大小 | 8个文本/批 | 无限制 |
