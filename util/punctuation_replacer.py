# util/punctuation_replacer.py
# -*- coding: utf-8 -*-

import re
from configurer.config_reader import get_punctuation_mapping, project_config
from logger.logger import biz_logger


# ----------------------------------------
# 6) 主入口：根据中英文方向依次调用
# ----------------------------------------
def replace_punctuation(orig: str, trans: str, src: str, tgt: str) -> str:
    biz_logger.info(f"开始标点替换处理，翻译方向: {src}→{tgt}")
    text = trans

    # zh→en：先 replace 多字符，再 translate 单字符
    if src == 'zh' and tgt == 'en':
        biz_logger.info("执行中译英标点处理")
        # 1) 整个映射表
        mapping = {
            '，': ',', '：': ':', '；': ';', '？': '?', '！': '!',
            '。': '.', '……': '...', '——': '-',
            '（': '(', '）': ')', '【': '[', '】': ']'
        }

        # 2) 拆分出多字符和单字符的子集
        multi_char = {k: v for k, v in mapping.items() if len(k) > 1}
        single_char = {k: v for k, v in mapping.items() if len(k) == 1}

        # 3) 多字符 .replace()
        for k, v in multi_char.items():
            text = text.replace(k, v)

        # 4) 单字符 translate
        result = text.translate(str.maketrans(single_char))
        biz_logger.info("完成中译英标点处理")
        return result

    

    biz_logger.info(f"不支持的翻译方向: {src}→{tgt}，跳过标点处理")
    return text
