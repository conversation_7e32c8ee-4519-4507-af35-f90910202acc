# api/translation.py

import time
import json
from collections import defaultdict
from typing import List, <PERSON><PERSON>

from fastapi import APIRouter
from fastapi.responses import PlainTextResponse

from models.TranslateRequest import TranslateRequest, Terminology
from translate_api import TranslateAPI
from logger.biz_log import biz_log, BizAction
from logger.logger import biz_logger

from util.pre_processor import pre_process
from util.post_processor import post_process
from util.term_processor import TerminologyProcessor
from util.character_util import calc_word_count
from util.tag_simple import (
    mask_tags_list,
    restore_tags_texts,
    split_by_tag_count
)
from models.result import TranslateResultDTO
from configurer.config_reader import get_term_interv_placeholder

router = APIRouter(include_in_schema=False)
translate_api = TranslateAPI()
term_processor = TerminologyProcessor()

# 拆分阈值
SPLIT_THRESHOLD = 12
MAX_TAGS_PER_CHUNK = 12

# 调试
is_print = True
is_repr_print = False

def build_success_result(
    src_list: List[str],
    src_lang: str,
    result_list: List[str],
    start_time: float
) -> str:
    biz_logger.info("开始构建翻译成功结果")
    biz_logger.info(f"源文本列表长度：{len(src_list)}")
    biz_logger.info(f"结果列表长度：{len(result_list)}")
    biz_logger.info(f"源语言：{src_lang}")
    
    word_count = calc_word_count(src_list, src_lang)
    time_cost = (time.time() - start_time)*1000
    
    biz_logger.info(f"计算得到的字数：{word_count}")
    biz_logger.info(f"计算得到的耗时：{time_cost:.2f}ms")
    
    dto = TranslateResultDTO(
        code=0,
        message='OK',
        result=result_list,
        time_cost=time_cost,
        success=True,
        sent_count=len(src_list),
        word_count=word_count
    )
    
    result_json = json.dumps(dto.__dict__, ensure_ascii=False)
    biz_logger.info(f"构建翻译成功结果完成，JSON长度：{len(result_json)}")
    return result_json

def _core_split_translate_merge(
    flat_texts: List[str],
    flat_maps: List[List[Tuple[str,str]]],
    orig_idx: List[int],
    src_lang: str,
    tgt_lang: str,
    pure_idxs: set
) -> List[str]:
    """
    flat_texts 已经 split, flat_maps/orig_idx 对应
    pure_idxs: 需要保留原文 (跳过模型) 的 chunk 索引
    """
    biz_logger.info(f"开始核心翻译流程，源语言：{src_lang}，目标语言：{tgt_lang}")
    biz_logger.info(f"扁平化文本数量：{len(flat_texts)}")
    biz_logger.info(f"扁平化标签映射数量：{len(flat_maps)}")
    biz_logger.info(f"原始索引数量：{len(orig_idx)}")
    biz_logger.info(f"跳过翻译的索引集合：{pure_idxs}")
    
    # 1) 调用模型：跳过 pure_idxs
    to_model = [txt for i, txt in enumerate(flat_texts) if i not in pure_idxs]
    biz_logger.info(f"需要翻译的文本段数：{len(to_model)}，跳过翻译的段数：{len(pure_idxs)}")
    
    if to_model:
        biz_logger.info("准备发送到翻译模型的文本：")
        for i, txt in enumerate(to_model):
            biz_logger.info(f"  [{i}]: {txt}")
    else:
        biz_logger.info("没有文本需要发送到翻译模型")
    
    translated = translate_api.translate(to_model, src_lang, tgt_lang) if to_model else []
    biz_logger.info(f"翻译模型返回结果数量：{len(translated)}")
    
    if translated:
        biz_logger.info("翻译模型返回的结果：")
        for i, txt in enumerate(translated):
            biz_logger.info(f"  [{i}]: {txt}")
    
    it = iter(translated)

    # 2) 重建 flat_out
    biz_logger.info("开始重建翻译结果列表")
    flat_out = []
    for i in range(len(flat_texts)):
        if i in pure_idxs:
            result_text = flat_texts[i]
            biz_logger.info(f"索引 {i} 跳过翻译，保持原文：{result_text}")
        else:
            result_text = next(it)
            biz_logger.info(f"索引 {i} 使用翻译结果：{result_text}")
        flat_out.append(result_text)
    
    biz_logger.info(f"重建翻译结果列表完成，结果数量：{len(flat_out)}")

    # 3) 还原标签
    biz_logger.info("开始还原文本中的标签")
    biz_logger.info(f"准备还原标签的文本数量：{len(flat_out)}")
    biz_logger.info(f"标签映射数量：{len(flat_maps)}")
    
    flat_restored = restore_tags_texts(flat_out, flat_maps)
    biz_logger.info(f"标签还原完成，还原后文本数量：{len(flat_restored)}")
    
    if flat_restored:
        biz_logger.info("标签还原后的文本：")
        for i, txt in enumerate(flat_restored):
            biz_logger.info(f"  [{i}]: {txt}")

    # 4) 合并回原始条目
    biz_logger.info("开始合并翻译结果到原始条目")
    merged = defaultdict(list)
    for idx, seg in zip(orig_idx, flat_restored):
        merged[idx].append(seg)
        biz_logger.info(f"将段落 '{seg}' 合并到原始索引 {idx}")
    
    # 注意：merged_list 长度要与原始 src_list 一致
    max_idx = max(orig_idx) if orig_idx else -1
    biz_logger.info(f"最大原始索引：{max_idx}")
    
    result = []
    for i in range(max_idx+1):
        merged_text = "".join(merged[i])
        result.append(merged_text)
        biz_logger.info(f"原始索引 {i} 合并后的文本：{merged_text}")
    
    biz_logger.info(f"核心翻译流程完成，合并后条目数：{len(result)}")
    return result

def _translate_without_terms(
    texts: List[str],
    tag_maps: List[List[Tuple[str,str]]],
    src_lang: str,
    tgt_lang: str
) -> List[str]:
    """
    纯"无术语"流程：mask→split→translate→restore→merge
    """
    biz_logger.info("开始无术语翻译流程")
    biz_logger.info(f"输入文本数量：{len(texts)}")
    biz_logger.info(f"标签映射数量：{len(tag_maps)}")
    
    # 扁平化
    flat_texts, flat_maps, orig_idx = [], [], []
    biz_logger.info("开始文本扁平化处理")
    
    for idx, (t, mp) in enumerate(zip(texts, tag_maps)):
        biz_logger.info(f"处理第 {idx} 个文本，标签数量：{len(mp)}")
        if len(mp) <= SPLIT_THRESHOLD:
            flat_texts.append(t)
            flat_maps.append(mp)
            orig_idx.append(idx)
            biz_logger.info(f"  文本 {idx} 标签数量 {len(mp)} <= 阈值 {SPLIT_THRESHOLD}，不拆分")
        else:
            biz_logger.info(f"  文本 {idx} 标签数量 {len(mp)} > 阈值 {SPLIT_THRESHOLD}，需要拆分")
            split_count = 0
            for ct, cmp in split_by_tag_count(t, mp, max_tags=MAX_TAGS_PER_CHUNK):
                flat_texts.append(ct)
                flat_maps.append(cmp)
                orig_idx.append(idx)
                biz_logger.info(f"    拆分片段 {split_count}：{ct}，标签数量：{len(cmp)}")
                split_count += 1
            biz_logger.info(f"  文本 {idx} 拆分为 {split_count} 个片段")

    biz_logger.info(f"文本扁平化完成，扁平化后段落数：{len(flat_texts)}")
    if is_print:
        biz_logger.info("扁平化后的文本：")
        for i, txt in enumerate(flat_texts):
            biz_logger.info(f"  [{i}]: {txt}")

    # 无 pure_idxs
    biz_logger.info("调用核心翻译合并流程（无术语模式）")
    merged = _core_split_translate_merge(
        flat_texts, flat_maps, orig_idx, src_lang, tgt_lang, pure_idxs=set()
    )
    biz_logger.info("无术语翻译流程完成")
    return merged

def _translate_with_terms(
    texts: List[str],
    tag_maps: List[List[Tuple[str,str]]],
    src_list_len: int,
    terms: List[Terminology],
    src_lang: str,
    tgt_lang: str
) -> List[str]:
    """
    带术语流程：先对 flat_texts 做 term_replace，再 split，再模型跳过整句术语，再 post_translation。
    """
    biz_logger.info(f"开始带术语翻译流程，术语数量：{len(terms)}")
    biz_logger.info(f"输入文本数量：{len(texts)}")
    biz_logger.info(f"标签映射数量：{len(tag_maps)}")
    biz_logger.info(f"源文本列表长度：{src_list_len}")
    
    # 注入 placeholder
    prefix = get_term_interv_placeholder()
    biz_logger.info(f"术语占位符前缀：{prefix}")
    
    for i, tm in enumerate(terms, start=1):
        tm.placeholder = f"<{prefix}{i:03d}>"
        biz_logger.info(f"术语 {i}：'{tm.src_term}' → '{tm.tgt_term}'，占位符：{tm.placeholder}，不翻译：{tm.is_not_translate}")
    biz_logger.info("术语占位符注入完成")

    # 1) 先在原文本层面做 term_replace，确保 placeholder 已经植入
    biz_logger.info("开始术语替换处理")
    texts = term_processor.term_replace(texts, terms, src_lang)
    biz_logger.info("术语替换完成")
    if is_print:
        biz_logger.info("术语替换后的文本：")
        for i, txt in enumerate(texts):
            biz_logger.info(f"  [{i}]: {txt}")

    # 扁平化
    flat_texts, flat_maps, orig_idx = [], [], []
    biz_logger.info("开始文本扁平化处理（带术语）")
    
    for idx, (t, mp) in enumerate(zip(texts, tag_maps)):
        biz_logger.info(f"处理第 {idx} 个文本，标签数量：{len(mp)}")
        if len(mp) <= SPLIT_THRESHOLD:
            flat_texts.append(t)
            flat_maps.append(mp)
            orig_idx.append(idx)
            biz_logger.info(f"  文本 {idx} 标签数量 {len(mp)} <= 阈值 {SPLIT_THRESHOLD}，不拆分")
        else:
            biz_logger.info(f"  文本 {idx} 标签数量 {len(mp)} > 阈值 {SPLIT_THRESHOLD}，需要拆分")
            split_count = 0
            for ct, cmp in split_by_tag_count(t, mp, max_tags=MAX_TAGS_PER_CHUNK):
                flat_texts.append(ct)
                flat_maps.append(cmp)
                orig_idx.append(idx)
                biz_logger.info(f"    拆分片段 {split_count}：{ct}，标签数量：{len(cmp)}")
                split_count += 1
            biz_logger.info(f"  文本 {idx} 拆分为 {split_count} 个片段")

    biz_logger.info(f"文本扁平化完成，扁平化后段落数：{len(flat_texts)}")
    if is_print:
        biz_logger.info("扁平化后的文本：")
        for i, txt in enumerate(flat_texts):
            biz_logger.info(f"  [{i}]: {txt}")

    # 找 pure_idxs：整句 placeholder chunk 不走模型
    biz_logger.info("开始识别需要跳过翻译的段落")
    placeholders = {tm.placeholder for tm in terms if tm.is_not_translate}
    biz_logger.info(f"不翻译术语的占位符集合：{placeholders}")
    
    pure_idxs = set()
    for i, txt in enumerate(flat_texts):
        if txt.strip() in placeholders:
            pure_idxs.add(i)
            biz_logger.info(f"段落 {i} 匹配不翻译占位符：{txt.strip()}")
    
    biz_logger.info(f"识别到需要跳过翻译的段落数：{len(pure_idxs)}")
    if is_print:
        biz_logger.info("需要跳过翻译的段落：")
        for i in pure_idxs:
            biz_logger.info(f"  [{i}]: {flat_texts[i]}")

    # 调用核心
    biz_logger.info("调用核心翻译合并流程（带术语模式）")
    flat_merged = _core_split_translate_merge(
        flat_texts, flat_maps, orig_idx, src_lang, tgt_lang, pure_idxs
    )

    # 最后把术语 placeholder 还原成目标术语
    biz_logger.info("开始还原术语占位符")
    biz_logger.info(f"准备还原占位符的文本数量：{len(flat_merged)}")
    
    final = term_processor.post_translation(
        translated_list=flat_merged,
        src_lang=src_lang,
        tgt_lang=tgt_lang,
        term_list=terms
    )
    
    biz_logger.info(f"术语占位符还原完成，最终结果数量：{len(final)}")
    biz_logger.info("带术语翻译流程完成")
    return final

@router.post('/translate', response_class=PlainTextResponse)
@biz_log(action=BizAction.mt)
def translate(req: TranslateRequest):
    start = time.time()
    src_list = req.src_text_list
    src_lang = req.src_lang
    tgt_lang = req.tgt_lang
    terms = req.terms or []
    
    biz_logger.info(f"收到翻译请求：{src_lang}→{tgt_lang}，文本数量：{len(src_list)}，术语数量：{len(terms)}")
    biz_logger.debug("原始文本列表：")
    for i, text in enumerate(src_list):
        biz_logger.debug(f"  [{i}]: {text}")
    
    if terms:
        biz_logger.info("术语列表：")
        for i, term in enumerate(terms):
            biz_logger.info(f"  [{i}]: {term.src_term} → {term.tgt_term} (不翻译: {term.is_not_translate})")

    # 1) pre‐process
    biz_logger.info("开始文本预处理")
    texts = pre_process(src_list, src_lang, tgt_lang)
    biz_logger.info(f"文本预处理完成，处理后文本数量：{len(texts)}")
    if is_print:
        pre_list = texts.copy()
        biz_logger.info("预处理后的文本：")
        for i, text in enumerate(texts):
            biz_logger.info(f"  [{i}]: {text}")

    # 2) mask 标签
    biz_logger.info("开始标签掩码处理")
    texts, tag_maps = mask_tags_list(texts)
    biz_logger.info(f"标签掩码处理完成，掩码后文本数量：{len(texts)}，标签映射数量：{len(tag_maps)}")
    if is_print:
        tag_simple_list = texts.copy()
        biz_logger.debug("标签掩码后的文本：")
        for i, text in enumerate(texts):
            biz_logger.debug(f"  [{i}]: {text}")
        biz_logger.info("标签映射：")
        for i, tag_map in enumerate(tag_maps):
            biz_logger.info(f"  [{i}]: {tag_map}")

    # 3) 分支：有术语 / 无术语
    if terms:
        biz_logger.info("检测到术语，使用带术语翻译流程")
        merged = _translate_with_terms(
            texts, tag_maps, len(src_list),
            terms, src_lang, tgt_lang
        )
    else:
        biz_logger.info("未检测到术语，使用无术语翻译流程")
        merged = _translate_without_terms(
            texts, tag_maps, src_lang, tgt_lang
        )

    biz_logger.info(f"翻译流程完成，合并后结果数量：{len(merged)}")

    # 4) post‐process
    biz_logger.info("开始文本后处理")
    final_list = post_process(src_list, merged, src_lang, tgt_lang)
    biz_logger.info(f"文本后处理完成，最终结果数量：{len(final_list)}")
    if is_print:
        post_list = final_list.copy()
        biz_logger.debug("后处理后的文本：")
        for i, text in enumerate(final_list):
            biz_logger.debug(f"  [{i}]: {text}")

    # 5) debug log
    if is_print:
        biz_logger.info("开始详细调试日志输出")
        for i in range(len(src_list)):
            biz_logger.info("─"*50)
            biz_logger.info(f"Segment {i}")
            biz_logger.info(f"原文        : {src_list[i]}")
            biz_logger.info(f"翻译前处理  : {pre_list[i]}")
            biz_logger.info(f"简标文本    : {tag_simple_list[i]}")
            if terms:
                biz_logger.info(f"★ 带术语流程")
            biz_logger.info(f"合并后文本  : {merged[i]!r}")
            biz_logger.info(f"翻译后处理  : {post_list[i]!r}")
        biz_logger.info("＝"*50)

    # 6) 返回
    biz_logger.info(f"翻译请求处理完成，耗时：{(time.time() - start)*1000:.2f}ms")
    return build_success_result(src_list, src_lang, final_list, start)
