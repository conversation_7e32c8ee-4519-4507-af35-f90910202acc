#!/usr/bin/env python3
"""
测试小参数大模型翻译功能
"""

import requests
import json
import time


def test_small_llm_translation():
    """测试小参数大模型翻译接口（完全绕过前后处理）"""
    
    # 测试数据
    test_cases = [
        {
            "src_lang": "en",
            "tgt_lang": "zh",
            "texts": ["Hello world", "Machine translation", "Artificial intelligence"],
            "terms": [
                {"src_term": "Machine translation", "tgt_term": "机器翻译", "is_not_translate": False}
            ]
        },
        {
            "src_lang": "zh",
            "tgt_lang": "en", 
            "texts": ["你好世界", "机器翻译", "人工智能"],
            "terms": [
                {"src_term": "机器翻译", "tgt_term": "Machine translation", "is_not_translate": False}
            ]
        }
    ]
    
    base_url = "http://localhost:7861"  # 根据实际部署地址调整
    
    for i, test_case in enumerate(test_cases):
        print(f"\n=== 测试用例 {i+1} ===")
        print(f"源语言: {test_case['src_lang']}")
        print(f"目标语言: {test_case['tgt_lang']}")
        print(f"源文本: {test_case['texts']}")
        
        # 构建请求
        payload = {
            "src_text_list": test_case['texts'],
            "src_lang": test_case['src_lang'],
            "tgt_lang": test_case['tgt_lang'],
            "terms": test_case['terms']
        }
        
        headers = {
            "Content-Type": "application/json",
            "use-small-llm": "true"  # 启用小参数大模型
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/translate",
                json=payload,
                headers=headers
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 翻译成功")
                print(f"耗时: {(end_time - start_time)*1000:.2f}ms")
                print(f"结果: {result.get('result', [])}")
                print(f"字数: {result.get('word_count', 0)}")
            else:
                print(f"❌ 翻译失败，状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        print("-" * 50)


def test_machine_translation():
    """测试机翻模型（默认行为）"""
    
    print("\n=== 测试机翻模型（默认） ===")
    
    payload = {
        "src_text_list": ["Hello world", "Machine translation"],
        "src_lang": "en",
        "tgt_lang": "zh",
        "terms": []
    }
    
    headers = {
        "Content-Type": "application/json"
        # 不设置 use-small-llm 头，默认使用机翻模型
    }
    
    base_url = "http://localhost:7861"
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/translate",
            json=payload,
            headers=headers
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 机翻成功")
            print(f"耗时: {(end_time - start_time)*1000:.2f}ms")
            print(f"结果: {result.get('result', [])}")
        else:
            print(f"❌ 机翻失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def test_special_format_with_small_llm():
    """测试特殊格式处理 + 小参数大模型"""
    
    print("\n=== 测试特殊格式处理 + 小参数大模型 ===")
    
    payload = {
        "src_text_list": [
            "剂量设定为5 mg/kg（口服给药）。",
            "试验组共记录103例（80.5%）不良事件。"
        ],
        "src_lang": "zh",
        "tgt_lang": "en",
        "terms": []
    }
    
    headers = {
        "Content-Type": "application/json",
        "use-small-llm": "true",  # 启用小参数大模型
        "enable-special-format": "true"  # 启用特殊格式处理
    }
    
    base_url = "http://localhost:7861"
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/translate",
            json=payload,
            headers=headers
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 特殊格式 + 小参数大模型成功")
            print(f"耗时: {(end_time - start_time)*1000:.2f}ms")
            print(f"结果: {result.get('result', [])}")
        else:
            print(f"❌ 特殊格式 + 小参数大模型失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")


if __name__ == "__main__":
    print("开始测试翻译功能...")
    
    # 测试小参数大模型
    test_small_llm_translation()
    
    # 测试机翻模型
    test_machine_translation()
    
    # 测试特殊格式 + 小参数大模型
    test_special_format_with_small_llm()
    
    print("\n测试完成！")
