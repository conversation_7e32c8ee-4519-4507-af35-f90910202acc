from __future__ import annotations

import re
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Set

# ------------------ 内置资源 ------------------

# 优化 2: 使用元组 (tuple) 代替列表，因为它是不可变数据，且预先排序好，避免运行时重复排序
# 使用 set 去重后再排序，确保 "司徒" 不会重复
# 无论是单姓还是复姓，都通过这一份列表进行匹配，更准确且易于维护
_ALL_SURNAMES: tuple[str, ...] = tuple(sorted({
    # 常见复姓 (76)
    "欧阳", "太史", "端木", "上官", "司马", "东方", "独孤", "南宫", "万俟", "闻人",
    "夏侯", "诸葛", "尉迟", "公羊", "赫连", "澹台", "皇甫", "宗政", "濮阳", "公冶",
    "太叔", "申屠", "公孙", "慕容", "仲孙", "钟离", "长孙", "宇文", "司徒", "鲜于",
    "司空", "闾丘", "子车", "亓官", "司寇", "巫马", "公西", "颛孙", "壤驷", "公良",
    "漆雕", "乐正", "宰父", "谷梁", "拓跋", "夹谷", "轩辕", "令狐", "段干", "百里",
    "呼延", "东郭", "南门", "羊舌", "微生", "公户", "公玉", "公仪", "梁丘", "公仲",
    "公上", "公门", "公山", "公坚", "左丘", "公伯", "西门", "公祖", "第五", "公乘",
    "贯丘", "公皙", "南荣", "东里", "东宫", "仲长", "子书", "子桑", "即墨", "达奚",
    "褚师", "吴铭",
    # 常见单姓 (437 - 去重后)
    "赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈", "褚", "卫", "蒋",
    "沈", "韩", "杨", "朱", "秦", "尤", "许", "何", "吕", "施", "张", "孔", "曹",
    "严", "华", "金", "魏", "陶", "姜", "戚", "谢", "邹", "喻", "柏", "水", "窦",
    "章", "云", "苏", "潘", "葛", "奚", "范", "彭", "郎", "鲁", "韦", "昌", "马",
    "苗", "凤", "花", "方", "俞", "任", "袁", "柳", "酆", "鲍", "史", "唐", "费",
    "廉", "岑", "薛", "雷", "贺", "倪", "汤", "滕", "殷", "罗", "毕", "郝", "邬",
    "安", "常", "乐", "于", "时", "傅", "皮", "卞", "齐", "康", "伍", "余", "元",
    "卜", "顾", "孟", "平", "黄", "穆", "萧", "尹", "姚", "邵", "湛", "汪", "祁",
    "毛", "禹", "狄", "米", "贝", "明", "臧", "计", "伏", "成", "戴", "谈", "宋",
    "茅", "庞", "熊", "纪", "舒", "屈", "项", "祝", "董", "梁", "杜", "阮", "蓝",
    "闵", "席", "季", "麻", "强", "贾", "路", "娄", "危", "江", "童", "颜", "郭",
    "梅", "盛", "林", "刁", "钟", "徐", "邱", "骆", "高", "夏", "蔡", "田", "樊",
    "胡", "凌", "霍", "虞", "万", "支", "柯", "昝", "管", "卢", "莫", "经", "房",
    "裘", "缪", "干", "解", "应", "宗", "丁", "宣", "贲", "邓", "郁", "单", "杭",
    "洪", "包", "诸", "左", "石", "崔", "吉", "钮", "龚", "程", "嵇", "邢", "滑",
    "裴", "陆", "荣", "翁", "荀", "羊", "於", "惠", "甄", "曲", "家", "封", "芮",
    "羿", "储", "靳", "汲", "邴", "糜", "松", "井", "段", "富", "巫", "乌", "焦",
    "巴", "弓", "牧", "隗", "山", "谷", "车", "侯", "宓", "蓬", "全", "郗", "班",
    "仰", "秋", "仲", "伊", "宫", "宁", "仇", "栾", "暴", "甘", "钭", "厉", "戎",
    "祖", "武", "符", "刘", "景", "詹", "束", "龙", "叶", "幸", "司", "韶", "郜",
    "黎", "蓟", "薄", "印", "宿", "白", "怀", "蒲", "邰", "从", "鄂", "索", "咸",
    "籍", "赖", "卓", "蔺", "屠", "蒙", "池", "乔", "阴", "胥", "能", "苍", "双",
    "闻", "莘", "党", "翟", "谭", "贡", "劳", "逄", "姬", "申", "扶", "堵", "冉",
    "宰", "郦", "雍", "郤", "璩", "桑", "桂", "濮", "牛", "寿", "通", "边", "扈",
    "燕", "冀", "浦", "尚", "农", "温", "别", "庄", "晏", "柴", "瞿", "阎", "充",
    "慕", "连", "茹", "习", "宦", "艾", "鱼", "容", "向", "古", "易", "慎", "戈",
    "廖", "庾", "终", "暨", "居", "衡", "步", "都", "耿", "满", "弘", "匡", "国",
    "文", "寇", "广", "禄", "阙", "东", "欧", "殳", "沃", "利", "蔚", "越", "夔",
    "隆", "师", "巩", "厍", "聂", "晁", "勾", "敖", "融", "冷", "訾", "辛", "阚",
    "那", "简", "饶", "空", "曾", "毋", "沙", "乜", "养", "鞠", "须", "丰", "巢",
    "关", "蒯", "相", "查", "后", "荆", "红", "游", "竺", "权", "逮", "盍", "益",
    "桓", "公"
}, key=len, reverse=True))

# 多音字姓氏拼音表 (保持不变)
_SURNAME_PINYIN: Dict[str, str] = {
    "单": "shan", "曾": "zeng", "仇": "qiu", "翟": "zhai",
    "解": "xie", "查": "zha", "乐": "yue", "繁": "po",
}

# ------------------ 抽象接口 ------------------

class NER(ABC):
    load_time: float = 0.0

    @abstractmethod
    def extract_person(self, text: str) -> List[str]:
        ...


class PinyinConverter(ABC):
    @abstractmethod
    def convert(self, name: str) -> str:
        ...


# ------------------- spaCy NER -------------------
# SpacyNER 类的实现已经很好了，无需改动
class SpacyNER(NER):
    def __init__(self):
        tic = time.perf_counter()
        import spacy
        self.nlp = spacy.load("zh_core_web_lg")
        SpacyNER.load_time = time.perf_counter() - tic

    def extract_person(self, text: str) -> List[str]:
        seen, out = set(), []
        for ent in self.nlp(text).ents:
            if ent.label_ == "PERSON" and ent.text not in seen:
                seen.add(ent.text)
                out.append(ent.text)
        return out


# ------------------ HanLP 2.x NER ------------------
# Hanlp2NER 类的实现也很好，无需改动
# 修正后的 Hanlp2NER 类
class Hanlp2NER(NER):
    def __init__(self):
        tic = time.perf_counter()
        import hanlp
        self.pipeline = hanlp.pipeline() \
            .append(hanlp.utils.rules.split_sentence, output_key='sentences') \
            .append(hanlp.load('FINE_ELECTRA_SMALL_ZH'), output_key='tok') \
            .append(hanlp.load('MSRA_NER_ELECTRA_SMALL_ZH'),
                    input_key='tok', output_key='ner')
        Hanlp2NER.load_time = time.perf_counter() - tic

    def extract_person(self, text: str) -> List[str]:
        doc = self.pipeline(text)
        seen, out = set(), []
        for sent_ner in doc['ner']:
            for word, label, _, _ in sent_ner:
                # 关键修改：同时检查 'NR' 和 'PERSON' 两种可能的人名标签
                if label in ('PERSON', 'NR') and word not in seen:
                    seen.add(word)
                    out.append(word)
        return out


# ------------------ 拼音转换 (修正后) ------------------
from pypinyin import lazy_pinyin, Style

class PypinyinConverter(PinyinConverter):
    def __init__(self):
        self.lazy = lazy_pinyin
        self.style = Style.NORMAL

    def convert(self, name: str) -> str:
        # 姓、名切分逻辑是正确的，无需改动
        for s in _ALL_SURNAMES:
            if name.startswith(s):
                surname, given = s, name[len(s):]
                break
        else:
            surname, given = name[0], name[1:]

        # --- Bug修复在此 ---
        # 对于姓氏，无论是单姓还是复姓，都应该将所有字的拼音连接起来
        surname_py = _SURNAME_PINYIN.get(surname, "".join(self.lazy(surname, style=self.style)))

        # 名的处理逻辑保持不变
        given_py = "".join(self.lazy(given, style=self.style)) if given else ""

        # 使用 f-string 简化字符串格式化
        return f"{given_py.capitalize()} {surname_py.capitalize()}".strip()


# ------------------ 主翻译器 (已按要求修改) ------------------
class NameTranslator:
    def __init__(self, ner: NER, converter: PinyinConverter):
        self.ner = ner
        self.conv = converter
        # 在初始化时捕获一次加载时间
        self._initial_load_time = self.ner.__class__.load_time
        # 用于跟踪加载时间是否已被报告
        self._load_time_reported = False

    def translate(self, text: str) -> Dict:
        # --- 关键修改开始 ---
        # 根据是否已报告过来决定本次调用要显示的加载时间
        if not self._load_time_reported:
            load_time_to_report = self._initial_load_time
            self._load_time_reported = True # 标记为已报告
        else:
            load_time_to_report = 0.0 # 后续调用均报告为0
        # --- 关键修改结束 ---

        tic = time.perf_counter()
        persons = self.ner.extract_person(text)
        if not persons: # 优化 5: 如果没有识别到人名，提前返回
            total_time = time.perf_counter() - tic
            return {
                "text": text,
                "mapping": {},
                "timing": {
                    "load_model_s": round(load_time_to_report, 4),
                    "ner_s": round(total_time, 4),
                    "pinyin_s": 0,
                    "replace_s": 0,
                    "total_s": round(total_time, 4),
                }
            }

        t1 = time.perf_counter()
        mapping = {p: self.conv.convert(p) for p in persons}
        t2 = time.perf_counter()

        # 优化 6 (核心): 使用 re.sub 和回调函数进行单次、高效的替换
        sorted_persons = sorted(persons, key=len, reverse=True)
        pattern = re.compile("|".join(map(re.escape, sorted_persons)))

        def get_pinyin(match: re.Match) -> str:
            return mapping[match.group(0)]

        text = pattern.sub(get_pinyin, text)
        text = re.sub(r'、([A-Z][a-z]+(?: [A-Z][a-z]+)?)', r', \1', text)
        t3 = time.perf_counter()

        return {
            "text": text,
            "mapping": mapping,
            "timing": {
                "load_model_s": round(load_time_to_report, 4), # 使用新的变量
                "ner_s": round(t1 - tic, 4),
                "pinyin_s": round(t2 - t1, 4),
                "replace_s": round(t3 - t2, 4),
                "total_s": round(t3 - tic, 4),
            }
        }


# ------------------ CLI ------------------
if __name__ == "__main__":
    demo = (
        "供试品管理人员：刘双双。\n"
        "供试品配制人员：张敏、王怡婷、朱凡、曾志伟。"
    )
    # 推荐的实践方式：初始化一次，多次使用
    print("正在加载模型...")
    # translator = NameTranslator(Hanlp2NER(), PypinyinConverter())
    translator = NameTranslator(SpacyNER(), PypinyinConverter())
    # 直接从实例的私有变量读取初始加载时间并打印
    print(f"模型加载耗时: {translator._initial_load_time:.4f} 秒\n")

    print("--- 第一次罗马化 ---")
    res = translator.translate(demo)
    print(res["text"], "\n")
    print("耗时(秒):", res["timing"])

    print("\n--- 第二次罗马化 (无模型加载时间) ---")
    demo2 = "项目负责人是欧阳娜娜，协助人员是单于先生和皇甫先生。"
    res2 = translator.translate(demo2)
    print(res2["text"], "\n")
    print("耗时(秒):", res2["timing"])