import requests
import json
import time
from typing import List
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from logger.logger import biz_logger


class SmallLLMClient:
    def __init__(self):
        self.base_url = "https://f-dev.yiya-ai.com/v1/workflows/run"
        self.auth_token = "app-7SBEg21uxyuabI7WjC6Z0l1o"
        self.workflow_id = "05e56425-59d3-49be-a9a3-a963ce1c34a8"
        self.timeout = 120  # 请求超时时间（秒）
        self.max_workers = 10  # 最大并发线程数
    
    def _make_single_request(self, texts: List[str], src_lang: str, tgt_lang: str, 
                           terminologies: str = "", tb_ids: List[str] = None) -> List[str]:
        """
        发送单个请求到小参数大模型接口
        """
        if not texts:
            return []
        
        biz_logger.info(f"小参数大模型翻译请求：{src_lang}→{tgt_lang}，文本数量：{len(texts)}")
        if terminologies:
            biz_logger.info(f"术语字符串：{terminologies}")
        
        # 构建请求体
        request_body = {
            "mode": "1",
            "reference": "",
            "src_lang": src_lang,
            "src_texts": texts,
            "tbIds": tb_ids or [],
            "terminologies": terminologies,
            "tgt_lang": tgt_lang
        }
        
        payload = {
            "inputs": {
                "request_body": json.dumps(request_body, ensure_ascii=False)
            },
            "response_mode": "blocking",
            "user": "abc-123"
        }
        
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
        
        # 详细记录请求信息
        biz_logger.info(f"小参数大模型请求详情：")
        biz_logger.info(f"  URL: {self.base_url}")
        biz_logger.info(f"  Headers: {headers}")
        biz_logger.info(f"  Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        biz_logger.info(f"  Request Body: {json.dumps(request_body, ensure_ascii=False, indent=2)}")
        
        try:
            start_time = time.time()
            response = requests.post(
                self.base_url,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            end_time = time.time()
            
            biz_logger.info(f"小参数大模型响应详情：")
            biz_logger.info(f"  状态码: {response.status_code}")
            biz_logger.info(f"  响应时间: {(end_time - start_time)*1000:.2f}ms")
            biz_logger.info(f"  响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    biz_logger.info(f"小参数大模型翻译成功，响应内容：{json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    # 提取翻译结果
                    if (result.get("data", {}).get("status") == "succeeded" and 
                        "outputs" in result.get("data", {}) and 
                        "tgt_texts" in result["data"]["outputs"]):
                        
                        translated_texts = result["data"]["outputs"]["tgt_texts"]
                        biz_logger.info(f"小参数大模型翻译完成，结果数量：{len(translated_texts)}")
                        biz_logger.info(f"翻译结果: {translated_texts}")
                        return translated_texts
                    else:
                        error_msg = f"小参数大模型翻译失败，响应状态异常：{result.get('data', {}).get('status', 'unknown')}"
                        if result.get('data', {}).get('error'):
                            error_msg += f"，错误详情: {result['data']['error']}"
                        biz_logger.error(error_msg)
                        biz_logger.error(f"完整响应：{json.dumps(result, ensure_ascii=False, indent=2)}")
                        raise Exception(error_msg)
                except json.JSONDecodeError as e:
                    biz_logger.error(f"小参数大模型响应JSON解析失败：{e}")
                    biz_logger.error(f"原始响应内容：{response.text}")
                    raise Exception(f"小参数大模型响应JSON解析失败：{e}")
            else:
                biz_logger.error(f"小参数大模型翻译请求失败，状态码：{response.status_code}")
                biz_logger.error(f"错误响应内容：{response.text}")
                raise Exception(f"小参数大模型翻译请求失败，状态码：{response.status_code}")
                        
        except requests.exceptions.Timeout:
            biz_logger.error(f"小参数大模型翻译请求超时（{self.timeout}秒）")
            raise Exception(f"小参数大模型翻译请求超时（{self.timeout}秒）")
        except requests.exceptions.RequestException as e:
            biz_logger.error(f"小参数大模型翻译网络请求异常：{e}")
            raise Exception(f"小参数大模型翻译网络请求异常：{e}")
        except Exception as e:
            biz_logger.error(f"小参数大模型翻译异常：{e}")
            raise Exception(f"小参数大模型翻译异常：{e}")
    
    def translate_batch(self, texts: List[str], src_lang: str, tgt_lang: str, 
                       terminologies: str = "", tb_ids: List[str] = None) -> List[str]:
        """
        并发调用小参数大模型接口进行翻译
        """
        if not texts:
            return []
        
        biz_logger.info(f"开始并发翻译，总文本数量：{len(texts)}")
        
        # 如果文本数量较少，直接发送单个请求
        if len(texts) <= 8:
            return self._make_single_request(texts, src_lang, tgt_lang, terminologies, tb_ids)
        
        # 分批处理，每批最多8个文本
        batch_size = 8
        batches = [texts[i:i + batch_size] for i in range(0, len(texts), batch_size)]
        biz_logger.info(f"将文本分为 {len(batches)} 批，每批最多 {batch_size} 个文本")
        
        all_results = []
        
        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有批次的任务
            future_to_batch = {
                executor.submit(self._make_single_request, batch, src_lang, tgt_lang, terminologies, tb_ids): i 
                for i, batch in enumerate(batches)
            }
            
            # 收集结果
            batch_results = [None] * len(batches)
            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    batch_result = future.result()
                    batch_results[batch_idx] = batch_result
                    biz_logger.info(f"批次 {batch_idx + 1} 完成，结果数量：{len(batch_result)}")
                except Exception as e:
                    biz_logger.error(f"批次 {batch_idx + 1} 处理失败：{e}")
                    # 失败时抛出异常，不返回原文
                    raise Exception(f"批次 {batch_idx + 1} 处理失败：{e}")
            
            # 合并所有批次的结果
            for batch_result in batch_results:
                if batch_result:
                    all_results.extend(batch_result)
        
        biz_logger.info(f"并发翻译完成，总结果数量：{len(all_results)}")
        return all_results
    
    def translate_batch_sync(self, texts: List[str], src_lang: str, tgt_lang: str, 
                           terminologies: str = "", tb_ids: List[str] = None) -> List[str]:
        """
        同步调用小参数大模型接口进行翻译（兼容性方法）
        """
        return self.translate_batch(texts, src_lang, tgt_lang, terminologies, tb_ids)


# 全局实例
small_llm_client = SmallLLMClient()
