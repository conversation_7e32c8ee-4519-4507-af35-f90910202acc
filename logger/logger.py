# logger_config.py
# -*- coding: utf-8 -*-

import os
import sys
import contextvars
from loguru import logger
from config.env_config import LOG_HOME

# 全局日志级别，只写 WARNING 及以上 DEBUG
log_level = 'WARNING'

# 请求 ID，用于 trace
request_id = contextvars.ContextVar('request_id', default='')

def get_request_id() -> str:
    return request_id.get()

def conf_logger():
    # 1) 移除 Loguru 自带的默认 handler，避免输出 DEBUG/INFO 到控制台
    logger.remove()

    # 2) 控制台日志输出（终端），可以用enqueue，但通常没必要
    # enqueue=True 会将日志写入放到单独的线程/进程队列，适合多进程/多线程写文件时防止竞争，终端输出一般不需要
    # 但如果你想统一用enqueue也可以，没问题
    logger.add(
        sys.stderr,
        level=log_level,
        format=(
            "{time:MM-DD HH:mm:ss} | {level} | {extra[request_id]} | "
            "{module}.{function}:{line} | {message}"
        ),
        # 通常终端输出不需要enqueue，但加上也没问题
        enqueue=True,
        filter=lambda record: record["level"].name != "INFO"
    )

    # 3) 添加 app.log，只记录带 log_tag='app' 的 WARNING+ 日志
    logger.add(
        os.path.join(LOG_HOME, "app.log"),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {extra[request_id]} | {message}",
        level=log_level,
        rotation="00:00",
        retention="30 days",
        enqueue=True,
        encoding="utf-8",
        filter=lambda record: record["extra"].get("log_tag") == "app" and record["level"].name != "INFO"
    )

    # 4) 添加 biz.log，只记录带 log_tag='biz' 的 WARNING+ 日志
    logger.add(
        os.path.join(LOG_HOME, "biz.log"),
        format="\u0001".join([
            "{time:YYYY-MM-DD HH:mm:ss}",
            "{level}",
            "{extra[request_id]}",
            "{message}"
        ]),
        level=log_level,
        rotation="00:00",
        retention="30 days",
        enqueue=True,
        encoding="utf-8",
        # filter=lambda record: record["extra"].get("log_tag") == "biz"
        filter=lambda record: record["extra"].get("log_tag") == "biz" and record["level"].name != "INFO"
    )

    # 5) 绑定 log_tag，并在每条记录里注入当前 request_id
    app_logger = logger.bind(log_tag="app") \
                       .patch(lambda record: record["extra"].update(request_id=get_request_id()))
    biz_logger = logger.bind(log_tag="biz") \
                       .patch(lambda record: record["extra"].update(request_id=get_request_id()))

    return app_logger, biz_logger

# 在模块导入时即配置好
app_logger, biz_logger = conf_logger()

if __name__ == "__main__":
    app_logger.warning("This is a WARNING in app.log only")
    app_logger.info("This INFO will NOT appear anywhere")
    biz_logger.error("This is an ERROR in biz.log only")
