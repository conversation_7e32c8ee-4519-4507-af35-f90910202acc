# util/name_direct.py
import re
from typing import List, Dict
from configurer.config_reader import get_name_list

try:
    from pypinyin import lazy_pinyin, Style
except ImportError:
    print("Warning: pypinyin not installed. Please install with: pip install pypinyin")
    lazy_pinyin = None

# 本地硬编码的姓名列表
LOCAL_NAMES = ["李明", "林小玉"]

# 线上配置姓名列表
online_names = get_name_list() or []
ALL_NAMES = sorted(set(online_names + LOCAL_NAMES), key=len, reverse=True)

# 构建姓氏集合，用于快速判断是否需要处理
SURNAME_SET = set(name[0] for name in ALL_NAMES if name)

# 姓名匹配正则
escaped = [re.escape(name) for name in ALL_NAMES]
NAME_REGEX = re.compile(r"(?:%s)" % "|".join(escaped))

def chinese_to_pinyin(chinese_name: str) -> str:
    """
    将中文姓名转换为拼音格式：名在前 姓在后
    例如：林小玉 -> <PERSON><PERSON> <PERSON>
    """
    if not lazy_pinyin:
        return f"[{chinese_name}]"
    if len(chinese_name) < 2:
        return chinese_name

    surname = chinese_name[0]
    given   = chinese_name[1:]

    # 姓氏全拼，首字母大写
    surname_py = lazy_pinyin(surname, style=Style.NORMAL)[0].capitalize()

    # 名字全拼，先 join 再整体 capitalize
    given_list = lazy_pinyin(given, style=Style.NORMAL)
    given_py   = "".join(given_list).capitalize()

    return f"{given_py} {surname_py}"

class NameReplacer:
    def __init__(self):
        pass

    def replace_with_pinyin(self, text: str) -> str:
        """
        直接将所有匹配到的中文姓名替换为拼音（名-姓 顺序）。
        """
        # 如果句子里没有已知姓氏，就跳过
        if not any(ch in SURNAME_SET for ch in text):
            return text

        def _repl(m: re.Match) -> str:
            name = m.group(0)
            return chinese_to_pinyin(name)

        return NAME_REGEX.sub(_repl, text)

# 测试
if __name__ == "__main__":
    replacer = NameReplacer()
    samples = [
        "今天天气很好，无需处理。",
        "今天林小玉和李明一起去散步。",
        "李明、王伟和赵敏都来了。",
    ]
    for s in samples:
        replaced = replacer.replace_with_pinyin(s)
        print(f"原文：{s}")
        print(f"处理后：{replaced}\n")
