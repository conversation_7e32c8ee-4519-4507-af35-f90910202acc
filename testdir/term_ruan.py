import os
from typing import List, Any
import ctranslate2
from transformers import AutoTokenizer

class Ctranslate2TranslatorSoft:
    """
    软干预方案：
      1. 通过 add_special_tokens 将所有术语作为特殊 Token 添加到分词器中，
         从而确保模型在分词和解码时原样保留；
      2. 翻译时采用相似的超参配置以防重复及漏译。
    """
    def __init__(self, model_path: str, token_path: str,
                 src_lang: str, tgt_lang: str,
                 beam_size: int, max_decoding_length: int,
                 compute_type: str = "auto", device: str = "cuda", device_index: int = 0,
                 special_tokens: List[str] = None):
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self._infer_engine = ctranslate2.Translator(
            model_path, compute_type=compute_type, device=device, device_index=device_index
        )
        self._tokenizer = AutoTokenizer.from_pretrained(token_path)
        if special_tokens:
            # 添加特殊 Token（确保这些术语不会被拆分）
            self._tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})
        self._target_lang_tokens = [self._tokenizer.lang_code_to_token[tgt_lang]]
        self._beam_size = beam_size
        self._max_decoding_length = max_decoding_length

    def tokenize(self, sequences: List[str]) -> List[List[str]]:
        return [self._tokenizer.convert_ids_to_tokens(self._tokenizer.encode(seq))
                for seq in sequences]

    def detokenize(self, tokens_list: List[Any]) -> List[str]:
        return [self._tokenizer.decode(self._tokenizer.convert_tokens_to_ids(result.hypotheses[0][1:]))
                for result in tokens_list]

    def translate(self, tokens_list: List[List[str]]):
        tgt_tokens_list = self._infer_engine.translate_batch(
            tokens_list,
            target_prefix=[self._target_lang_tokens] * len(tokens_list),
            beam_size=self._beam_size,
            max_decoding_length=self._max_decoding_length,
            repetition_penalty=1.1,
            length_penalty=1.2,
            coverage_penalty=0.1,
            no_repeat_ngram_size=3
        )
        return tgt_tokens_list

if __name__ == "__main__":
    model_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_quant_2025021722"
    token_path = "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_origin_2025021722"
    src_lang = "zh"
    tgt_lang = "en"
    
    # 定义特殊 Token 列表，直接使用目标术语（无需做硬替换）
    special_tokens = [
        "MACCE", "不良事件", "严重不良事件",
        "试验组", "对照组",
        "试验产品DissolveTM冠脉药物球囊扩张导管",
        "冠状动脉支架内再狭窄"
    ]
    
    texts = [
        "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
        "本申请要求于2023年2月17日递交的中国专利申请第202310153996.9号的优先权，在此全文引用上述中国专利申请公开的内容以作为本申请的一部分。",
        "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；",
        "试验组共记录103例（80.5%）不良事件，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。",
        "术后1、6、9、12个月试验组MACCE发生分别为1例（0.8%）、11例（8.6%）、36例（28.3%）、45例（35.4%），对照组MACCE发生分别为5例（3.8%）、11例（8.4%）、30例（22.9%）、33例（25.4%），以上结果显示，试验组和对照组术后12个月MACCE发生率无统计学显著的差异（35.4% vs 25.4%, p=0.0794）；试验组共记录103例（80.5%）不良事件，对照组共记录99例（75.0%）不良事件（p=0.2889），试验组和对照组不良事件发生率无统计学差异；本试验过程中试验组共记录52例（40.6%）严重不良事件，对照组共记录42例（31.8%）严重不良事件（p=0.1392），试验组和对照组严重不良事件发生率无统计学差异；以上结果证明了试验产品DissolveTM冠脉药物球囊扩张导管治疗冠状动脉支架内再狭窄的安全性。"
    ]
    
    translator = Ctranslate2TranslatorSoft(
        model_path=model_path,
        token_path=token_path,
        src_lang=src_lang,
        tgt_lang=tgt_lang,
        beam_size=5,
        max_decoding_length=512,
        device="cuda",
        special_tokens=special_tokens
    )
    
    tokens = translator.tokenize(texts)
    translated_tokens = translator.translate(tokens)
    results = translator.detokenize(translated_tokens)
    
    for i, (src, res) in enumerate(zip(texts, results), start=1):
        print(f"【软干预方案】\n源文本 {i}: {src}")
        print(f"翻译结果 {i}: {res}")
        print("-" * 50)
