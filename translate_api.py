# translate_api.py
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import torch
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor

from configurer.config_reader import project_config
from util.sent_split import split
from util.langident import Langident
from util.non_translation import NonTranslation
from util.name import NameReplacer
from util.name_direct import NameReplacer as NameReplacerDirect
from util.rule_translator import RuleTranslator
from util.substring_no_trans import mask_substring_no_trans, restore_substring_no_trans
from util.punctuation_replacer import replace_punctuation
from inference.translator import Translator
from llm.revision import replace_unk_tokens
from aop.context import get_disable_llm_correction
from inference.engine.profiler import TimeProfiler
from logger.logger import biz_logger
from configurer.singleton import singleton

device = torch.device("cuda" if torch.cuda.device_count() > 0 else "cpu")
MAX_WORKERS = project_config.get('translation', {}).get('max_workers', 256)
BATCH_SIZE  = project_config.get('translation', {}).get('max_batch_size', 30)


@singleton
class TranslateModel:
    """
    单例翻译模型封装：初始化 Translator 与线程池，并行调用 Translator.translate()
    """
    def __init__(self):
        cur = os.path.abspath(os.path.dirname(__file__))
        model_path = os.environ.get('MODEL_PATH', f"{cur}/model/")
        token_path = os.environ.get('TOKEN_PATH', f"{cur}/token/")
        src_lang   = os.environ.get('SOURCE_LANG', 'zh')
        tgt_lang   = os.environ.get('TARGET_LANG', 'en')
        max_length = int(os.environ.get('MAX_LENGTH', 512))
        device_index = int(os.environ.get('DEVICE_INDEX', 0))
        beam_num = int(os.environ.get('BEAM_NUM', 4))
        compute_type = os.environ.get('COMPUTE_TYPE', 'int8_float16')

        # biz_logger.info(f"模型路径: {model_path}")
        # biz_logger.info(f"词表路径: {token_path}")
        # biz_logger.info(f'最大长度: {max_length}')
        
        self.translator = Translator(
            model_path=model_path,
            token_path=token_path,
            src_lang=src_lang,
            tgt_lang=tgt_lang,
            beam_num=beam_num,
            max_input_length=max_length,
            max_decoding_length=max_length,
            compute_type=compute_type,
            device=str(device),
            device_index=device_index,
        )
        self.pool = ThreadPoolExecutor(max_workers=MAX_WORKERS)

        # 预热
        warm = project_config.get('warm_text', {}).get(src_lang, [])
        if warm:
            try:
                biz_logger.info(f"开始模型预热...")
                self.translator.translate(warm)
                biz_logger.info(f"模型预热完成")
            except Exception:
                biz_logger.error(f"模型预热失败")
                pass

    def translate(self, sents: List[str], src_lang=None, tgt_lang=None) -> List[str]:
        disable_corr = get_disable_llm_correction()
        results = []
        biz_logger.info(f"开始批量翻译，共{len(sents)}条文本")
        with TimeProfiler(title="批量翻译计时：", desc=f"条目数:{len(sents)}"):
            futures = [self.pool.submit(self.translator.translate, [t]) for t in sents]
            for idx, fut in enumerate(futures):
                try:
                    out, status = fut.result()
                    if hasattr(status,'code') and status.code!=0:
                        biz_logger.error(f"翻译失败，错误码={status.code}")
                        results.append("")
                        continue
                    first = out[0] if out else ""
                    cand  = first[0] if isinstance(first,(list,tuple)) else first
                    if not disable_corr:
                        biz_logger.info(f"对第{idx}条文本进行LLM校正")
                        cand = replace_unk_tokens(sents[idx], cand)
                    results.append(cand)
                except Exception as e:
                    biz_logger.error(f"子句翻译异常: {e}")
                    results.append("")
        biz_logger.info(f"批量翻译完成，共处理{len(sents)}条文本")
        return results


class TranslateAPI:
    """
    对外暴露 translate(...) 接口：
      输入 src_text_list, src_lang, tgt_lang
      返回保留原有换行结构的译文列表
    """
    def __init__(self):
        self.model = TranslateModel()
        self.ld    = Langident()
        self.nt    = NonTranslation()
        self.rt    = RuleTranslator()    # ← 新增规则翻译器

    def _preprocess(self, src_list, src_lang, tgt_lang):
        """
        将 src_list 按行 & 子句拆扁平，标记跳过、记录行区间：
        返回 flat_sents, skip_flags, mapping
        """
        biz_logger.info(f"开始预处理，源语言:{src_lang}，目标语言:{tgt_lang}，文本数量:{len(src_list)}")
        flat_sents = []
        skip_flags = []
        mapping    = []
        for text_idx, text in enumerate(src_list):
            line_intv = []
            for line_idx, line in enumerate(text.splitlines()):
                line = line.strip()
                if not line:
                    # 空行
                    biz_logger.info(f"文本{text_idx+1}的第{line_idx+1}行为空行，跳过处理")
                    line_intv.append((len(flat_sents), len(flat_sents)))
                    continue

                # 如果整行视为"非译元素"，直接跳过模型
                if self.nt.is_non_translatable(line,src_lang,tgt_lang,self.ld):
                    biz_logger.info(f"文本{text_idx+1}的第{line_idx+1}行被识别为非译元素，将跳过翻译")
                    start = len(flat_sents)
                    flat_sents.append(line)
                    skip_flags.append(True)
                    line_intv.append((start, start+1))
                    continue

                # # —— 新增：先尝试规则翻译 —— #
                # # —— 修正：传入 src_lang, tgt_lang —— #
                # mapped = self.rt.translate(line, src_lang, tgt_lang)
                # if mapped is not None:
                #     biz_logger.debug(f"文本{text_idx+1}的第{line_idx+1}行使用规则翻译")
                #     start = len(flat_sents)
                #     flat_sents.append(mapped)
                #     skip_flags.append(True)               # 跳过模型
                #     line_intv.append((start, start+1))
                #     continue

                # 否则按子句拆
                biz_logger.info(f"文本{text_idx+1}的第{line_idx+1}行进行子句拆分")
                start = len(flat_sents)
                for sub, pre in split(line, src_lang):
                    text_piece = pre if pre is not None else sub
                    flat_sents.append(text_piece)
                    skip_flags.append(False)
                line_intv.append((start, len(flat_sents)))

            mapping.append(line_intv)
        biz_logger.info(f"预处理完成，共拆分出{len(flat_sents)}个子句，其中{sum(skip_flags)}个子句将跳过翻译")
        return flat_sents, skip_flags, mapping

    def _translate_batch(self, flats, skips, src_lang, tgt_lang):
        """
        只对 skips=False 的 flats 送入模型，True 的回原文
        """
        biz_logger.info(f"开始批量翻译处理，源语言:{src_lang}，目标语言:{tgt_lang}")
        to_trans   = []
        trans_idx  = []
        for i, (txt, skip) in enumerate(zip(flats, skips)):
            if not skip:
                trans_idx.append(i)
                to_trans.append(txt)

        # 翻译
        outs = []
        if to_trans:
            biz_logger.info(f"需要翻译的子句数量:{len(to_trans)}，批次大小:{BATCH_SIZE}")
            for i in range(0, len(to_trans), BATCH_SIZE):
                batch = to_trans[i:i+BATCH_SIZE]
                biz_logger.info(f"处理第{i//BATCH_SIZE+1}批，包含{len(batch)}个子句")
                batch_results = self.model.translate(batch, src_lang, tgt_lang)
                outs.extend(batch_results)
            
        # 合并
        flat_res = []
        ptr = 0
        for idx, (orig, skip) in enumerate(zip(flats, skips)):
            if skip:
                # 整句跳过或规则翻译时，不做标点变化
                flat_res.append(orig)
            else:
                # 子句翻译结果先做标点，再在最外层恢复子串
                raw = outs[ptr]
                ptr += 1
                biz_logger.info(f"对子句{idx+1}进行标点处理")
                formatted = replace_punctuation(orig, raw, src_lang, tgt_lang)
                flat_res.append(formatted)

        biz_logger.info(f"批量翻译处理完成，共处理{len(flats)}个子句")
        return flat_res

    def _reassemble(self, flat_res, mapping, tgt_lang):
        """
        根据 mapping 将 flat_res 按行拼回，行间以\n连接
        """
        biz_logger.info(f"开始重组翻译结果，目标语言:{tgt_lang}")
        final = []
        for map_idx, line_intv in enumerate(mapping):
            lines = []
            for s,e in line_intv:
                # biz_logger.info(f's: {s}, e: {e}')
                seg = flat_res[s:e]
                if tgt_lang in ('zh','zhtw','ja'):
                    lines.append(''.join(seg).strip())
                else:
                    lines.append(' '.join(seg).replace('  ',' ').strip())
            final.append("\n".join(lines))
        biz_logger.info(f"翻译结果重组完成，共生成{len(final)}个文本")
        return final

    def translate(self, src_list, src_lang, tgt_lang):
        biz_logger.info(f"开始翻译流程，源语言:{src_lang}，目标语言:{tgt_lang}，文本数量:{len(src_list)}")
        # ---- 0. 先全局屏蔽一次所有"非译子串" ----
        biz_logger.debug(f"开始非译子串屏蔽")
        masked_nt_list: List[str] = []
        nt_mappings: List[Dict[str,str]] = []
        for txt_idx, txt in enumerate(src_list):
            masked, mapping = mask_substring_no_trans(txt, src_lang, tgt_lang)
            masked_nt_list.append(masked)
            nt_mappings.append(mapping)
            biz_logger.debug(f"  [{txt_idx}]: {masked}")
        biz_logger.info(f"非译子串屏蔽完成")

        # ---- 0.5 直接将中文姓名替换为拼音 ----
        biz_logger.debug("开始将中文姓名替换为拼音")
        masked_name_list: List[str] = []
        for idx, txt in enumerate(masked_nt_list):
            # NameReplacerDirect.replace_with_pinyin 会把 ALL_NAMES 列表中的中文姓名
            # 直接换成 “名在前 姓在后” 的拼音
            mtxt = NameReplacerDirect().replace_with_pinyin(txt)
            masked_name_list.append(mtxt)
            biz_logger.debug(f"  [{idx}]: {mtxt}")
        biz_logger.info("中文姓名拼音替换完成")


        # ---- 1. 原来的 pre-process / batch / reassemble ----
        # biz_logger.debug(f"预处理输入（屏蔽后的文本）：")
        # for idx, masked_text in enumerate(masked_name_list):
        #     biz_logger.debug(f"  [{idx}]: {masked_text}")
        
        flats, skips, mapping_info = self._preprocess(masked_name_list, src_lang, tgt_lang)
        biz_logger.debug(f"预处理输出（扁平化子句）：")
        for idx, flat in enumerate(flats):
            biz_logger.debug(f"  [{idx}]: {flat} (skip: {skips[idx]})")
        
        flat_res = self._translate_batch(flats, skips, src_lang, tgt_lang)
        biz_logger.debug(f"批量翻译输出（扁平化结果）：")
        for idx, result in enumerate(flat_res):
            biz_logger.debug(f"  [{idx}]: {result}")
        
        assembled = self._reassemble(flat_res, mapping_info, tgt_lang)
        biz_logger.debug(f"重组输出（最终翻译结果）：")
        for idx, result in enumerate(assembled):
            biz_logger.debug(f"  [{idx}]: {result}")

        # ---- 2. 最后再把占位符还原 ----
        biz_logger.debug(f"开始还原非译子串")
        restored_nt: List[str] = []
        for idx, (line, mapping) in enumerate(zip(assembled, nt_mappings)):
            result = restore_substring_no_trans(line, mapping, src_lang, tgt_lang)
            restored_nt.append(result)
            biz_logger.debug(f"  [{idx}]: {result}")

        # # ---- 3. 还原中文姓名拼音 ----
        # biz_logger.debug("开始还原中文姓名拼音")
        # final_out: List[str] = []
        # for idx, (line, replacer) in enumerate(zip(restored_nt, name_replacers)):
        #     result = replacer.restore_with_pinyin(line)
        #     final_out.append(result)
        #     biz_logger.debug(f"  [{idx}]: {result}")


        return restored_nt


if __name__ == '__main__':
    # 一个 batch 里混合多种情况的示例
    texts = [
        # 1. 纯中文长句 → 翻译
        "4)春节期间以旧换新活动成果显著，手机成为今年春节换新潮主力，"
        "国补政策扩容到 3C数码产品后显著刺激了手机销量增长。",
        # 2. 多行 AE 列表（全英文首字母+中文）→ 逐行翻译、保留换行
        "AE 免疫性肠炎\nAE 尿蛋白阳性\nAE 尿蛋白阳性 1级\n"
        "AE 尿蛋自阳性 1级结束\nAE 尿蛋白阳性1级结束\n"
        "AE 凝血酶原时间延长\nAE 贫血\nAE 贫血加重",
        # 3. 多行纯中文人员列表 → 按行翻译
        "供试品管理人员：刘双双\n供试品配制人员：张敏、王怡婷、朱凡、周颖琪、张恬、张瑜\n"
        "供试品分析：胡晓、王超超、陈泽政、张伟伟\n张敏\n王怡婷\n朱凡",
        # 4. 纯英文大块摘要（非译元素） → skip=True，直接原文返回
        "Abstract：Objective   To establish the national standard materials of testosterone "
        "in frozen human serum， ... Conclusions The homogeneity，stability and commutability "
        "of candidate material are good，and their values are assigned accurately and reliably. "
        "They can be used as national standard."
    ]
    api = TranslateAPI()
    outs = api.translate(texts, 'zh', 'en')
    for src, tgt in zip(texts, outs):
        print("-----")
        print("原文：", src)
        print("译文：", tgt)
