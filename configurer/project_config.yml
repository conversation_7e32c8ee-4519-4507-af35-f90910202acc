# config/project_config.yml

# --------------------------
# 环境与设备配置
# --------------------------
environment:
  # Conda 环境名称（可选，用于部署环境管理）
  conda_env: mt_server_py310
  # 设置 CUDA 可见设备（这里只让系统看到目标 GPU，例如 7）
  cuda_visible_devices: "7"
  # 默认使用的设备（当 GPU 可用时推荐使用 "cuda"）
  device: "cuda"
  # 如果在 transformers 或 CTranslate2 模型中需要指定设备编号（注意：当设置了 CUDA_VISIBLE_DEVICES 后，代码中通常使用 0 即代表唯一可见 GPU）
  device_index: 7

# --------------------------
# 模型配置
# --------------------------
models:
  zh2en:
    model_path: "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_quant_2025021722"
    tokenizer_path: "/home/<USER>/data/models/trained/zh2en/m2m-1.2b-31/m2m100_1.2b_zh2en_origin_2025021722"
  en2zh:
    model_path: "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-31/m2m100_1.2b_en2zh_quant_2025020617"
    tokenizer_path: "/home/<USER>/data/models/trained/en2zh/m2m-1.2b-31/m2m100_1.2b_en2zh_origin_2025020617"

# --------------------------
# 语言配置
# --------------------------
languages:
  source: "zh"  # 源语言
  target: "en"  # 目标语言

# --------------------------
# 翻译任务配置
# --------------------------
translation:
  beam_num: 5                 # 束搜索宽度
  max_decoding_length: 512      # 最大解码长度
  max_batch_size: 30           # 每批处理最大样本数
  batch_wait_timeout_s: 0.1     # 批任务等待时间（秒）
  timeout: 120                  # 翻译请求超时时间（秒）
  exec_batchs_per_s: 30         # 每秒处理请求数
  max_workers: 256              # 最大并发数
  special_beam_selection:
    - pattern: "患者因“1.肺原位癌2.胸膜钙化”于2023.4.4来我院住院治疗"
      beam_index: 4            # 选择第5个候选译文（索引 4）
    - pattern: "不使用beam1"     # 例如：如果包含“不使用beam1”，选择第二个候选
      beam_index: 1
    - pattern: "客户要求特殊处理"
      beam_index: 2


# --------------------------
# API 配置
# --------------------------
api:
  zh2en:
    host: "0.0.0.0"
    port: 7860
    workers: 1
    worker_connections: 2000       # 每个 worker 的连接数（如用于 gunicorn 配置）
  en2zh:
    host: "0.0.0.0"
    port: 7861
    workers: 1
    worker_connections: 2000       # 每个 worker 的连接数（如用于 gunicorn 配置）

# --------------------------
# 日志配置
# --------------------------
logging:
  level: "DEBUG"              # 日志级别：INFO, WARNING, DEBUG 等
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# --------------------------
# 术语替换配置
# --------------------------
terminology:
  placeholder_format: "<__TERM{index:03d}>"  # 生成占位符的格式（必须包含 {index} 占位符）
  placeholder_pattern: "<_+TERM\\d{3}>"         # 用于查找占位符的正则表达式

# --------------------------
# 分句 (Sentence Splitting) 配置
# --------------------------
sent_split:
  en:
    split_length: 100        # 当文本短于该长度时不进行分句
    max_length: 100          # 分句后每个句子的最大字符数
    min_length: 15           # 合并短句时的最小字符数
    punctuations: [".", "!", "?", ","]   # 英文分句时常用标点
  zh:
    split_length: 100
    max_length: 100
    min_length: 15
    punctuations: ["。", "！", "？"]       # 中文分句标点

# --------------------------
# 数据管理配置
# --------------------------
data:
  batch_size: 30                # 每批翻译样本数
  max_num: 100                  # 最大样本数（示例参数）
  min_num: 15                   # 最小样本数（示例参数）
  max_pool_size: 600            # 线程池最大任务数（通常为 batch_size 的 20 倍）
  data_expired_time: 30         # 数据过期时间（秒）
  max_service_time: 20          # 最大服务时长（秒）
  sleep_time: 0.02              # 任务处理期间休眠时间（秒）


# 预热文本，用于启动时模型预加载（中英文各自预热）
warm_text:
  en: [
    'About what is coronavirus',
    'During the period of liquidation, the partnership enterprise shall survive and shall not carry out business activities that have nothing to do with liquidation.',
    'Article 17 A partner who does not participate in the execution of partnership affairs shall have the right to supervise the execution of partnership affairs by the partner, the managing partner shall regularly report to the other partners the execution of the affairs and the operation and financial status of the partnership, the proceeds of the execution of the partnership firm shall belong to the partnership, and the expenses and losses incurred are borne by the partnership.'
  ]
  zh: [
    '清算期间，合伙企业存续，不得开展与清算无关的经营活动。',
    '合伙企业财产在支付清算费用和职工工资、社会保险费用、法定补偿金以及缴纳所欠税款、清偿债务后的剩余财产，依照第十二条的规定进行分配。',
    '第十七条	不参加执行合伙事务的合伙人有权监督执行事务合伙人执行合伙事务的情况，执行事务合伙人应当定期向其他合伙人报告事务执行情况以及合伙企业的经营和财务状况，其执行合伙事务所产生的收益归合伙企业，所产生的费用和亏损由合伙企业承担。'
  ]

# 常见中文单姓与复姓列表（示例，实际可扩展）
first_name:
  first_name_list:: ['赵', '钱', '孙', '李', '周', '吴', '郑', '王', '冯', '陈', '褚', '卫', '蒋', '沈', '韩', '杨', '朱', '秦', '尤', '许', '何', '吕', '施', '张', '孔', '曹', '严', '华', '金', '魏', '陶', '姜', '戚', '谢', '邹', '喻', '柏', '水', '窦', '章', '云', '苏', '潘', '葛', '奚', '范', '彭', '郎', '鲁', '韦', '昌', '马', '苗', '凤', '花', '方', '俞', '任', '袁', '柳', '酆', '鲍', '史', '唐', '费', '廉', '岑', '薛', '雷', '贺', '倪', '汤', '滕', '殷', '罗', '毕', '郝', '邬', '安', '常', '乐', '于', '时', '傅', '皮', '卞', '齐', '康', '伍', '余', '元', '卜', '顾', '孟', '平', '黄', '和', '穆', '萧', '尹', '姚', '邵', '湛', '汪', '祁', '毛', '禹', '狄', '米', '贝', '明', '臧', '计', '伏', '成', '戴', '谈', '宋', '茅', '庞', '熊', '纪', '舒', '屈', '项', '祝', '董', '梁', '杜', '阮', '蓝', '闵', '席', '季', '麻', '强', '贾', '路', '娄', '危', '江', '童', '颜', '郭', '梅', '盛', '林', '刁', '锺', '徐', '邱', '骆', '高', '夏', '蔡', '田', '樊', '胡', '凌', '霍', '虞', '万', '支', '柯', '昝', '管', '卢', '莫', '经', '房', '裘', '缪', '干', '解', '应', '宗', '丁', '宣', '贲', '邓', '郁', '单', '杭', '洪', '包', '诸', '左', '石', '崔', '吉', '钮', '龚', '程', '嵇', '邢', '滑', '裴', '陆', '荣', '翁', '荀', '羊', '於', '惠', '甄', '麴', '家', '封', '芮', '羿', '储', '靳', '汲', '邴', '糜', '松', '井', '段', '富', '巫', '乌', '焦', '巴', '弓', '牧', '隗', '山', '谷', '车', '侯', '宓', '蓬', '全', '郗', '班', '仰', '秋', '仲', '伊', '宫', '宁', '仇', '栾', '暴', '甘', '钭', '历', '戎', '祖', '武', '符', '刘', '景', '詹', '束', '龙', '叶', '幸', '司', '韶', '郜', '黎', '蓟', '溥', '印', '宿', '白', '怀', '蒲', '邰', '从', '鄂', '索', '咸', '籍', '赖', '卓', '蔺', '屠', '蒙', '池', '乔', '阳', '郁', '胥', '能', '苍', '双', '闻', '莘', '党', '翟', '谭', '贡', '劳', '逄', '姬', '申', '扶', '堵', '冉', '宰', '郦', '雍', '却', '璩', '桑', '桂', '濮', '牛', '寿', '通', '边', '扈', '燕', '冀', '僪', '浦', '尚', '农', '温', '别', '庄', '晏', '柴', '瞿', '阎', '充', '慕', '连', '茹', '习', '宦', '艾', '鱼', '容', '向', '古', '易', '慎', '戈', '廖', '庾', '终', '暨', '居', '衡', '步', '都', '耿', '满', '弘', '匡', '国', '文', '寇', '广', '禄', '阙', '东', '欧', '殳', '沃', '利', '蔚', '越', '夔', '隆', '师', '巩', '厍', '聂', '晁', '勾', '敖', '融', '冷', '訾', '辛', '阚', '那', '简', '饶', '空', '曾', '毋', '沙', '乜', '养', '鞠', '须', '丰', '巢', '关', '蒯', '相', '查', '后', '荆', '红', '游', '竺', '权', '逮', '盍', '益', '桓', '公', '召', '有', '舜', '丛', '岳', '寸', '贰', '皇', '侨', '彤', '竭', '端', '赫', '实', '甫', '集', '象', '翠', '狂', '辟', '典', '良', '函', '芒', '苦', '其', '京', '中', '夕', '之', '冠', '宾', '香', '果', '蹇', '称', '诺', '来', '多', '繁', '戊', '朴', '回', '毓', '税', '荤', '靖', '绪', '愈', '硕', '牢', '买', '但', '巧', '枚', '撒', '泰', '秘', '亥', '绍', '以', '壬', '森', '斋', '释', '奕', '姒', '朋', '求', '羽', '用', '占', '真', '穰', '翦', '闾', '漆', '贵', '代', '贯', '旁', '崇', '栋', '告', '休', '褒', '谏', '锐', '皋', '闳', '在', '歧', '禾', '示', '是', '委', '钊', '频', '嬴', '呼', '大', '威', '昂', '律', '冒', '保', '系', '抄', '定', '化', '莱', '校', '么', '抗', '祢', '綦', '悟', '宏', '功', '庚', '务', '敏', '捷', '拱', '兆', '丑', '丙', '畅', '苟', '随', '类', '卯', '俟', '友', '答', '乙', '允', '甲', '留', '尾', '佼', '玄', '乘', '裔', '延', '植', '环', '矫', '赛', '昔', '侍', '度', '旷', '遇', '偶', '前', '由', '咎', '塞', '敛', '受', '泷', '袭', '衅', '叔', '圣', '御', '夫', '仆', '镇', '藩', '邸', '府', '掌', '首', '员', '焉', '戏', '可', '智', '尔', '凭', '悉', '进', '笃', '厚', '仁', '业', '肇', '资', '合', '仍', '九', '衷', '哀', '刑', '俎', '仵', '圭', '夷', '徭', '蛮', '汗', '孛', '乾', '帖', '罕', '洛', '淦', '洋', '邶', '郸', '郯', '邗', '邛', '剑', '虢', '隋', '蒿', '茆', '菅', '苌', '树', '桐', '锁', '钟', '机', '盘', '铎', '斛', '玉', '线', '针', '箕', '庹', '绳', '磨', '蒉', '瓮', '弭', '刀', '疏', '牵', '浑', '恽', '势', '世', '仝', '同', '蚁', '止', '戢', '睢', '冼', '种', '涂', '肖', '己', '泣', '潜', '卷', '脱', '谬', '蹉', '赧', '浮', '顿', '说', '次', '错', '念', '夙', '斯', '完', '丹', '表', '聊', '源', '姓', '吾', '寻', '展', '出', '不', '户', '闭', '才', '无', '书', '学', '愚', '本', '性', '雪', '霜', '烟', '寒', '少', '字', '桥', '板', '斐', '独', '千', '诗', '嘉', '扬', '善', '揭', '祈', '析', '赤', '紫', '青', '柔', '刚', '奇', '拜', '佛', '陀', '弥', '阿', '素', '长', '僧', '隐', '仙', '隽', '宇', '祭', '酒', '淡', '塔', '琦', '闪', '始', '星', '南', '天', '接', '波', '碧', '速', '禚', '腾', '潮', '镜', '似', '澄', '潭', '謇', '纵', '渠', '奈', '风', '春', '濯', '沐', '茂', '英', '兰', '檀', '藤', '枝', '检', '生', '折', '登', '驹', '骑', '貊', '虎', '肥', '鹿', '雀', '野', '禽', '飞', '节', '宜', '鲜', '粟', '栗', '豆', '帛', '官', '布', '衣', '藏', '宝', '钞', '银', '门', '盈', '庆', '喜', '及', '普', '建', '营', '巨', '望', '希', '道', '载', '声', '漫', '犁', '力', '贸', '勤', '革', '改', '兴', '亓', '睦', '修', '信', '闽', '北', '守', '坚', '勇', '汉', '练', '尉', '士', '旅', '五', '令', '将', '旗', '军', '行', '奉', '敬', '恭', '仪', '母', '堂', '丘', '义', '礼', '慈', '孝', '理', '伦', '卿', '问', '永', '辉', '位', '让', '尧', '依', '犹', '介', '承', '市', '所', '苑', '杞', '剧', '第', '零', '谌', '招', '续', '达', '忻', '六', '鄞', '战', '迟', '候', '宛', '励', '粘', '萨', '邝', '覃', '辜', '初', '楼', '城', '区', '局', '台', '原', '考', '妫', '纳', '泉', '老', '清', '德', '卑', '过', '麦', '曲', '竹', '百', '福', '言', '佟', '爱', '年', '笪', '谯', '哈', '墨', '赏', '伯', '佴', '佘', '牟', '商', '琴', '后', '况', '亢', '缑', '帅', '海', '归', '钦', '鄢', '汝', '法', '闫', '楚', '晋', '督', '仉', '盖', '逯', '库', '郏', '逢', '阴', '薄', '厉', '稽', '开', '光', '操', '瑞', '眭', '泥', '运', '摩', '伟', '铁', '迮']
  common_surnames: ['万俟', '司马', '上官', '欧阳', '夏侯', '诸葛', '闻人', '东方', '赫连', '皇甫', '尉迟', '公羊', '澹台', '公冶', '宗政', '濮阳', '淳于', '单于', '太叔', '申屠', '公孙', '仲孙', '轩辕', '令狐', '钟离', '宇文', '长孙', '慕容', '司徒', '司空', '叶赫那拉', '章佳', '那拉', '依尔根觉罗', '依尔觉罗', '萨嘛喇', '赫舍里', '额尔德特', '萨克达', '钮祜禄', '他塔喇', '喜塔腊', '讷殷富察', '叶赫那兰', '库雅喇', '瓜尔佳', '舒穆禄', '爱新觉罗', '索绰络', '纳喇', '乌雅', '范姜', '碧鲁', '张廖', '张简', '图门', '太史', '公叔', '乌孙', '完颜', '马佳', '佟佳', '富察', '费莫', '第五', '南宫', '西门', '东门', '左丘', '梁丘', '微生', '羊舌', '呼延', '南门', '东郭', '百里', '谷梁', '宰父', '夹谷', '拓跋', '壤驷', '乐正', '漆雕', '公西', '巫马', '端木', '颛孙', '子车', '司寇', '亓官', '鲜于', '锺离', '闾丘', '公良', '段干']


punctuation_mapping:
  zh:
    ',': '，'
    '?': '？'
    '!': '！'
    ':': '：'
    ';': '；'
    '(': '（'
    ')': '）'
    '[': '【'
    ']': '】'
    '"': '“'
    "'": '’'
    '...': '…'
    '--': '—'
    # 根据需要补充
  en:
    '，': ','
    '？': '?'
    '！': '!'
    '：': ':'
    '；': ';'
    '（': '('
    '）': ')'
    '【': '['
    '】': ']'
    '“': '"'
    '’': "'"
    '…': '...'
    '—': '--'

