# util/jiaobiao.py

import re

# 角标字符集
SCRIPT_CHARS = '₀-₉ₐ-ₒₓₔₕₖₗₘₙₚₛₜ₦₧₩₪₫₭₮₯₰₱₲₳₴₵₶₷₸₹₺₻₼₽₾₿'
# 原始角标字符集
SCRIPT_CHARS_ORIGINAL = '₀₁₂₃₄₅₆₇₈₉ₐₑ₂₃₄₅₆₇₈₉ₓₔₕₖₗₘₙₚₛₜ₦₧₩₪₫₭₮₯₰₱₲₳₴₵₶₷₸₹₺₻₼₽₾₿'

"""
常用上标 ⁰ ¹ ² ³ ⁴ ⁵ ⁶ ⁷ ⁸ ⁹ ⁺ ⁻ ⁼ ⁽ ⁾ ⁿ º ˙

常用下标 ₀ ₁ ₂ ₃ ₄ ₅ ₆ ₇ ₈ ₉ ₊ ₋ ₌ ₍ ₎ ₐ ₑ ₒ ₓ ₔ ₕ ₖ ₗ ₘ ₙ ₚ ₛ ₜ

更多上标 ᵃ ᵇ ᶜ ᵈ ᵉ ᵍ ʰ ⁱ ʲ ᵏ ˡ ᵐ ⁿ ᵒ ᵖ ᵒ⃒ ʳ ˢ ᵗ ᵘ ᵛ ʷ ˣ ʸ ᙆ ᴬ ᴮ ᒼ ᴰ ᴱ ᴳ ᴴ ᴵ ᴶ ᴷ ᴸ ᴹ ᴺ ᴼ ᴾ ᴼ̴ ᴿ ˢ ᵀ ᵁ ᵂ ˣ ᵞ ᙆ ꝰ ˀ ˁ ˤ ꟸ ꭜ ʱ ꭝ ꭞ ʴ ʵ ʶ ꭟ ˠ ꟹ ᴭ ᴯ ᴲ ᴻ ᴽ ᵄ ᵅ ᵆ ᵊ ᵋ ᵌ ᵑ ᵓ ᵚ ᵝ ᵞ ᵟ ᵠ ᵡ ᵎ ᵔ ᵕ ᵙ ᵜ ᶛ ᶜ ᶝ ᶞ ᶟ ᶡ ᶣ ᶤ ᶥ ᶦ ᶧ ᶨ ᶩ ᶪ ᶫ ᶬ ᶭ ᶮ ᶯ ᶰ ᶱ ᶲ ᶳ ᶴ ᶵ ᶶ ᶷ ᶸ ᶹ ᶺ ᶼ ᶽ ᶾ ᶿ ꚜ ꚝ ჼ ᒃ ᕻ ᑦ ᒄ ᕪ ᑋ ᑊ ᔿ ᐢ ᣕ ᐤ ᣖ ᣴ ᣗ ᔆ ᙚ ᐡ ᘁ ᐜ ᕽ ᙆ ᙇ ᒼ ᣳ ᒢ ᒻ ᔿ ᐤ ᣖ ᣵ ᙚ ᐪ ᓑ ᘁ ᐜ ᕽ ᙆ ᙇ ⁰ ¹ ² ³ ⁴ ⁵ ⁶ ⁷ ⁸ ⁹ ⁺ ⁻ ⁼ ˂ ˃ ⁽ ⁾ ˙ * º

更多下标 ₐ ₔ ₑ ₕ ᵢ ⱼ ₖ ₗ ₘ ₙ ₒ ₚ ᵣ ₛ ₜ ᵤ ᵥ ₓ ᙮ ᵤ ᵩ ᵦ ₗ ˪ ៳ ៷ ₒ ᵨ ₛ ៴ ᵤ ᵪ ᵧ

中文上标 ㆒㆓㆔㆕㆖㆗㆘㆙㆚㆛㆜㆝㆞㆟

"""

# # ——— 全量上标字符映射 ———
# SUPERSCRIPT_MAP = {
#     # 数字
#     **{str(i): sup for i, sup in enumerate("⁰¹²³⁴⁵⁶⁷⁸⁹")},
#     # 小写字母
#     **dict(zip(
#         "abcdefghijklmnopqrstuvwxyz",
#         "ᵃᵇᶜᵈᵉᶠᵍʰⁱʲᵏˡᵐⁿᵒᵖᑫʳˢᵗᵘᵛʷˣʸᶻ"
#     )),
#     # 注意：部分上角标字符实际上是大写形式，因为Unicode中没有对应的小写形式
#     # 大写字母（支持的子集）
#     **dict(zip(
#         "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
#         "ᴬᴮᒼᴰᴱᶠᴳᴴᴵᴶᴷᴸᴹᴺᴼᴾᴼ̴ᴿˢᵀᵁⱽᵂˣᵞᙆ"
#     )),
#     # 常用符号
#     "+": "⁺", "-": "⁻", "=": "⁼", "(": "⁽", ")": "⁾",
#     ".": "˙", "/": "ˌ"
# }

# # ——— 全量下标字符映射 ———
# SUBSCRIPT_MAP = {
#     # 数字
#     **{str(i): sub for i, sub in enumerate("₀₁₂₃₄₅₆₇₈₉")},
#     # 小写字母
#     **dict(zip(
#         "abcdefghijklmnopqrstuvwxyz",
#         "ₐᵦᶜᵈₑᶠᵍₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥʷₓʸᶻ"
#     )),
#     # 常用符号
#     "+": "₊", "-": "₋", "=": "₌", "(": "₍", ")": "₎", ".": "․", "/": "ʹ"
# }

# # 大写字母映射到对应小写下标
# for c in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
#     SUBSCRIPT_MAP[c] = SUBSCRIPT_MAP.get(c.lower(), c)

# ——— 1. 基础上标：数字 + 小写 + 符号 ———
_digit_sup = {str(i): sup for i, sup in enumerate("⁰¹²³⁴⁵⁶⁷⁸⁹")}
_lower_sup = dict(zip(
    "abcdefghijklmnopqrstuvwxyz",
    "ᵃᵇᶜᵈᵉᶠᵍʰⁱʲᵏˡᵐⁿᵒᵖᑫʳˢᵗᵘᵛʷˣʸᶻ"
))
_symbol_sup = {
    "+": "⁺", "-": "⁻", "=": "⁼", "(": "⁽", ")": "⁾",
    ".": "˙", "/": "ˌ"
}

_base_sup = {}
_base_sup.update(_digit_sup)
_base_sup.update(_lower_sup)
_base_sup.update(_symbol_sup)

# ——— 2. 大写字母退回到小写上标（如果没有就原样） ———
_upper_sup = {c: _base_sup.get(c.lower(), c) for c in "ABCDEFGHIJKLMNOPQRSTUVWXY"}

# ——— 3. 合并成最终的 SUPERSCRIPT_MAP ———
SUPERSCRIPT_MAP = {}
SUPERSCRIPT_MAP.update(_base_sup)
SUPERSCRIPT_MAP.update(_upper_sup)

# ——— 1. 基础下标：数字 + 小写 + 符号 ———
_digit_sub = {str(i): sub for i, sub in enumerate("₀₁₂₃₄₅₆₇₈₉")}
_lower_sub = dict(zip(
    "abcdefghijklmnoprstuvwxyz",
    "ₐᵦᶜᵈₑᶠᵍₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥʷₓʸᶻ"
))
_symbol_sub = {
    "+": "₊", "-": "₋", "=": "₌", "(": "₍", ")": "₎",
    ".": "․", "/": "ʹ"
}

_base_sub = {}
_base_sub.update(_digit_sub)
_base_sub.update(_lower_sub)
_base_sub.update(_symbol_sub)

# ——— 2. 大写字母fallback到小写下标（如果没有就保持原字符） ———
_upper_sub = {c: _base_sub.get(c.lower(), c) for c in "ABCDEFGHIJKLMNOPRSTUVWXY"}

# ——— 3. 合并成最终的 SUBSCRIPT_MAP ———
SUBSCRIPT_MAP = {}
SUBSCRIPT_MAP.update(_base_sub)
SUBSCRIPT_MAP.update(_upper_sub)




# 严格到内容不含 "<" 的上标
SUP_TAG_PATTERN = re.compile(
    r'<g\b[^>]*(?:\bx-sup\b|;sup\b)[^>]*>([^<]+?)</g\b[^>]*>',
    re.IGNORECASE
)
# 同理下标
SUB_TAG_PATTERN = re.compile(
    r'<g\b[^>]*(?:\bx-sub\b|;sub\b)[^>]*>([^<]+?)</g\b[^>]*>',
    re.IGNORECASE
)

def replace_sup_sub(text: str) -> str:
    def _sub(m):
        return ''.join(SUBSCRIPT_MAP.get(ch, ch) for ch in m.group(1))
    def _sup(m):
        return ''.join(SUPERSCRIPT_MAP.get(ch, ch) for ch in m.group(1))

    # 1) **先**把所有下标替换掉
    while SUB_TAG_PATTERN.search(text):
        text = SUB_TAG_PATTERN.sub(_sub, text)
    # 2) 再把所有上标替换掉
    while SUP_TAG_PATTERN.search(text):
        text = SUP_TAG_PATTERN.sub(_sup, text)

    return text

def preprocess_for_translation(src):
    """
    接受字符串或字符串列表，统一做上/下标替换。
    """
    if isinstance(src, list):
        return [replace_sup_sub(s) for s in src]
    return replace_sup_sub(src)

if __name__ == '__main__':
    # ——— 自检示例 ———
    samples = [
        'E = mc<g id="1" x-sup="true">2</g>',
        'H<g id="2" x-sub="true">2</g>O',
        'Author<g id="3" x-sup="true">†</g> wrote',
        'x<g x-sup="true">n</g> + y<g x-sup="true">n</g> = (x+y)<g x-sup="true">n</g>',
        '10<g x-sup="true">-3</g> m',
        'H<g x-sub="true">2</g>SO<g x-sub="true">4</g>',
        'CO<g x-sub="true">2</g>',
        'p<g x-sup="true">i</g><g x-sub="true">j</g>',
        'α<g x-sup="true">2</g>',
        'θ<g x-sub="true">0</g>',
        '10<g x-sup="true">-6</g> m',
        'Phase<g x-sup="true">II</g>',
        'f(x)<g x-sup="true">′′</g>',
        'm<g x-sub="true">max</g>',
        'X<g x-sup="true">+</g>Y<g x-sub="true">-</g>Z<g x-sup="true">%</g>',
        # 1) 作者注脚（多属性、嵌套）  
        "…Smith, J.<g id=\"a1\" type=\"xref\" text=\"tag property=&quot;x-link;sup;color:123456;&quot;\">a</g type=\"xref\" text=\"/tag\"><g data-extra=\"foo\" type=\"note\" text=\"tag property=&quot;x-sup;&quot;\">*<g id=\"x\" type=\"inner\" text=\"tag property=&quot;x-link;color:654321;&quot;\">†</g type=\"inner\" text=\"/tag\"></g type=\"note\" text=\"/tag\">, Doe, A.",

        # 2) 化学式下标  
        "H<g id=\"sub1\" type=\"chem\" text=\"tag property=&quot;x-sub;color:00FF00;&quot;\">2</g>O and Na<g type=\"chem2\" data-tag=\"xyz\" text=\"tag property=&quot;x-sub;&quot;\">+</g>Cl<g type=\"chem3\" text=\"tag property=&quot;x-sub;&quot;\">–</g>",

        # 3) 数学公式上下标混合  
        "F = G·m<g id=\"sup1\" x-sup=\"true\">1</g>·m<g id=\"sup2\" x-sup=\"true\">2</g>/r<g id=\"sub2\" x-sub=\"true\">2</g>",

        # 4) 希腊字母上标  
        "Δ<g type=\"greek\" text=\"tag property=&quot;x-sup;color:FF0000;&quot;\">θ</g>, α<g x-sup=\"true\">′</g>, β<g data-ref=\"b2\" text=\"tag property=&quot;x-sup;&quot;\">″</g>",

        # 5) 同位素标注  
        "U<g id=\"iso235\" type=\"sup\" text=\"tag property=&quot;x-link;sup;color:ABCDEF;&quot;\">235</g>U<g id=\"iso238\" type=\"sup2\" text=\"tag property=&quot;x-sup;&quot;\">238</g>",

        # 6) 脱字符号上标  
        "pH = 7<g type=\"ph\" text=\"tag property=&quot;x-sup;&quot;\">.4</g>",

        # 7) 序数词上标  
        "Chapter 3<g id=\"ord\" type=\"ordtag\" text=\"tag property=&quot;x-sup;color:000000;&quot;\">rd</g> Edition",

        # 8) 复杂属性顺序  
        "Sample<g data-id=\"123\" type=\"foo\" text=\"tag property=&quot;color:00FF00;x-sup;&quot;\">A</g> vs. Control",

        # 9) 连续上下标  
        "X<g x-sup=\"true\">i</g><g x-sub=\"true\">j</g><g id=\"t\" type=\"tag\" text=\"tag property=&quot;x-sup;&quot;\">k</g>",

        # 10) 百分号上标  
        "Yield = 85<g type=\"pct\" text=\"tag property=&quot;x-sup;&quot;\">%</g>",

        # 11) 带单引号属性  
        "Value<g id='t1' type='tag1' text='tag property=&quot;x-sub;color:FF00FF;&quot;'>n</g>",

        # 12) 下标 + 前后混杂标签  
        "Formula: C<g type=\"sub\" text=\"tag property=&quot;x-sub;&quot;\">6</g>H<g type=\"sub\" text=\"tag property=&quot;x-sub;&quot;\">12</g>O<g type=\"sub\" text=\"tag property=&quot;x-sub;&quot;\">6</g>",

        # 13) 多重属性位置变化  
        "Note<g text=\"foo\" type=\"bar\" data-foo=\"x\" text=\"tag property=&quot;x-sup;&quot;\">*</g>",

        # 14) 参数下标  
        "θ<g id=\"0\" type=\"param\" text=\"tag property=&quot;x-sub;color:000000;&quot;\">0</g> = 0",

        #   15) 化学电荷上标  
        "Na<g x-sup=\"true\">+</g>, SO<g x-sup=\"true\">2−</g>",
        # 原始示例
        'There was   a severe decline in the number of MBS cases, as was demon-strated by the last Task Force Estimate [<g id="2" type="tag2" text="tag property=&quot;x-link;color:007FAC;&quot;">5</g>].',

        # 引文带超链接格式
        'In March 2020, the COVID-19 pandemic stopped all elective surgeries across the United States [<g id="1" type="tag1" text="tag property=&quot;x-link;color:007FAC;&quot;">4</g>].',

        # 文本中混合了 <ex> 和 <g>
        '1 <ex id="1" type="tag1" text="/tag"/> and <g id="2" type="tag2" text="tag property=&quot;x-link;color:007FAC;&quot;">2</g> and <g id="3" type="tag3" text="tag property=&quot;x-link;color:007FAC;&quot;">Tables 1</g> and <g id="4" type="tag4" text="tag property=&quot;x-link;color:007FAC;&quot;">2</g>.',

        # “bx” 空元素标签与后续文字粘连
        'The overall estimated number of metabolic and bariatric procedures for 2022 … is listed in <bx id="1" type="tag1" text="tag property=&quot;x-link;color:007FAC;&quot;"/>Figs.',

        # 表格引用
        '.7%, and OAGB .5% (<g id="1" type="tag1" text="tag property=&quot;x-link;color:007FAC;&quot;">Table 3</g>).',

        # 多标签连续出现
        'Prevalence of obesity<ex id="1" type="tag1" text="/tag"/><bx id="2" type="tag2" text="tag property=&quot;x-link;color:007FAC;&quot;"/> and severe obesity among adults…',

        # URL 拆成多段 <g> + <x> + <g>
        '<g id="1" type="tag1" text="tag property=&quot;x-link;color:007FAC;&quot;">https://milkeninstitute.org/sites/default/files/reports-pdf/Mi-Americas-</g><x id="2" type="tag2" text="tag property=&quot;x&quot;"/><g id="4" type="tag4" text="tag property=&quot;x-link;color:007FAC;&quot;">Obesity-Crisis-WEB.pdf</g><g id="5" type="tag5" text="tag">.</g>',

        # 数学式里的上/下标
        'Equation: E = mc<g x-sup="true">2</g>,  H<g x-sub="true">2</g>O and a<g x-sup="true">‡</g>.',

        # 嵌套在其他容器里的上标
        '<bx id="1" x-sub="true">H<g x-sub="true">2</g>O solution</bx> is stable.',

        # 多个不同标签类型混用
        'Value: A<g x-sup="true">+</g>B<g x-sub="true">−</g>C<g x-sup="true">=</g>D, see <span>note</span><g x-sup="true">1</g>.',

        # 嵌套 <em> 在 <g> 里面（脚本不应删除 <em>）
        'Nested: <g x-sup="true"><em>2</em></g> should keep <em> intact.',

        # 含 HTML 实体的例子
        'Threshold &lt; 5% <g x-sup="true">2</g> and &amp; other symbols.',

        # 带有多余属性的 <g>
        '<g id="10" data-info="foo" property="x-sup;bar">3</g> times.',

        # 国际化度量单位
        '体温为 37<g x-sup="true">℃</g>，血压 120<g x-sub="true">/80</g> mmHg。',
        (
            '在实验中，测得电容 C<g id="1" type="tag1" text="tag property=&quot;x-sub;&quot;">1</g type="tag1" text="/tag"> 的电压 V<g id="2" type="tag2" text="tag property=&quot;x-sup;&quot;">2</g type="tag2" text="/tag">，'
            '详见文献 <g id="LNK" type="tagLink" text="tag property=&quot;x-link;color:007FAC;&quot;">[1]'
            '<g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag">'
            '</g type="tagLink" text="/tag">；'
            '另附表<bx id="BX1" type="tagBox" text="tag property=&quot;x-link;color:FF00FF;&quot;"/>A 和脚注<ex id="EX1" type="tagEx" text="/tag"/>²；'
            '同时在<mrk id="M1" mtype="emph"/>深度测试中出现嵌套<g id="3" type="tag3" text="tag property=&quot;x-sup;&quot;">'
            '<g id="4" type="tag4" text="tag property=&quot;x-sub;&quot;">3</g type="tag4" text="/tag">'
            '</g type="tag3" text="/tag">，并且保留<span>普通标签</span>；'
            '此外还有分隔符<x id="X1" type="tagX" text="tag property=&quot;x&quot;"/>和实体&lt; &amp; &quot; &gt;。'
        ),



    ]
    for s in samples:
        print(f'原文: {s}')
        print(f'替换后: {replace_sup_sub(s)}')
        print('-' * 80)
