"""
纯后处理中文姓名规范化

核心函数
---------
normalize_names(src: str, tgt: str) -> str
    给定一对 “中文原句 + 英文译句”，
    返回已将所有中文姓名修正为 ‘Given Surname’ 的英文句。

依赖
-----
pip install pypinyin
"""
from __future__ import annotations
import re
from functools import lru_cache
from typing import List, Tuple

from pypinyin import lazy_pinyin, Style

# ----------------------------------------------------------------------
# 1. 姓氏词表  —— 常见 560 单姓 + 60 复姓；可继续补充
# ----------------------------------------------------------------------
_SINGLE = (
    "赵钱孙李周吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金魏"
    "陶姜戚谢邹喻柏水窦章云苏潘葛奚范彭郎鲁韦昌马苗凤花方俞任袁"
    "柳鲍史唐费廉岑薛雷贺倪汤滕殷罗毕郝邬安常乐于时傅皮卞齐康伍"
    "余元卜顾孟平黄和穆萧尹姚邵湛汪祁毛禹狄米贝明臧计伏成戴谈宋"
    "茅庞熊纪舒屈项祝董梁杜阮蓝闵席季麻强贾路娄危江童颜郭梅盛林"
    "刁钟徐邱骆高夏蔡田樊胡凌霍虞万支柯昝管卢莫经房裘缪解干应宗"
    "丁宣贠饶禚郦雍隗璩桑桂濮牛寿通买杭池竺嵇邢佟"
)
_COMPOUND = [
    "欧阳", "司马", "上官", "夏侯", "诸葛", "东方", "皇甫", "尉迟", "长孙",
    "慕容", "司徒", "司空", "令狐", "赫连", "澹台", "公冶", "宗政", "鲜于",
    "闾丘", "东郭", "南宫", "百里", "羊舌", "呼延", "谷梁", "梁丘", "左丘",
    "东门", "西门", "南门", "北堂", "端木", "亓官", "第五", "公孙", "仲孙",
]

# 编译匹配中文姓名的正则（复姓优先）
_CN_CHAR = r"[\u4e00-\u9fff]"
PAT_COMPOUND = rf"(?:{'|'.join(_COMPOUND)}){_CN_CHAR}{{1,2}}"
PAT_SINGLE = rf"[{_SINGLE}]{_CN_CHAR}{{1,2}}"
RE_CNAME = re.compile(rf"(?:{PAT_COMPOUND})|(?:{PAT_SINGLE})")

# ---------------- 2. 中文 → 拼音 ----------------
@lru_cache(maxsize=4096)
def _safe_pinyin(ch: str) -> str:
    py = lazy_pinyin(ch, strict=False, errors="ignore")
    if py:
        return py[0]
    return f"U+{ord(ch):x}"             # ### FIX ###

_SUR_CACHE = {}
def cn2en(cn: str) -> str:
    if cn[:2] in _COMPOUND:
        sur, giv = cn[:2], cn[2:]
    else:
        sur, giv = cn[0], cn[1:]

    if sur not in _SUR_CACHE:
        _SUR_CACHE[sur] = "".join(lazy_pinyin(sur)).capitalize()
    sur_py = _SUR_CACHE[sur]
    giv_py = "".join(_safe_pinyin(c) for c in giv).capitalize()
    return f"{giv_py} {sur_py}"


# ---------------- 3. 译文匹配模板 ----------------
def _name_patterns(sur_py: str) -> List[re.Pattern]:
    W = r"[A-Z][A-Za-z'‑-]{1,20}"
    patts = [
        rf"\b{sur_py}\s+{W}\b",   # Surname Given
        rf"\b{W}\s+{sur_py}\b",   # Given Surname
        rf"\b{sur_py}[,，]\s*{W}\b",
        rf"\b{W}[,，]\s*{sur_py}\b",
        rf"\b{sur_py}\b",
    ]
    return [re.compile(p, flags=re.I) for p in patts]

# ---------------- 4. 去重 ----------------
_DUP_A = re.compile(r"\b([A-Z][a-z]{1,20})\s+([A-Z][a-z]{1,20})\s+\1\b", re.I)
#           └── A  B  A   → 保留 “A B”
_DUP_B = re.compile(r"\b([A-Z][a-z]{1,20})\s+\1\b", re.I)
#           └── A  A      → 保留 “A”

# 每个正则配对自己的替换串，避免再次出错
_DEDUP_RULES: list[tuple[re.Pattern, str]] = [
    (_DUP_A, r"\1 \2"),   # 两组 → 用 \1 \2
    (_DUP_B, r"\1"),      # 一组 → 用 \1
]

def _dedup(txt: str) -> str:
    for pat, repl in _DEDUP_RULES:
        txt = pat.sub(repl, txt)
    return txt


# ---------------- 5. 把一个姓名替进句子 ----------------
def _replace_or_insert(tgt: str, eng_std: str, sur_py: str) -> str:
    # 5.1 先尝试多种 pattern 替换
    for p in _name_patterns(sur_py):
        tgt, n = p.subn(eng_std, tgt, count=1)
        if n:
            return tgt

    # 5.2 若未命中：找 **第一个大写开头词组** 或句尾
    m = re.search(r"\b[A-Z][A-Za-z'‑-]{1,20}\b", tgt)
    if m:
        tgt = tgt[:m.start()] + eng_std + tgt[m.end():]
    else:
        tgt = tgt + f" ({eng_std})"
    return tgt


# ---------------- 6. 主外部接口 ----------------
CN_RE = re.compile(r"[\u4e00-\u9fff]+")

def normalize_names(src: str, tgt: str) -> str:
    cn_names = [m.group(0) for m in RE_CNAME.finditer(src)]
    if not cn_names:
        return tgt

    out = tgt
    for cn in cn_names:
        eng_std = cn2en(cn)
        sur_py = eng_std.split()[-1]
        out = _replace_or_insert(out, eng_std, sur_py)

    # 剩余中文直接删掉（如 “张” 残留）
    out = CN_RE.sub("", out)

    return _dedup(out).strip()